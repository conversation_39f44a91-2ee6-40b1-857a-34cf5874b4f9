import argparse
import time
import numpy as np
import networkx as nx
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
import dgl
from matplotlib import pyplot
from sklearn.preprocessing import MinMaxScaler
from sklearn.model_selection import train_test_split
from dgl import DGLGraph
from dgl.data import register_data_args, load_data
from dgl.nn.pytorch.conv import SAGEConv
from dgl.nn.pytorch.conv import GATConv
import dgl.function as fn
from dgl.data import CoraGraphDataset, CiteseerGraphDataset, PubmedGraphDataset
import torch.utils.data as Data
from dgl.dataloading import GraphDataLoader
from dgl.data import MiniGCDataset
import copy

from utils import EarlyStopping
import os

class MLPPredictor(nn.Module):
    def __init__(self, num_layers_linear,in_features, edge_features,num_hidden_linear, num_classes,activation):
        super().__init__()
        self.gat_layers = nn.ModuleList()
        self.activation = activation
        self.num_layers_linear = num_layers_linear
        if num_layers_linear ==1:
            self.gat_layers.append(torch.nn.Linear(in_features * 2 + edge_features,num_classes))
        elif num_layers_linear == 2:
            self.gat_layers.append(torch.nn.Linear(in_features * 2+ edge_features, num_hidden_linear))
            self.gat_layers.append(torch.nn.Linear(num_hidden_linear, num_classes))
        else:
            self.gat_layers.append(torch.nn.Linear(in_features * 2+ edge_features, num_hidden_linear))
            for l in range(1, num_layers_linear - 1):
                self.gat_layers.append(torch.nn.Linear(num_hidden_linear, num_hidden_linear))
            self.gat_layers.append(torch.nn.Linear(num_hidden_linear, num_classes))
        # self.W = nn.Linear(in_features * 2, num_classes)

    def apply_edges(self, edges):
        h_u = edges.src['h']
        h_v = edges.dst['h']
        h_e = edges.data["efeat"]
        h = torch.cat([h_u, h_v, h_e],1)
        for l in range(0, self.num_layers_linear-1):
            h = self.activation(self.gat_layers[l](h))
        score = self.activation(self.gat_layers[-1](h))
        # score = self.W(torch.cat([h_u, h_v], 1))
        return {'score': score}

    def forward(self, graph, h):
        # h是从5.1节的GNN模型中计算出的节点表示
        with graph.local_scope():
            graph.ndata['h'] = h
            graph.apply_edges(self.apply_edges)
            return graph.edata['score']
class GAT(nn.Module):
    def __init__(self,
                 num_layers,
                 num_layers_linear,
                 in_dim,
                 num_hidden,
                 num_hidden_linear,
                 num_classes,
                 heads,
                 activation_GNN,
                 activation_NN,
                 feat_drop,
                 attn_drop,
                 negative_slope,
                 residual):
        super(GAT, self).__init__()
        self.num_layers = num_layers
        self.num_layers_linear = num_layers_linear
        self.gat_layers = nn.ModuleList()
        self.activation_GNN = activation_GNN
        self.activation_NN = activation_NN
        # input projection (no residual)
        self.gat_layers.append(GATConv(
            in_dim, num_hidden, heads[0],
            feat_drop, attn_drop, negative_slope, False, self.activation_GNN))
        # hidden layers
        for l in range(1, num_layers):
            # due to multi-head, the in_dim = num_hidden * num_heads
            self.gat_layers.append(GATConv(
                num_hidden * heads[l - 1], num_hidden, heads[l],
                feat_drop, attn_drop, negative_slope, residual, self.activation_GNN))
        # output projection
        self.gat_layers.append(GATConv(
            num_hidden * heads[-2], num_hidden, heads[-1],
            feat_drop, attn_drop, negative_slope, residual, None))
        self.pred = MLPPredictor(num_layers_linear,num_hidden,in_dim,num_hidden_linear,num_classes,self.activation_NN)
        # regression
        # for l in range(0, num_layers_linear-1):
        #     self.gat_layers.append(torch.nn.Linear(num_hidden*heads[-1],num_hidden_linear))
        # if num_layers_linear ==1:
        #     self.gat_layers.append(torch.nn.Linear(num_hidden*heads[-1],num_classes))
        # elif num_layers_linear == 2:
        #     self.gat_layers.append(torch.nn.Linear(num_hidden * heads[-1], num_hidden_linear))
        #     self.gat_layers.append(torch.nn.Linear(num_hidden_linear, num_classes))
        # else:
        #     self.gat_layers.append(torch.nn.Linear(num_hidden * heads[-1], num_hidden_linear))
        #     for l in range(1, num_layers_linear - 1):
        #         self.gat_layers.append(torch.nn.Linear(num_hidden_linear, num_hidden_linear))
        #     self.gat_layers.append(torch.nn.Linear(num_hidden_linear, num_classes))

    def forward(self, g, inputs):
        h = inputs
        for l in range(self.num_layers):
            h = self.gat_layers[l](g, h).flatten(1)
        # output projection
        logits = self.gat_layers[self.num_layers](g, h).mean(1)
        h=logits
        return self.pred(g,h)

def accuracy(logits, labels):
    _, indices = torch.max(logits, dim=1)
    correct = torch.sum(indices == labels)
    return correct.item() * 1.0 / len(labels)


def evaluate(model, features, labels, mask):
    model.eval()
    with torch.no_grad():
        logits = model(features)
        logits = logits[mask]
        labels = labels[mask]
        return accuracy(logits, labels)
def plot_scatter(measure,prediction):
    area = np.pi * 2 ** 2  # 点面积
    # 画散点图
    measure = measure.cpu().detach().numpy()
    prediction = prediction.cpu().detach().numpy()
    axs2 = pyplot.subplot(111, facecolor='none')  # 在图标1中创建子图
    axs2.scatter(measure, prediction, s=area, c='#DC143C', alpha=0.4,
                 linewidth=6.0)
    if np.min(prediction.ravel()[np.flatnonzero(prediction)])<np.min(measure.ravel()[np.flatnonzero(measure)]):
        xmin = np.min(prediction.ravel()[np.flatnonzero(prediction)])-1
    else:
        xmin = np.min(measure.ravel()[np.flatnonzero(measure)])-1
    if np.max(prediction.ravel()[np.flatnonzero(prediction)])>np.max(measure.ravel()[np.flatnonzero(measure)]):
        xmax = np.max(prediction.ravel()[np.flatnonzero(prediction)])+1
    else:
        xmax = np.max(measure.ravel()[np.flatnonzero(measure)])+1
    axs2.plot([xmin, xmax], [xmin, xmax], color='#000000', linewidth=3.0)
    pyplot.tick_params(labelsize=15)
    pyplot.xlim(xmax=xmax, xmin=xmin)
    pyplot.ylim(ymax=xmax, ymin=xmin)
    pyplot.xlabel('measured SNR')
    pyplot.ylabel('predicted SNR')
    pyplot.grid()
    pyplot.show()
    return
# def findone(data):
#     n=0
#     for j in range(np.size(data, 0)):
#         for k in range(np.size(data, 1)):
#             n = n + len(data[j,k].nonzero())
#     print(n)

def MaxMinSclar(data,single=False,*wavelength):
    if single:#对于单个矩阵
        max_value = np.max(data)
        min_value = np.min(data)
        return (data-min_value)/(max_value-min_value)
    else:#对于复合矩阵（矩阵中的元素为矩阵）
        min_W = 0
        max_W = 0
        min_P = 0
        max_P = 0
        for i in range(np.size(data,0)):#找到每个部分的最大值和最小值
            a = np.max(data[i,0])
            b = np.min(data[i,0])
            if a>max_P:
                max_P = a
            if b<min_P:
                min_P=b
            for j in range(np.size(data[i,2],0)):
                for k in range(np.size(data[i,2],1)):
                    data[i, 2][j, k] = data[i, 2][j, k]*wavelength#将波长信息加入
                    a = np.max(data[i, 2][j,k])
                    b = np.min(data[i, 2][j,k])
                    if a > max_W:
                        max_W = a
                    if b < min_W:
                        min_W = b
        for i in range(np.size(data,0)):#最大最小归一化处理
            data[i,0] = (data[i,0]-min_P)/(max_P-min_P)
            for j in range(np.size(data[i,2],0)):
                for k in range(np.size(data[i,2],1)):
                    data[i, 2][j,k] = (data[i, 2][j,k] - min_W) / (max_W - min_W)
        return data


channel_num=80
wavelength_start = 191.3e12
wavelength_spacing = 50e9
wavelength = np.zeros((1,channel_num))
for i in range(channel_num):
    wavelength[0,i] = wavelength_start + i * wavelength_spacing
data = np.load('g_data_Jnet_edgeFeat_GN.npy' , allow_pickle=True)
route = np.load("route.npy", allow_pickle=True)
wavelength_allo = np.load("SelectWave.npy", allow_pickle=True)
data = data.tolist()

batch_size = 64
train_size = 0.8
data_train, data_test = train_test_split(data, train_size=train_size,random_state=8)

loader = GraphDataLoader(dataset=data_train,batch_size=batch_size,shuffle=True)


###############模型超参数
num_heads = 3
num_out_heads=1
num_layers = 2
num_layers_linear =3
heads = ([num_heads] * num_layers) + [num_out_heads]
in_feats = 161
num_hidden = 64
num_hidden_linear = 128
num_out = 80
in_drop = 0
attn_drop = 0
negative_slope = 0
weight_decay = 1e-5
lr = 1e-3
residual = True
epoch_num = 2000
model = GAT(
            num_layers,
            num_layers_linear,
            in_feats,
            num_hidden,
            num_hidden_linear,
            num_out,
            heads,
            F.elu,
            F.elu,
            in_drop,
            attn_drop,
            negative_slope,
            residual)
print(model)
loss_fcn = torch.nn.MSELoss()
optimizer = torch.optim.Adam(
    model.parameters(), lr=lr, weight_decay=weight_decay)
dur = []
# ################################时间消耗####################
# lightpath_number = [50,100,200,300,400]
# time_list = []
# model.load_state_dict(torch.load("GNN_Jnet_model_GN.pth"))
# for i in range(len(lightpath_number)):
#     graph = data[lightpath_number[i]-1][0]
#     labels = data[lightpath_number[i]-1][1]
#     features = graph.ndata["nfeat"]
#     time_start = time.time()
#     logits = model(graph, features)
#     tim_end = time.time()
#     time_list.append(tim_end-time_start)
# np.savetxt("time_list.csv",time_list)
########################散点图数据###############################################################
model.load_state_dict(torch.load("GNN_Jnet_model_GN1.pth"))
loss_fcn = torch.nn.MSELoss()
test_loader = GraphDataLoader(dataset=data_test,batch_size=len(data_test),shuffle=True)
for batched_graph, labels in test_loader:
    # forward
    features = batched_graph.ndata["nfeat"]
    logits = model(batched_graph, features)
    labels = labels.view(labels.shape[0] * labels.shape[1], labels.shape[2])
    for z in range(logits.shape[0]):
        for j in range(logits.shape[1]):
            if float(logits[z][j]) < 5:
                logits[z][j] = 0
    plot_scatter(logits.view(logits.shape[0]*logits.shape[1]),labels.view(labels.shape[0]*labels.shape[1]))
    kwargs = dict(histtype='stepfilled', alpha=0.8, density=True, bins=40)
    pyplot.hist((logits.view(logits.shape[0]*logits.shape[1])-labels.view(labels.shape[0]*labels.shape[1])).detach().numpy(), **kwargs)
    pyplot.grid()
    pyplot.show()
    # predictor = logits.view(logits.shape[0]*logits.shape[1]).detach().numpy()
    # measure = labels.view(labels.shape[0]*labels.shape[1]).detach().numpy()
    # error = predictor-measure
    predictor = logits.view(logits.shape[0] * logits.shape[1]).detach().numpy()
    measure = labels.view(labels.shape[0] * labels.shape[1]).detach().numpy()
    error = predictor - measure
    np.savetxt("Estimation Error.csv", error)
    np.savetxt("predictor.csv",predictor)
    np.savetxt("measure.csv", measure)
    within_05db = np.sum(np.abs(error) < 0.5) / len(error)
    print(f"误差 < 0.5dB 的占比为: {within_05db:.4%}")
    print(torch.max(torch.max(torch.abs(logits-labels))))
    loss = loss_fcn(logits, labels)
print(loss)
##############################新部署光路对已部署光路的影响####################################
# lightpath_number = [50,100,150,200,250,300]
# SNR_list = []#被测光路的估计SNR值
# measured_SNR = []#部署新光路后被测光路的SNR值
# route_test = route[49]
# SelectWave = wavelength_allo[49]
# model.load_state_dict(torch.load("GNN_Jnet_model_GN.pth"))
# for i in range(len(lightpath_number)):
#     graph = data[lightpath_number[i]-1][0]
#     labels = data[lightpath_number[i]-1][1]
#     features = graph.ndata["nfeat"]
#     logits = model(graph, features)
#     # labels = labels.view(labels.shape[0] * labels.shape[1], labels.shape[2])
#     SNR_list.append(logits[graph.edge_ids(route_test[-2]-1, route_test[-1]-1),SelectWave])
#     measured_SNR.append(labels[graph.edge_ids(route_test[-2]-1, route_test[-1]-1),SelectWave])
# np.savetxt("Predicted_SNR_list.csv",SNR_list)
# np.savetxt("Measured_SNR.csv",measured_SNR)



# test_loader = GraphDataLoader(dataset=data_test,batch_size=len(data_test),shuffle=True)
# for batched_graph, labels in test_loader:
#     features = batched_graph.ndata["nfeat"]
#     logits = model(batched_graph, features)
#     labels = labels.view(labels.shape[0] * labels.shape[1], labels.shape[2])
#     for z in range(logits.shape[0]):
#         for j in range(logits.shape[1]):
#             if float(logits[z][j]) < 5:
#                 logits[z][j] = 0
#     error = (logits.view(logits.shape[0]*logits.shape[1])-labels.view(labels.shape[0]*labels.shape[1])).detach().numpy()
#     np.savetxt("Estimation Error.csv",error)
#     break



