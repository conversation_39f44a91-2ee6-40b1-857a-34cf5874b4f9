import numpy as np
import pandas as pd
import torch
import dgl
from sklearn.preprocessing import MinMaxScaler
import copy
# 归一化函数
def MaxMinSclar(data,single=False,*wavelength):
    if single:#对于单个矩阵
        max_value = np.max(data)
        min_value = np.min(data)
        return (data-min_value)/(max_value-min_value)
    else:#对于复合矩阵（矩阵中的元素为矩阵）
        min_W = 0
        max_W = 0
        min_P = 0
        max_P = 0
        min_SNR = 0
        max_SNR = 0
        delt = []
        for i in range(np.size(data, 0)):  # 找到每个部分的最大值和最小值
            for j in range(np.size(data[i, 1], 0)):
                for k in range(np.size(data[i, 1], 1)):
                    a = np.max(data[i, 1][j, k])
                    if a == np.inf:
                        delt.append(i)
        delt = set(delt)
        delt = list(delt)
        data = np.delete(data,delt,axis=0)
        # for i in range(len(delt)):
        #     data = np.delete(data, delt[i], 0)
        for i in range(np.size(data,0)):#找到每个部分的最大值和最小值
            for j in range(np.size(data[i, 0], 0)):
                for k in range(np.size(data[i, 0], 1)):
                    a = np.max(data[i, 0][j, k])
                    b = np.min(data[i, 0][j, k])
                    if a > max_P:
                        max_P = a
                    if b < min_P:
                        min_P = b
            for j in range(np.size(data[i, 1], 0)):
                for k in range(np.size(data[i, 1], 1)):
                    a = np.max(data[i, 1][j, k])
                    b = np.min(data[i, 1][j, k])
                    # if a == np.inf:
                    #     data = np.delete(data, i,0)
                    #     continue
                    if a > max_SNR:
                        max_SNR = a
                    if b < min_SNR:
                        min_SNR = b
            for j in range(np.size(data[i,2],0)):
                for k in range(np.size(data[i,2],1)):
                    data[i, 2][j, k] = data[i, 2][j, k]*wavelength#将波长信息加入
                    a = np.max(data[i, 2][j,k])
                    b = np.min(data[i, 2][j,k])
                    if a > max_W:
                        max_W = a
                    if b < min_W:
                        min_W = b
        for i in range(np.size(data,0)):#最大最小归一化处理
            for j in range(np.size(data[i, 0], 0)):
                for k in range(np.size(data[i, 0], 1)):
                    data[i, 0][j, k] = (data[i, 0][j, k] - min_P) / (max_P - min_P)
            for j in range(np.size(data[i,2],0)):
                for k in range(np.size(data[i,2],1)):
                    data[i, 2][j,k] = (data[i, 2][j,k] - min_W) / (max_W - min_W)
            # for j in range(np.size(data[i,1],0)):
            #     for k in range(np.size(data[i,1],1)):
            #         data[i, 1][j,k] = (data[i, 1][j,k] - min_SNR) / (max_SNR - min_SNR)
        return data


# 1. 加载原始数据
channel_num = 80
wavelength_start = 191.3e12
wavelength_spacing = 50e9
wavelength = np.zeros((1, channel_num))
data_inter = []
data_num = 15
for i in range(channel_num):
    wavelength[0, i] = wavelength_start + i * wavelength_spacing
for i in range(data_num):
    data_inter.append(np.load(f"Jnet_{i}.npy", allow_pickle=True))

# 2. 数据预处理
data = np.vstack([data_inter[i][0:550] for i in range(data_num)])
data_sclar = MaxMinSclar(data, True, *wavelength)
# 定义 link_sclar
link_length = np.array([[0,160,240,0,0,0,0,0,0,0,0,0,0,0],
                        [160,0,0,240,0,0,0,0,0,0,0,0,0,0],
                        [240,0,0,0,240,0,0,0,0,0,0,0,0,0],
                        [0,240,0,0,80,40,0,0,0,0,0,0,0,0],
                        [0,0,240,80,0,40,80,0,240,0,0,0,0,0],
                        [0,0,0,40,40,0,0,160,0,0,0,0,0,0],
                        [0,0,0,0,80,0,0,80,0,0,0,240,0,0],
                        [0,0,0,0,0,160,80,0,0,160,0,0,0,0],
                        [0,0,0,0,240,0,0,0,0,0,0,240,0,240],
                        [0,0,0,0,0,0,0,160,0,0,40,40,0,0],
                        [0,0,0,0,0,0,0,0,0,40,0,40,320,0],
                        [0,0,0,0,0,0,240,0,240,40,40,0,320,240],
                        [0,0,0,0,0,0,0,0,0,0,320,320,0,160],
                        [0,0,0,0,0,0,0,0,240,0,0,240,160,0]])

link_sclar = MaxMinSclar(link_length, True)  # 归一化链路长度

# 构建图数据
u, v = torch.tensor([0, 0, 1, 2, 3, 3, 4, 4, 4, 5, 6, 6, 7, 8, 11, 9, 8, 13, 11, 11, 13, 10]), torch.tensor([1, 2, 3, 4, 4, 5, 5, 8, 6, 7, 7, 11, 9, 11, 9, 10, 13, 11, 10, 12, 12, 12])
g = dgl.graph((u, v))
g = g.int()
g = dgl.to_bidirected(g)

g_data = []
for i in range(len(data_sclar)):
    g1 = copy.deepcopy(g)
    e_feat = torch.zeros((g1.num_edges(), channel_num * 3))  # 边特征
    e_labels = torch.zeros((g1.num_edges(), channel_num))  # 边标签
    for j in range(len(data_sclar[i, 2])):
        for k in range(len(data_sclar[i, 2])):
            if g1.has_edges_between(j, k):
                e_feat[g1.edge_ids(j, k), 0:channel_num] = torch.from_numpy(data_sclar[i, 2][j, k])  # 波长分配
                e_feat[g1.edge_ids(j, k), channel_num:2 * channel_num] = torch.from_numpy(data_sclar[i, 0][j, k])  # 功率
                e_labels[g1.edge_ids(j, k), 0:channel_num] = torch.from_numpy(data_sclar[i, 1][j, k])  # 标签
                link_len = torch.zeros(channel_num)
                link_len[torch.nonzero(e_feat[g1.edge_ids(j, k), 0:channel_num])] = link_sclar[j, k]
                e_feat[g1.edge_ids(j, k), 2 * channel_num:3 * channel_num] = link_len  # 链路长度
    g1.edata["efeat"] = e_feat
    g1.update_all(dgl.function.copy_e('efeat', 'm'), dgl.function.sum('m', 'nfeat'))
    g_data.append((g1, e_labels))

# 4. 保存图数据
torch.save(g_data, "g_data_Jnet.pt")
