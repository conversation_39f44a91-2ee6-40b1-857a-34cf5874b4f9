import time
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import dgl
import dgl.function as fn
from dgl.nn.pytorch.conv import GATConv
from dgl.dataloading import GraphDataLoader
from sklearn.model_selection import train_test_split

# 1. 定义 GAT 模型
class GAT(nn.Module):
    def __init__(self, num_layers, num_layers_linear, in_dim, num_hidden, num_hidden_linear, num_classes, heads, activation, feat_drop, attn_drop, negative_slope, residual):
        super(GAT, self).__init__()
        self.num_layers = num_layers
        self.num_layers_linear = num_layers_linear
        self.gat_layers = nn.ModuleList()
        self.activation = activation

        # 输入层（无残差连接）
        self.gat_layers.append(GATConv(
            in_dim, num_hidden, heads[0],
            feat_drop, attn_drop, negative_slope, False, self.activation))

        # 隐藏层
        for l in range(1, num_layers):
            self.gat_layers.append(GATConv(
                num_hidden * heads[l - 1], num_hidden, heads[l],
                feat_drop, attn_drop, negative_slope, residual, self.activation))

        # 输出层
        self.gat_layers.append(GATConv(
            num_hidden * heads[-2], num_hidden, heads[-1],
            feat_drop, attn_drop, negative_slope, residual, None))

        # 回归层
        if num_layers_linear == 1:
            self.gat_layers.append(nn.Linear(273, num_classes))
        elif num_layers_linear == 2:
            self.gat_layers.append(nn.Linear(273, num_hidden_linear))
            self.gat_layers.append(nn.Linear(num_hidden_linear, num_classes))
        else:
            self.gat_layers.append(nn.Linear(273, num_hidden_linear))
            for l in range(1, num_layers_linear - 1):
                self.gat_layers.append(nn.Linear(num_hidden_linear, num_hidden_linear))
            self.gat_layers.append(nn.Linear(num_hidden_linear, num_classes))

    def forward(self, g, inputs):
        h = inputs
        for l in range(self.num_layers):
            h = self.gat_layers[l](g, h).flatten(1)

        g.ndata['h'] = h

        # 这里 apply_edges 是自己定义 message passing，把两端节点特征拼接
        g.apply_edges(fn.u_add_v('h', 'h', 'edge_feat'))
        edge_feats = g.edata['edge_feat']

        # 原始边特征也加进来（例如波长功率长度）
        combined_edge_feats = torch.cat([edge_feats, g.edata['original_edge_feat']], dim=1)

        x = combined_edge_feats
        for l in range(self.num_layers + 1, self.num_layers + self.num_layers_linear):
            x = self.activation(self.gat_layers[l](x))
        output = self.activation(self.gat_layers[-1](x))

        return output


### 2. 主程序入口
if __name__ == "__main__":
    # 加载图数据
    data = torch.load("g_data_Jnet_GNPY_Graduation.pt")
    g_data = []
    for g, label in data:
        e_featW = g.edata['efeat'][:, 0:80]  # 波长分配
        e_featL = g.edata['efeat'][:, 160:240]  # 链路长度
        g.edata['original_edge_feat'] = torch.cat([e_featW, e_featL], dim=1)
        g_data.append((g, label))

    # 2.2 划分训练/测试集
    batch_size = 16
    train_size = 0.8
    data_train, data_test = train_test_split(g_data, train_size=train_size)

    train_loader = GraphDataLoader(dataset=data_train, batch_size=batch_size, shuffle=True)
    test_loader = GraphDataLoader(dataset=data_test, batch_size=len(data_test), shuffle=False)

    # 2.3 模型超参数
    num_layers = 3
    num_layers_linear = 2
    in_feats = 80
    num_hidden = 64
    num_hidden_linear = 128
    num_out = 80
    heads = [3, 3, 3, 1]
    in_drop = 0.0
    attn_drop = 0.0
    negative_slope = 0.2
    weight_decay = 5e-4
    lr = 0.005
    residual = False
    epoch_num = 200

    # 2.4 初始化模型
    model = GAT(
        num_layers=num_layers,
        num_layers_linear=num_layers_linear,
        in_dim=in_feats,
        num_hidden=num_hidden,
        num_hidden_linear=num_hidden_linear,
        num_classes=num_out,
        heads=heads,
        activation=F.elu,
        feat_drop=in_drop,
        attn_drop=attn_drop,
        negative_slope=negative_slope,
        residual=residual
    )
    print(model)

    loss_fcn = torch.nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=weight_decay)

    # 2.5 训练过程
    dur = []
    for epoch in range(epoch_num):
        model.train()
        if epoch >= 3:
            t0 = time.time()

        # for batched_graph, labels in train_loader:
        #     features = batched_graph.ndata["nfeat"]
        #     logits = model(batched_graph, features)
        #     labels = labels.view(labels.shape[0] * labels.shape[1], labels.shape[2])
        #     loss = loss_fcn(logits, labels)
        #
        #     optimizer.zero_grad()
        #     loss.backward()
        #     optimizer.step()
        for batched_graph, labels in train_loader:
            features = batched_graph.ndata["nfeat"]

            logits = model(batched_graph, features)  # (边数, 80)

            # ✅新的地方！！！
            # 不要用节点labels了！
            edge_labels = batched_graph.edata['efeatW']  # 举个例子，如果efeatW可以当作target

            mask = edge_labels.sum(dim=1) > 0  # 有信号的边
            loss = loss_fcn(logits[mask], edge_labels[mask])

            optimizer.zero_grad()
            loss.backward()
            optimizer.step()

        if epoch >= 3:
            dur.append(time.time() - t0)

        print(f"Epoch {epoch:03d} | Loss {loss.item():.4f} | Time(s) {np.mean(dur):.4f}")

        # 每50轮降低学习率
        if epoch % 50 == 0 and epoch != 0:
            for param_group in optimizer.param_groups:
                param_group['lr'] *= 0.5

    # 2.6 测试过程
    model.eval()
    with torch.no_grad():
        test_loss = 0.0
        for batched_graph, labels in test_loader:
            features = batched_graph.ndata["nfeat"]
            logits = model(batched_graph, features)
            labels = labels.view(labels.shape[0] * labels.shape[1], labels.shape[2])
            loss = loss_fcn(logits, labels)
            test_loss += loss.item()
        test_loss /= len(test_loader)
        print(f"Test Loss: {test_loss:.4f}")
