import torch
import torch.nn as nn
import torch.nn.functional as F
import dgl
from dgl.nn.pytorch.conv import GATConv
import numpy as np
import pandas as pd
from sklearn.preprocessing import MinMaxScaler
from sklearn.model_selection import train_test_split
import time

###################################定义 GAT 模型###########################################
class GAT(nn.Module):
    def __init__(self, num_layers, in_dim, num_hidden, num_classes, heads, activation, feat_drop, attn_drop,
                 negative_slope, residual):
        super(GAT, self).__init__()
        self.num_layers = num_layers
        self.gat_layers = nn.ModuleList()
        self.activation = activation

        # 输入投影（无残差）
        self.gat_layers.append(GATConv(
            in_dim, num_hidden, heads[0],
            feat_drop, attn_drop, negative_slope, False, self.activation))

        # 隐藏层
        for l in range(1, num_layers):
            self.gat_layers.append(GATConv(
                num_hidden * heads[l - 1], num_hidden, heads[l],
                feat_drop, attn_drop, negative_slope, residual, self.activation))

        # 输出投影
        self.gat_layers.append(GATConv(
            num_hidden * heads[-2], num_hidden, heads[-1],
            feat_drop, attn_drop, negative_slope, residual, None))

    def forward(self, g, inputs):
        h = inputs
        for l in range(self.num_layers):
            h = self.gat_layers[l](g, h).flatten(1)
        # 输出投影
        logits = self.gat_layers[-1](g, h).mean(1)
        return logits


################################### 定义 MLPPredictor 模型###########################################
class MLPPredictor(nn.Module):
    def __init__(self, num_layers_linear, in_features, edge_features, num_hidden_linear, num_classes, activation):
        super().__init__()
        self.gat_layers = nn.ModuleList()
        self.activation = activation
        self.num_layers_linear = num_layers_linear

        if num_layers_linear == 1:
            self.gat_layers.append(torch.nn.Linear(in_features * 2 + edge_features, num_classes))
        elif num_layers_linear == 2:
            self.gat_layers.append(torch.nn.Linear(in_features * 2 + edge_features, num_hidden_linear))
            self.gat_layers.append(torch.nn.Linear(num_hidden_linear, num_classes))
        else:
            self.gat_layers.append(torch.nn.Linear(in_features * 2 + edge_features, num_hidden_linear))
            for l in range(1, num_layers_linear - 1):
                self.gat_layers.append(torch.nn.Linear(num_hidden_linear, num_hidden_linear))
            self.gat_layers.append(torch.nn.Linear(num_hidden_linear, num_classes))

    def apply_edges(self, edges):
        h_u = edges.src['h']
        h_v = edges.dst['h']
        h_e = edges.data["efeat"]
        h = torch.cat([h_u, h_v, h_e], 1)
        for l in range(0, self.num_layers_linear - 1):
            h = self.activation(self.gat_layers[l](h))
        score = self.activation(self.gat_layers[-1](h))
        return {'score': score}

    def forward(self, graph, h):
        with graph.local_scope():
            graph.ndata['h'] = h
            graph.apply_edges(self.apply_edges)
            return graph.edata['score']


###################################定义完整的 GAT 模型###########################################
class GATModel(nn.Module):
    def __init__(self, num_layers, num_layers_linear, in_dim, num_hidden, num_hidden_linear, num_classes, heads, activation_GNN, activation_NN, feat_drop, attn_drop, negative_slope, residual):
        super(GATModel, self).__init__()
        self.gat = GAT(num_layers, in_dim, num_hidden, num_hidden, heads, activation_GNN, feat_drop, attn_drop, negative_slope, residual)
        self.predictor = MLPPredictor(num_layers_linear, num_hidden, in_dim, num_hidden_linear, num_classes, activation_NN)

    def forward(self, g, features):
        h = self.gat(g, features)
        return self.predictor(g, h)


###################################数据预处理###########################################
# 示例数据加载和预处理
data = np.load('your_data.npy', allow_pickle=True)
data = data.tolist()

# 数据标准化等预处理操作
# ...

# 构建 DGL 图
g = dgl.graph((u, v))
g.ndata['feat'] = node_features
g.edata['feat'] = edge_features


###################################模型训练###########################################
def train(model, g, features, labels, optimizer, loss_fcn):
    model.train()
    optimizer.zero_grad()
    logits = model(g, features)
    loss = loss_fcn(logits, labels)
    loss.backward()
    optimizer.step()
    return loss.item()

# 初始化模型、优化器和损失函数
model = GATModel(
    num_layers=2,
    num_layers_linear=3,
    in_dim=161,
    num_hidden=64,
    num_hidden_linear=128,
    num_classes=80,
    heads=[3, 3, 1],
    activation_GNN=F.elu,
    activation_NN=F.elu,
    feat_drop=0.0,
    attn_drop=0.0,
    negative_slope=0.2,
    residual=True
)

optimizer = torch.optim.Adam(model.parameters(), lr=0.001, weight_decay=5e-4)
loss_fcn = torch.nn.MSELoss()

# 训练循环
for epoch in range(200):
    loss = train(model, g, features, labels, optimizer, loss_fcn)
    print(f'Epoch {epoch}, Loss: {loss:.4f}')
