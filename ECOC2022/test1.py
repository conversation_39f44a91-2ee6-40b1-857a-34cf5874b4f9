import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import dgl
from dgl.dataloading import GraphDataLoader
from sklearn.model_selection import train_test_split
import warnings

warnings.filterwarnings("ignore", category=UserWarning)

class MLPPredictor(nn.Module):
    def __init__(self, num_layers_linear, in_features, edge_features, num_hidden_linear, num_classes, activation):
        super().__init__()
        self.gat_layers = nn.ModuleList()
        self.activation = activation
        self.num_layers_linear = num_layers_linear
        if num_layers_linear == 1:
            self.gat_layers.append(torch.nn.Linear(in_features * 2 + edge_features, num_classes))
        elif num_layers_linear == 2:
            self.gat_layers.append(torch.nn.Linear(in_features * 2 + edge_features, num_hidden_linear))
            self.gat_layers.append(torch.nn.Linear(num_hidden_linear, num_classes))
        else:
            self.gat_layers.append(torch.nn.Linear(in_features * 2 + edge_features, num_hidden_linear))
            for l in range(1, num_layers_linear - 1):
                self.gat_layers.append(torch.nn.Linear(num_hidden_linear, num_hidden_linear))
            self.gat_layers.append(torch.nn.Linear(num_hidden_linear, num_classes))

    def apply_edges(self, edges):
        h_u = edges.src['h']
        h_v = edges.dst['h']
        h_e = edges.data["efeat"]
        h = torch.cat([h_u, h_v, h_e], 1)
        for l in range(0, self.num_layers_linear - 1):
            h = self.activation(self.gat_layers[l](h))
        score = self.activation(self.gat_layers[-1](h))
        return {'score': score}

    def forward(self, graph, h):
        with graph.local_scope():
            graph.ndata['h'] = h
            graph.apply_edges(self.apply_edges)
            return graph.edata['score']

class GAT(nn.Module):
    def __init__(self, num_layers, num_layers_linear, in_dim, num_hidden, num_hidden_linear, num_classes, heads,
                 activation_GNN, activation_NN, feat_drop, attn_drop, negative_slope, residual):
        super(GAT, self).__init__()
        from dgl.nn.pytorch import GATConv
        self.num_layers = num_layers
        self.num_layers_linear = num_layers_linear
        self.gat_layers = nn.ModuleList()
        self.activation_GNN = activation_GNN
        self.activation_NN = activation_NN
        self.gat_layers.append(GATConv(in_dim, num_hidden, heads[0], feat_drop, attn_drop, negative_slope, False, self.activation_GNN))
        for l in range(1, num_layers):
            self.gat_layers.append(GATConv(num_hidden * heads[l - 1], num_hidden, heads[l],
                                           feat_drop, attn_drop, negative_slope, residual, self.activation_GNN))
        self.gat_layers.append(GATConv(num_hidden * heads[-2], num_hidden, heads[-1],
                                       feat_drop, attn_drop, negative_slope, residual, None))
        self.pred = MLPPredictor(num_layers_linear, num_hidden, in_dim, num_hidden_linear, num_classes, self.activation_NN)

    def forward(self, g, inputs):
        h = inputs
        for l in range(self.num_layers):
            h = self.gat_layers[l](g, h).flatten(1)
        logits = self.gat_layers[self.num_layers](g, h).mean(1)
        return self.pred(g, logits)

data_files = [
    "Jnet data KSP_train_GNN_edgeFeat0.npy",
    "Jnet data KSP_train_GNN_edgeFeat1.npy",
    "Jnet data KSP_train_GNN_edgeFeat2.npy",
    "Jnet data KSP_train_GNN_edgeFeat3.npy",
    "Jnet_3.npy"
]

for filename in data_files:
    print(f"\n===== Testing {filename} =====")
    data = np.load(filename, allow_pickle=True).tolist()
    train_data, test_data = train_test_split(data, train_size=0.8, random_state=42)
    test_loader = GraphDataLoader(test_data, batch_size=len(test_data), shuffle=False)

    model = GAT(
        num_layers=2,
        num_layers_linear=3,
        in_dim=161,
        num_hidden=64,
        num_hidden_linear=128,
        num_classes=80,
        heads=[3, 3, 1],
        activation_GNN=F.elu,
        activation_NN=F.elu,
        feat_drop=0,
        attn_drop=0,
        negative_slope=0,
        residual=True
    )
    model.eval()
    loss_fn = nn.MSELoss()

    for batched_graph, labels in test_loader:
        with torch.no_grad():
            feats = batched_graph.ndata["nfeat"]
            preds = model(batched_graph, feats)
            labels = labels.view(labels.shape[0] * labels.shape[1], labels.shape[2])
            preds = preds.view_as(labels)

            mse = loss_fn(preds, labels).item()
            abs_error = torch.abs(preds - labels)
            below_05db = (abs_error < 0.5).float().mean().item()
            max_error = abs_error.max().item()

            print(f"MSE: {mse:.4f} | Max Error: {max_error:.4f} | <0.5dB占比: {below_05db:.4%}")
