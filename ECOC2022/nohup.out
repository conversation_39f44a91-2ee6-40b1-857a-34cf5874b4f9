nohup: ignoring input
Using backend: pytorch
/home/<USER>/anaconda3/envs/pytorch/lib/python3.8/site-packages/dgl/base.py:45: DGLWarning: DGLGraph.__len__ is deprecated.Please directly call DGLGraph.number_of_nodes.
  return warnings.warn(message, category=category, stacklevel=1)
Finished: 0
Finished: 10
Finished: 20
Finished: 30
Finished: 40
Finished: 50
Finished: 60
Finished: 70
Finished: 80
Finished: 90
Finished: 100
Finished: 110
Finished: 120
Finished: 130
Finished: 140
Finished: 150
Finished: 160
Finished: 170
Finished: 180
Finished: 190
Finished: 200
Finished: 210
Finished: 220
Finished: 230
Finished: 240
Finished: 250
Finished: 260
Finished: 270
Finished: 280
Finished: 290
Finished: 300
Finished: 310
Finished: 320
Finished: 330
Finished: 340
Finished: 350
Finished: 360
Finished: 370
Finished: 380
Finished: 390
Finished: 400
Finished: 410
Finished: 420
Finished: 430
Finished: 440
Finished: 450
Finished: 460
Finished: 470
Finished: 480
Finished: 490
Finished: 500
Finished: 510
Finished: 520
Finished: 530
Finished: 540
Finished: 550
Finished: 560
Finished: 570
Finished: 580
Finished: 590
Finished: 600
Finished: 610
Finished: 620
Finished: 630
Finished: 640
Finished: 650
Finished: 660
Finished: 670
Finished: 680
Finished: 690
Finished: 700
Finished: 710
Finished: 720
Finished: 730
Finished: 740
Finished: 750
Finished: 760
Finished: 770
Finished: 780
Finished: 790
Finished: 800
Finished: 810
Finished: 820
Finished: 830
Finished: 840
Finished: 850
Finished: 860
Finished: 870
Finished: 880
Finished: 890
Finished: 900
Finished: 910
Finished: 920
Finished: 930
Finished: 940
Finished: 950
Finished: 960
Finished: 970
Finished: 980
Finished: 990
Finished: 1000
Finished: 1010
Finished: 1020
Finished: 1030
Finished: 1040
Finished: 1050
Finished: 1060
Finished: 1070
Finished: 1080
Finished: 1090
Finished: 1100
Finished: 1110
Finished: 1120
Finished: 1130
Finished: 1140
Finished: 1150
Finished: 1160
Finished: 1170
Finished: 1180
Finished: 1190
Finished: 1200
Finished: 1210
Finished: 1220
Finished: 1230
Finished: 1240
Finished: 1250
Finished: 1260
Finished: 1270
Finished: 1280
Finished: 1290
Finished: 1300
Finished: 1310
Finished: 1320
Finished: 1330
Finished: 1340
Finished: 1350
Finished: 1360
Finished: 1370
Finished: 1380
Finished: 1390
Finished: 1400
Finished: 1410
Finished: 1420
Finished: 1430
Finished: 1440
Finished: 1450
Finished: 1460
Finished: 1470
Finished: 1480
Finished: 1490
Finished: 1500
Finished: 1510
Finished: 1520
Finished: 1530
Finished: 1540
Finished: 1550
Finished: 1560
Finished: 1570
Finished: 1580
Finished: 1590
Finished: 1600
Finished: 1610
Finished: 1620
Finished: 1630
Finished: 1640
Finished: 1650
Finished: 1660
Finished: 1670
Finished: 1680
Finished: 1690
Finished: 1700
Finished: 1710
Finished: 1720
Finished: 1730
Finished: 1740
Finished: 1750
Finished: 1760
Finished: 1770
Finished: 1780
Finished: 1790
Finished: 1800
Finished: 1810
Finished: 1820
Finished: 1830
Finished: 1840
Finished: 1850
Finished: 1860
Finished: 1870
Finished: 1880
Finished: 1890
Finished: 1900
Finished: 1910
Finished: 1920
Finished: 1930
Finished: 1940
Finished: 1950
Finished: 1960
Finished: 1970
Finished: 1980
Finished: 1990
Finished: 2000
Finished: 2010
Finished: 2020
Finished: 2030
Finished: 2040
Finished: 2050
Finished: 2060
Finished: 2070
Finished: 2080
Finished: 2090
Finished: 2100
Finished: 2110
Finished: 2120
Finished: 2130
Finished: 2140
Finished: 2150
Finished: 2160
Finished: 2170
Finished: 2180
Finished: 2190
Finished: 2200
Finished: 2210
Finished: 2220
Finished: 2230
Finished: 2240
Finished: 2250
Finished: 2260
Finished: 2270
Finished: 2280
Finished: 2290
Finished: 2300
Finished: 2310
Finished: 2320
Finished: 2330
Finished: 2340
Finished: 2350
Finished: 2360
Finished: 2370
Finished: 2380
Finished: 2390
Finished: 2400
Finished: 2410
Finished: 2420
Finished: 2430
Finished: 2440
Finished: 2450
Finished: 2460
Finished: 2470
Finished: 2480
Finished: 2490
Finished: 2500
Finished: 2510
Finished: 2520
Finished: 2530
Finished: 2540
Finished: 2550
Finished: 2560
Finished: 2570
Finished: 2580
Finished: 2590
Finished: 2600
Finished: 2610
Finished: 2620
Finished: 2630
Finished: 2640
Finished: 2650
Finished: 2660
Finished: 2670
Finished: 2680
Finished: 2690
Finished: 2700
Finished: 2710
Finished: 2720
Finished: 2730
Finished: 2740
Traceback (most recent call last):
  File "Data_Aggregation_edgeFeat.py", line 282, in <module>
    np.save('g_data_Jnet_GNPY.npy', g_data)
  File "<__array_function__ internals>", line 5, in save
  File "/home/<USER>/anaconda3/envs/pytorch/lib/python3.8/site-packages/numpy/lib/npyio.py", line 528, in save
    arr = np.asanyarray(arr)
  File "/home/<USER>/anaconda3/envs/pytorch/lib/python3.8/site-packages/dgl/heterograph.py", line 2152, in __getitem__
    raise DGLError('Invalid key "{}". Must be one of the edge types.'.format(orig_key))
dgl._ffi.base.DGLError: Invalid key "0". Must be one of the edge types.
