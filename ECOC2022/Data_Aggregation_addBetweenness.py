import argparse
import time
import numpy as np
import networkx as nx
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
import dgl
from sklearn.preprocessing import MinMaxScaler
from sklearn.model_selection import train_test_split
from dgl import DGLGraph
from dgl.data import register_data_args, load_data
from dgl.nn.pytorch.conv import SAGEConv
from dgl.nn.pytorch.conv import GATConv
import dgl.function as fn
from dgl.data import CoraGraphDataset, CiteseerGraphDataset, PubmedGraphDataset
import torch.utils.data as Data
from dgl.dataloading import GraphDataLoader
from dgl.data import MiniGCDataset
import copy

from utils import EarlyStopping
import os

# class GAT(nn.Module):
#     def __init__(self,
#                  num_layers,
#                  in_dim,
#                  num_hidden,
#                  num_classes,
#                  heads,
#                  activation,
#                  feat_drop,
#                  attn_drop,
#                  negative_slope,
#                  residual):
#         super(GAT, self).__init__()
#         self.num_layers = num_layers
#         self.gat_layers = nn.ModuleList()
#         self.activation = activation
#         # input projection (no residual)
#         self.gat_layers.append(GATConv(
#             in_dim, num_hidden, heads[0],
#             feat_drop, attn_drop, negative_slope, False, self.activation))
#         # hidden layers
#         for l in range(1, num_layers):
#             # due to multi-head, the in_dim = num_hidden * num_heads
#             self.gat_layers.append(GATConv(
#                 num_hidden * heads[l-1], num_hidden, heads[l],
#                 feat_drop, attn_drop, negative_slope, residual, self.activation))
#         # output projection
#         self.gat_layers.append(GATConv(
#             num_hidden * heads[-2], num_classes, heads[-1],
#             feat_drop, attn_drop, negative_slope, residual, None))
#     def forward(self, g,inputs):
#         h = inputs
#         for l in range(self.num_layers):
#             h = self.gat_layers[l](g, h).flatten(1)
#         # output projection
#         logits = self.gat_layers[-1](g, h).mean(1)
#         return logits

class GAT(nn.Module):
    def __init__(self,
                 num_layers,
                 num_layers_linear,
                 in_dim,
                 num_hidden,
                 num_hidden_linear,
                 num_classes,
                 heads,
                 activation,
                 feat_drop,
                 attn_drop,
                 negative_slope,
                 residual):
        super(GAT, self).__init__()
        self.num_layers = num_layers
        self.num_layers_linear = num_layers_linear
        self.gat_layers = nn.ModuleList()
        self.activation = activation
        # input projection (no residual)
        self.gat_layers.append(GATConv(
            in_dim, num_hidden, heads[0],
            feat_drop, attn_drop, negative_slope, False, self.activation))
        # hidden layers
        for l in range(1, num_layers):
            # due to multi-head, the in_dim = num_hidden * num_heads
            self.gat_layers.append(GATConv(
                num_hidden * heads[l - 1], num_hidden, heads[l],
                feat_drop, attn_drop, negative_slope, residual, self.activation))
        # output projection
        self.gat_layers.append(GATConv(
            num_hidden * heads[-2], num_hidden, heads[-1],
            feat_drop, attn_drop, negative_slope, residual, None))
        # regression
        # for l in range(0, num_layers_linear-1):
        #     self.gat_layers.append(torch.nn.Linear(num_hidden*heads[-1],num_hidden_linear))
        if num_layers_linear ==1:
            self.gat_layers.append(torch.nn.Linear(num_hidden*heads[-1],num_classes))
        elif num_layers_linear == 2:
            self.gat_layers.append(torch.nn.Linear(num_hidden * heads[-1], num_hidden_linear))
            self.gat_layers.append(torch.nn.Linear(num_hidden_linear, num_classes))
        else:
            self.gat_layers.append(torch.nn.Linear(num_hidden * heads[-1], num_hidden_linear))
            for l in range(1, num_layers_linear - 1):
                self.gat_layers.append(torch.nn.Linear(num_hidden_linear, num_hidden_linear))
            self.gat_layers.append(torch.nn.Linear(num_hidden_linear, num_classes))

    def forward(self, g, inputs):
        h = inputs
        for l in range(self.num_layers):
            h = self.gat_layers[l](g, h).flatten(1)
        # output projection
        logits = self.gat_layers[self.num_layers](g, h).mean(1)
        h=logits
        for l in range(num_layers+1, self.num_layers + self.num_layers_linear):
            h = self.activation(self.gat_layers[l](h))
        output = self.activation(self.gat_layers[-1](h))
        return output

def accuracy(logits, labels):
    _, indices = torch.max(logits, dim=1)
    correct = torch.sum(indices == labels)
    return correct.item() * 1.0 / len(labels)


def evaluate(model, features, labels, mask):
    model.eval()
    with torch.no_grad():
        logits = model(features)
        logits = logits[mask]
        labels = labels[mask]
        return accuracy(logits, labels)

# def findone(data):
#     n=0
#     for j in range(np.size(data, 0)):
#         for k in range(np.size(data, 1)):
#             n = n + len(data[j,k].nonzero())
#     print(n)

def MaxMinSclar(data,single=False,*wavelength):
    if single:#对于单个矩阵
        max_value = np.max(data)
        min_value = np.min(data)
        return (data-min_value)/(max_value-min_value)
    else:#对于复合矩阵（矩阵中的元素为矩阵）
        min_W = 0
        max_W = 0
        min_P = 0
        max_P = 0
        for i in range(np.size(data,0)):#找到每个部分的最大值和最小值
            a = np.max(data[i,0])
            b = np.min(data[i,0])
            if a>max_P:
                max_P = a
            if b<min_P:
                min_P=b
            for j in range(np.size(data[i,2],0)):
                for k in range(np.size(data[i,2],1)):
                    data[i, 2][j, k] = data[i, 2][j, k]*wavelength#将波长信息加入
                    a = np.max(data[i, 2][j,k])
                    b = np.min(data[i, 2][j,k])
                    if a > max_W:
                        max_W = a
                    if b < min_W:
                        min_W = b
        for i in range(np.size(data,0)):#最大最小归一化处理
            data[i,0] = (data[i,0]-min_P)/(max_P-min_P)
            for j in range(np.size(data[i,2],0)):
                for k in range(np.size(data[i,2],1)):
                    data[i, 2][j,k] = (data[i, 2][j,k] - min_W) / (max_W - min_W)
        return data


channel_num=80
wavelength_start = 191.3e12
wavelength_spacing = 50e9
wavelength = np.zeros((1,channel_num))
for i in range(channel_num):
    wavelength[0,i] = wavelength_start + i * wavelength_spacing
data1 = np.load("Jnet data KSP_train_GNN.npy" , allow_pickle=True)
data2 = np.load("Jnet data KSP_train_GNN_1.npy" , allow_pickle=True)
data3 = np.load("Jnet data KSP_train_GNN_2.npy" , allow_pickle=True)
data4 = np.load("Jnet data KSP_train_GNN_3.npy" , allow_pickle=True)
data5 = np.load("Jnet data KSP_train_GNN_4.npy" , allow_pickle=True)
data6 = np.load("Jnet data KSP_train_GNN_self.npy" , allow_pickle=True)
data7 = np.load("Jnet data KSP_train_GNN_self1.npy" , allow_pickle=True)
data8 = np.load("Jnet data KSP_train_GNN_self2.npy" , allow_pickle=True)

u,v = torch.tensor([0,0,1,2,3,3,4,4,4,5,6,6,7,8,11,9,8,13,11,11,13,10]),\
      torch.tensor([1,2,3,4,4,5,5,8,6,7,7,11,9,11,9,10,13,11,10,12,12,12])
link_length = np.array([[0,160,240,0,0,0,0,0,0,0,0,0,0,0],[160,0,0,240,0,0,0,0,0,0,0,0,0,0],[240,0,0,0,240,0,0,0,0,0,0,0,0,0],[0,240,0,0,80,40,0,0,0,0,0,0,0,0],[0,0,240,80,0,40,80,0,240,0,0,0,0,0],[0,0,0,40,40,0,0,160,0,0,0,0,0,0],[0,0,0,0,80,0,0,80,0,0,0,240,0,0],[0,0,0,0,0,160,80,0,0,160,0,0,0,0],[0,0,0,0,240,0,0,0,0,0,0,240,0,240],[0,0,0,0,0,0,0,160,0,0,40,40,0,0],[0,0,0,0,0,0,0,0,0,40,0,40,320,0],[0,0,0,0,0,0,240,0,240,40,40,0,320,240],[0,0,0,0,0,0,0,0,0,0,320,320,0,160],[0,0,0,0,0,0,0,0,240,0,0,240,160,0]])
g = dgl.graph((u,v))
g = g.int()
g = dgl.to_bidirected(g)
################加入节点中介性##########################
G = nx.Graph()
G.add_nodes_from(range(14))
u1 = u.numpy()
v1 = v.numpy()
for i in range(len(u)):
    G.add_edge(u1[i],v1[i])
G = G.to_directed()
betweenness = nx.betweenness_centrality(G, normalized=True)
# g = dgl.remove_self_loop(g)
# g = dgl.add_self_loop(g)
# cuda = True
# os.environ["TF_FORCE_GPU_ALLOW_GROWTH"] = "true"
# os.environ["CUDA_VISIBLE_DEVICES"] = "0"#（其中0.1是选择所调用的gpu）
link_sclar=MaxMinSclar(link_length,True)
df = pd.DataFrame(data1)
df = df.dropna()
data1 = df.values
df = pd.DataFrame(data2)
df = df.dropna()
data2 = df.values
data = np.vstack((data1,data2))
df = pd.DataFrame(data3)
df = df.dropna()
data3 = df.values
data = np.vstack((data,data3))
df = pd.DataFrame(data4)
df = df.dropna()
data4 = df.values
data = np.vstack((data,data4))
df = pd.DataFrame(data5)
df = df.dropna()
data5 = df.values
data = np.vstack((data,data5))
df = pd.DataFrame(data6)
df = df.dropna()
data6 = df.values
data = np.vstack((data,data6))
df = pd.DataFrame(data7)
df = df.dropna()
data7 = df.values
data = np.vstack((data,data7))
df = pd.DataFrame(data8)
df = df.dropna()
data8 = df.values
data = np.vstack((data,data8))

data_sclar = MaxMinSclar(data,False,wavelength)
#将矩阵数据转化为图数据
g_data = []
for i in range(len(data_sclar)):
    g1 = copy.deepcopy(g)
    e_featW = torch.zeros((g1.num_edges(),channel_num))
    e_featL = torch.zeros((g1.num_edges(),1))
    g_inter = []
    for j in range(len(data_sclar[i,0])):
        data_sclar[i,0][j] = betweenness[j]*data_sclar[i,0][j]
    g1.ndata["nfeat"] = torch.from_numpy(data_sclar[i,0]).type(torch.float32)#节点特征（发射功率）
    for j in range(len(data_sclar[i,2])):#边特征矩阵 波长分配+链路长度
        for k in range(len(data_sclar[i,2])):
            if g1.has_edges_between(j,k):
                e_featW[g1.edge_ids(j,k),0:channel_num] = torch.from_numpy(data_sclar[i,2][j,k])
                e_featL[g1.edge_ids(j, k), 0] = link_sclar[j,k]
    g1.edata["efeatW"] = e_featW
    g1.edata["efeatL"] = e_featL
    g1.update_all(dgl.function.e_add_v('efeatW', 'nfeat', 'm'),
                      dgl.function.sum('m', 'nfeat'))
    g1.update_all(dgl.function.e_mul_v('efeatL', 'nfeat', 'm'),
                  dgl.function.sum('m', 'nfeat'))
    g_inter.append(g1)
    g_inter.append(torch.from_numpy(data[i,1]).type(torch.float32))
    g_data.append(copy.deepcopy(g_inter))
np.save('g_data_Jnet_addBetweenness_self.npy', g_data)
# g_data1 = data8 = np.load('g_data_Jnet_self.npy' , allow_pickle=True)


# batch_size = 64
# train_size = 0.8
# data_train, data_test = train_test_split(g_data, train_size=train_size)
#
# loader = GraphDataLoader(dataset=data_train,batch_size=batch_size,shuffle=True)
#
# # # ### 计算中心中介性
# # # G = nx.Graph()
# # # G.add_nodes_from(range(9))  # 0-8节点
# # # for i in range(2):
# # #     G.add_edges_from([(7, 8), (4, 5), (7, 4), (8, 5), (8, 6), (6, 3), (5, 2), (4, 0), (2, 0), (2, 3), (3, 1),
# # #                       (0, 1)])  # 12条边
# # # G = G.to_directed()
# # # 计算每个节点的中心中介性
#
# # features = g.ndata['feat']
# # labels = g.ndata['label']
# # train_mask = g.ndata['train_mask']
# # val_mask = g.ndata['val_mask']
# # test_mask = g.ndata['test_mask']
# # num_feats = features.shape[1]
# # n_classes = data.num_labels
# # n_edges = data.graph.number_of_edges()
# # print("""----Data statistics------'
# #   #Edges %d
# #   #Classes %d
# #   #Train samples %d
# #   #Val samples %d
# #   #Test samples %d""" %
# #       (n_edges, n_classes,
# #        train_mask.int().sum().item(),
# #        val_mask.int().sum().item(),
# #        test_mask.int().sum().item()))
# #
# # # add self loop
# # g = dgl.to_bidirected(g)
# # g = dgl.remove_self_loop(g)
# # g = dgl.add_self_loop(g)
# # n_edges = g.number_of_edges()
# # # create model
# ###############模型超参数
# num_heads = 3
# num_out_heads=1
# num_layers = 2
# num_layers_linear = 2
# heads = ([num_heads] * num_layers) + [num_out_heads]
# in_feats = 80
# num_hidden = 64
# num_hidden_linear = 128
# num_out = 80
# in_drop = 0
# attn_drop = 0
# negative_slope = 0
# weight_decay = 1e-5
# lr = 1e-3
# residual = False
# epoch_num = 400
# model = GAT(
#             num_layers,
#             num_layers_linear,
#             in_feats,
#             num_hidden,
#             num_hidden_linear,
#             num_out,
#             heads,
#             F.elu,
#             in_drop,
#             attn_drop,
#             negative_slope,
#             residual)
# print(model)
# loss_fcn = torch.nn.MSELoss()
# optimizer = torch.optim.Adam(
#     model.parameters(), lr=lr, weight_decay=weight_decay)
# dur = []
# #########训练#############
# loss_list = []
# epoch_list = []
# for epoch in range(epoch_num):
#     model.train()
#     if epoch >= 3:
#         t0 = time.time()
#     for batched_graph, labels in loader:
#         #forward
#         features = batched_graph.ndata["nfeat"]
#         logits = model(batched_graph,features)
#         labels= labels.view(labels.shape[0]*labels.shape[1],labels.shape[2])
#         loss = loss_fcn(logits, labels)
#
#         optimizer.zero_grad()
#         loss.backward()
#         optimizer.step()
#
#     if epoch >= 3:
#         dur.append(time.time() - t0)
#     print('Epoch: ', epoch, '| Loss: ', loss.item(),'| Time(s): ', np.mean(dur))
#     loss_list.append(loss.item())
#     epoch_list.append(epoch)
#
#
#
#
# # heads = ([args.num_heads] * args.num_layers) + [args.num_out_heads]
# # print(model)
# # if cuda:
# #     model.cuda()
# # # initialize graph
# # dur = []
# # for epoch in range(args.epochs):
# #     model.train()
# #     if epoch >= 3:
# #         t0 = time.time()
# #     # forward
# #     logits = model(features)
# #     loss = loss_fcn(logits[train_mask], labels[train_mask])
# #
# #     optimizer.zero_grad()
# #     loss.backward()
# #     optimizer.step()
# #
# #     if epoch >= 3:
# #         dur.append(time.time() - t0)
# #
# #     train_acc = accuracy(logits[train_mask], labels[train_mask])
# #
# #     if args.fastmode:
# #         val_acc = accuracy(logits[val_mask], labels[val_mask])
# #     else:
# #         val_acc = evaluate(model, features, labels, val_mask)
# #         if args.early_stop:
# #             if stopper.step(val_acc, model):
# #                 break
# #
# #     print("Epoch {:05d} | Time(s) {:.4f} | Loss {:.4f} | TrainAcc {:.4f} |"
# #           " ValAcc {:.4f} | ETputs(KTEPS) {:.2f}".
# #           format(epoch, np.mean(dur), loss.item(), train_acc,
# #                  val_acc, n_edges / np.mean(dur) / 1000))
# #
# # print()
# # if args.early_stop:
# #     model.load_state_dict(torch.load('es_checkpoint.pt'))
# # acc = evaluate(model, features, labels, test_mask)
# # print("Test Accuracy {:.4f}".format(acc))

