#!/usr/bin/env python3
"""
串扰感知的QoT快速更新系统
专门针对新增光路时的全网QoT快速精确更新
"""

import numpy as np
import time
from typing import Dict, List, Set, Tuple
from collections import defaultdict
import json

class FastCrosstalkAnalyzer:
    """快速串扰分析器 - 毫秒级响应"""
    
    def __init__(self):
        self.crosstalk_cache = {}  # 缓存串扰计算结果
        self.wavelength_map = defaultdict(list)  # 波长到光路的映射
        self.path_overlap_cache = {}  # 路径重叠缓存
        
    def update_wavelength_map(self, lightpaths: List[Dict]):
        """更新波长映射表"""
        self.wavelength_map.clear()
        for lp in lightpaths:
            wavelength = lp.get('wavelength', 0)
            self.wavelength_map[wavelength].append(lp)
    
    def calculate_path_overlap(self, path1: List[str], path2: List[str]) -> Dict:
        """计算两条路径的重叠信息"""
        
        # 缓存键
        cache_key = f"{'-'.join(sorted(path1))}_{'-'.join(sorted(path2))}"
        if cache_key in self.path_overlap_cache:
            return self.path_overlap_cache[cache_key]
        
        set1, set2 = set(path1), set(path2)
        overlap_nodes = set1 & set2
        union_nodes = set1 | set2
        
        overlap_info = {
            'overlap_nodes': list(overlap_nodes),
            'overlap_count': len(overlap_nodes),
            'overlap_ratio': len(overlap_nodes) / max(len(union_nodes), 1),
            'path1_coverage': len(overlap_nodes) / max(len(set1), 1),
            'path2_coverage': len(overlap_nodes) / max(len(set2), 1),
            'max_consecutive_overlap': self._find_max_consecutive_overlap(path1, path2)
        }
        
        # 缓存结果
        self.path_overlap_cache[cache_key] = overlap_info
        return overlap_info
    
    def _find_max_consecutive_overlap(self, path1: List[str], path2: List[str]) -> int:
        """找到最大连续重叠段长度"""
        max_overlap = 0
        
        for i in range(len(path1)):
            for j in range(len(path2)):
                overlap_length = 0
                while (i + overlap_length < len(path1) and 
                       j + overlap_length < len(path2) and
                       path1[i + overlap_length] == path2[j + overlap_length]):
                    overlap_length += 1
                max_overlap = max(max_overlap, overlap_length)
        
        return max_overlap
    
    def calculate_crosstalk_penalty(self, new_lightpath: Dict, existing_lightpath: Dict) -> Dict:
        """计算串扰惩罚 - 精确物理模型"""
        
        new_wavelength = new_lightpath.get('wavelength', 0)
        existing_wavelength = existing_lightpath.get('wavelength', 0)
        new_power = new_lightpath.get('power', -15.0)
        existing_power = existing_lightpath.get('power', -15.0)
        
        # 计算路径重叠
        overlap_info = self.calculate_path_overlap(
            new_lightpath.get('path', []), 
            existing_lightpath.get('path', [])
        )
        
        if overlap_info['overlap_count'] == 0:
            return {'penalty_db': 0.0, 'type': 'no-overlap', 'confidence': 1.0}
        
        # 波长差异
        wavelength_diff = abs(new_wavelength - existing_wavelength)
        
        # 1. 同信道串扰（最强）
        if wavelength_diff == 0:
            penalty = self._calculate_cochannel_crosstalk(overlap_info, new_power, existing_power)
            return {
                'penalty_db': penalty,
                'type': 'co-channel',
                'confidence': 0.95,
                'overlap_info': overlap_info,
                'dominant_mechanism': 'co-channel interference'
            }
        
        # 2. 邻近信道串扰
        elif wavelength_diff <= 4:
            penalty = self._calculate_adjacent_channel_crosstalk(
                wavelength_diff, overlap_info, new_power, existing_power
            )
            return {
                'penalty_db': penalty,
                'type': 'adjacent-channel',
                'confidence': 0.85,
                'wavelength_distance': wavelength_diff,
                'overlap_info': overlap_info,
                'dominant_mechanism': 'adjacent channel interference'
            }
        
        # 3. 非线性串扰（高功率时）
        elif overlap_info['overlap_ratio'] > 0.3 and (new_power > -5 or existing_power > -5):
            penalty = self._calculate_nonlinear_crosstalk(overlap_info, new_power, existing_power)
            if penalty > 0.05:
                return {
                    'penalty_db': penalty,
                    'type': 'nonlinear',
                    'confidence': 0.75,
                    'overlap_info': overlap_info,
                    'dominant_mechanism': 'nonlinear effects'
                }
        
        return {'penalty_db': 0.0, 'type': 'negligible', 'confidence': 0.9}
    
    def _calculate_cochannel_crosstalk(self, overlap_info: Dict, new_power: float, existing_power: float) -> float:
        """计算同信道串扰惩罚"""
        
        overlap_ratio = overlap_info['overlap_ratio']
        consecutive_overlap = overlap_info['max_consecutive_overlap']
        
        # 基础惩罚：基于重叠比例
        base_penalty = overlap_ratio * 3.0  # 最高3dB基础惩罚
        
        # 连续重叠加重惩罚
        consecutive_penalty = min(consecutive_overlap * 0.5, 2.0)
        
        # 功率相关调制
        power_diff = abs(new_power - existing_power)
        avg_power = (new_power + existing_power) / 2
        
        # 功率差异影响（功率差越大，干扰越强）
        power_diff_factor = 1 + (power_diff / 10.0)
        
        # 平均功率影响（高功率时非线性效应增强）
        power_level_factor = 1 + max(0, (avg_power + 10) / 15.0)
        
        total_penalty = (base_penalty + consecutive_penalty) * power_diff_factor * power_level_factor
        
        return min(total_penalty, 8.0)  # 最大8dB惩罚
    
    def _calculate_adjacent_channel_crosstalk(self, wavelength_diff: int, overlap_info: Dict, 
                                            new_power: float, existing_power: float) -> float:
        """计算邻近信道串扰惩罚"""
        
        # 邻近信道衰减因子
        attenuation_factors = {1: 0.7, 2: 0.4, 3: 0.2, 4: 0.1}
        attenuation = attenuation_factors.get(wavelength_diff, 0.05)
        
        overlap_ratio = overlap_info['overlap_ratio']
        consecutive_overlap = overlap_info['max_consecutive_overlap']
        
        # 基础惩罚
        base_penalty = overlap_ratio * attenuation * 2.0
        
        # 连续重叠影响
        consecutive_penalty = min(consecutive_overlap * attenuation * 0.3, 1.0)
        
        # 功率相关调制（邻近信道对功率更敏感）
        avg_power = (new_power + existing_power) / 2
        power_factor = 1 + max(0, (avg_power + 5) / 20.0)
        
        total_penalty = (base_penalty + consecutive_penalty) * power_factor
        
        return min(total_penalty, 3.0)  # 最大3dB惩罚
    
    def _calculate_nonlinear_crosstalk(self, overlap_info: Dict, new_power: float, existing_power: float) -> float:
        """计算非线性串扰惩罚"""
        
        overlap_ratio = overlap_info['overlap_ratio']
        total_power = new_power + existing_power
        
        # 非线性效应阈值
        if total_power < -10:
            return 0.0
        
        # 非线性因子（功率越高越显著）
        nonlinear_factor = max(0, (total_power + 10) / 20.0)
        
        # 基于重叠的非线性惩罚
        base_penalty = overlap_ratio * nonlinear_factor * 1.5
        
        # 四波混频效应（高功率时）
        if total_power > -3:
            fwm_penalty = overlap_ratio * 0.5
            base_penalty += fwm_penalty
        
        return min(base_penalty, 2.0)  # 最大2dB惩罚

class RapidQoTUpdater:
    """快速QoT更新器"""
    
    def __init__(self):
        self.crosstalk_analyzer = FastCrosstalkAnalyzer()
        self.qot_cache = {}
        self.update_history = []
        
    def rapid_qot_update_for_new_lightpath(self, new_lightpath: Dict, 
                                         existing_lightpaths: List[Dict]) -> Dict:
        """新增光路时的快速QoT更新"""
        
        start_time = time.time()
        
        # 更新波长映射
        self.crosstalk_analyzer.update_wavelength_map(existing_lightpaths)
        
        # 分析结果
        update_results = {
            'new_lightpath': new_lightpath,
            'affected_lightpaths': [],
            'qot_updates': {},
            'crosstalk_summary': {
                'total_analyzed': len(existing_lightpaths),
                'affected_count': 0,
                'co_channel_conflicts': 0,
                'adjacent_channel_conflicts': 0,
                'nonlinear_effects': 0,
                'max_penalty_db': 0.0,
                'avg_penalty_db': 0.0
            },
            'performance_metrics': {
                'analysis_time_ms': 0,
                'lightpaths_per_second': 0,
                'cache_hit_rate': 0
            }
        }
        
        penalties = []
        
        # 快速串扰分析
        for existing_lp in existing_lightpaths:
            crosstalk_result = self.crosstalk_analyzer.calculate_crosstalk_penalty(
                new_lightpath, existing_lp
            )
            
            if crosstalk_result['penalty_db'] > 0.05:  # 只处理显著影响
                # 更新QoT
                original_qot = existing_lp.get('qot', self._get_default_qot())
                updated_qot = self._apply_crosstalk_penalty(original_qot, crosstalk_result)
                
                affected_info = {
                    'lightpath_id': existing_lp.get('id', ''),
                    'original_qot': original_qot,
                    'updated_qot': updated_qot,
                    'crosstalk_info': crosstalk_result,
                    'qot_degradation': {
                        'snr_loss_db': original_qot.get('snr', 0) - updated_qot.get('snr', 0),
                        'osnr_loss_db': original_qot.get('osnr', 0) - updated_qot.get('osnr', 0),
                        'ber_increase': updated_qot.get('ber', 0) - original_qot.get('ber', 0)
                    }
                }
                
                update_results['affected_lightpaths'].append(affected_info)
                update_results['qot_updates'][existing_lp.get('id', '')] = updated_qot
                
                # 统计信息
                penalties.append(crosstalk_result['penalty_db'])
                if crosstalk_result['type'] == 'co-channel':
                    update_results['crosstalk_summary']['co_channel_conflicts'] += 1
                elif crosstalk_result['type'] == 'adjacent-channel':
                    update_results['crosstalk_summary']['adjacent_channel_conflicts'] += 1
                elif crosstalk_result['type'] == 'nonlinear':
                    update_results['crosstalk_summary']['nonlinear_effects'] += 1
        
        # 完善统计信息
        update_results['crosstalk_summary']['affected_count'] = len(update_results['affected_lightpaths'])
        if penalties:
            update_results['crosstalk_summary']['max_penalty_db'] = max(penalties)
            update_results['crosstalk_summary']['avg_penalty_db'] = np.mean(penalties)
        
        # 性能指标
        analysis_time = time.time() - start_time
        update_results['performance_metrics']['analysis_time_ms'] = analysis_time * 1000
        update_results['performance_metrics']['lightpaths_per_second'] = len(existing_lightpaths) / max(analysis_time, 0.001)
        
        # 记录更新历史
        self.update_history.append({
            'timestamp': time.time(),
            'new_lightpath_id': new_lightpath.get('id', ''),
            'affected_count': len(update_results['affected_lightpaths']),
            'analysis_time_ms': analysis_time * 1000
        })
        
        return update_results
    
    def _get_default_qot(self) -> Dict:
        """获取默认QoT值"""
        return {
            'snr': 25.0,
            'osnr': 26.0,
            'ber': 1e-15,
            'q_factor': 15.0
        }
    
    def _apply_crosstalk_penalty(self, original_qot: Dict, crosstalk_result: Dict) -> Dict:
        """应用串扰惩罚到QoT值"""
        
        updated_qot = original_qot.copy()
        penalty_db = crosstalk_result['penalty_db']
        confidence = crosstalk_result['confidence']
        
        # 应用惩罚（考虑置信度）
        effective_penalty = penalty_db * confidence
        
        # 更新SNR
        original_snr = original_qot.get('snr', 25.0)
        updated_qot['snr'] = max(original_snr - effective_penalty, 8.0)  # 最低SNR限制
        
        # 更新OSNR（通常比SNR受影响稍小）
        original_osnr = original_qot.get('osnr', 26.0)
        updated_qot['osnr'] = max(original_osnr - effective_penalty * 0.9, 10.0)
        
        # 更新BER（基于SNR的指数关系）
        snr_linear = 10 ** (updated_qot['snr'] / 10)
        updated_qot['ber'] = 0.5 * np.exp(-snr_linear / 2)
        
        # 更新Q因子
        if updated_qot['ber'] > 0:
            updated_qot['q_factor'] = 20 * np.log10(np.sqrt(2) * np.sqrt(-np.log(updated_qot['ber'])))
        else:
            updated_qot['q_factor'] = original_qot.get('q_factor', 15.0)
        
        # 添加串扰信息
        updated_qot['crosstalk_penalty_db'] = effective_penalty
        updated_qot['crosstalk_type'] = crosstalk_result['type']
        updated_qot['crosstalk_confidence'] = confidence
        updated_qot['update_timestamp'] = time.time()
        
        return updated_qot
    
    def get_performance_summary(self) -> Dict:
        """获取性能总结"""
        
        if not self.update_history:
            return {'message': 'No updates performed yet'}
        
        analysis_times = [record['analysis_time_ms'] for record in self.update_history]
        affected_counts = [record['affected_count'] for record in self.update_history]
        
        return {
            'total_updates': len(self.update_history),
            'avg_analysis_time_ms': np.mean(analysis_times),
            'min_analysis_time_ms': np.min(analysis_times),
            'max_analysis_time_ms': np.max(analysis_times),
            'avg_affected_lightpaths': np.mean(affected_counts),
            'total_lightpaths_analyzed': sum(affected_counts),
            'cache_efficiency': len(self.crosstalk_analyzer.path_overlap_cache) / max(len(self.update_history), 1)
        }

def main():
    """测试串扰感知QoT快速更新系统"""
    
    print("🚀 测试串扰感知QoT快速更新系统")
    print("=" * 60)
    
    # 创建更新器
    updater = RapidQoTUpdater()
    
    # 模拟现有光路
    existing_lightpaths = []
    for i in range(50):  # 50条现有光路
        lp = {
            'id': f'lp_{i:03d}',
            'path': [str(j) for j in range(i % 5, (i % 5) + 4)],  # 模拟路径
            'wavelength': 40 + (i % 10),  # 波长40-49
            'power': -15.0 + np.random.uniform(-3, 3),
            'qot': {
                'snr': 25.0 + np.random.uniform(-2, 2),
                'osnr': 26.0 + np.random.uniform(-2, 2),
                'ber': 1e-15 * np.random.uniform(0.5, 2.0)
            }
        }
        existing_lightpaths.append(lp)
    
    # 新增光路
    new_lightpath = {
        'id': 'new_lp_001',
        'path': ['0', '1', '2', '3'],
        'wavelength': 42,  # 与一些现有光路同信道
        'power': -12.0
    }
    
    print(f"现有光路数量: {len(existing_lightpaths)}")
    print(f"新增光路: {new_lightpath['id']}, 波长: {new_lightpath['wavelength']}")
    
    # 执行快速更新
    start_time = time.time()
    update_results = updater.rapid_qot_update_for_new_lightpath(new_lightpath, existing_lightpaths)
    total_time = time.time() - start_time
    
    # 显示结果
    print(f"\n📊 更新结果:")
    print(f"   总分析时间: {total_time*1000:.2f} ms")
    print(f"   受影响光路数: {update_results['crosstalk_summary']['affected_count']}")
    print(f"   同信道冲突: {update_results['crosstalk_summary']['co_channel_conflicts']}")
    print(f"   邻近信道冲突: {update_results['crosstalk_summary']['adjacent_channel_conflicts']}")
    print(f"   最大惩罚: {update_results['crosstalk_summary']['max_penalty_db']:.3f} dB")
    print(f"   平均惩罚: {update_results['crosstalk_summary']['avg_penalty_db']:.3f} dB")
    print(f"   处理速度: {update_results['performance_metrics']['lightpaths_per_second']:.0f} 光路/秒")
    
    # 显示几个受影响光路的详细信息
    print(f"\n🔍 受影响光路详情 (前5个):")
    for i, affected in enumerate(update_results['affected_lightpaths'][:5]):
        print(f"   {i+1}. 光路 {affected['lightpath_id']}:")
        print(f"      SNR: {affected['original_qot']['snr']:.2f} → {affected['updated_qot']['snr']:.2f} dB")
        print(f"      串扰类型: {affected['crosstalk_info']['type']}")
        print(f"      惩罚: {affected['crosstalk_info']['penalty_db']:.3f} dB")
    
    # 性能总结
    perf_summary = updater.get_performance_summary()
    print(f"\n⚡ 性能总结:")
    print(f"   平均分析时间: {perf_summary['avg_analysis_time_ms']:.2f} ms")
    print(f"   缓存效率: {perf_summary['cache_efficiency']:.2f}")
    
    print(f"\n✅ 串扰感知QoT快速更新测试完成！")
    
    # 保存结果
    with open('crosstalk_qot_update_results.json', 'w') as f:
        json.dump(update_results, f, indent=2, default=str)
    
    print(f"📁 详细结果已保存到 crosstalk_qot_update_results.json")

if __name__ == "__main__":
    main()
