#!/usr/bin/env python3
"""
优化的QoT更新策略
实现高效的全网QoT值更新，训练好后精度高、预测快

核心策略：
1. 增量式更新 - 只更新真正受影响的光路
2. 分层更新机制 - 按影响程度分优先级更新
3. 缓存机制 - 避免重复计算
4. 批量更新优化 - 减少网络开销
5. 自适应更新频率 - 根据网络变化动态调整
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Set, Optional
from dataclasses import dataclass, field
from collections import deque, defaultdict
import time
import threading
import queue
from concurrent.futures import ThreadPoolExecutor, as_completed
import json
import pickle
from intelligent_subgraph_qot_system import IntelligentSubgraphGAT
from dynamic_lightpath_impact_detector import DynamicImpactDetector, LightpathInfo, ImpactAnalysisResult

@dataclass
class QoTUpdateTask:
    """QoT更新任务"""
    lightpath_id: int
    priority: int  # 1=高, 2=中, 3=低
    update_type: str  # 'full', 'incremental', 'cached'
    estimated_change: float
    last_updated: float
    retry_count: int = 0
    
@dataclass
class QoTCacheEntry:
    """QoT缓存条目"""
    lightpath_id: int
    cached_qot: float
    cache_timestamp: float
    dependency_hash: str  # 依赖光路的哈希值
    confidence: float
    hit_count: int = 0

class IncrementalQoTCalculator:
    """增量式QoT计算器"""
    
    def __init__(self, base_calculator=None):
        """
        初始化增量计算器
        
        Args:
            base_calculator: 基础QoT计算器（物理层模型）
        """
        self.base_calculator = base_calculator
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 轻量级增量计算网络
        self.incremental_model = nn.Sequential(
            nn.Linear(8, 32),  # 输入：影响特征向量
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(32, 16),
            nn.ReLU(),
            nn.Linear(16, 1),  # 输出：QoT变化量
            nn.Tanh()  # QoT变化可正可负
        ).to(self.device)
        
        # 不确定性估计网络
        self.uncertainty_model = nn.Sequential(
            nn.Linear(8, 16),
            nn.ReLU(),
            nn.Linear(16, 8),
            nn.ReLU(),
            nn.Linear(8, 1),
            nn.Sigmoid()  # 输出不确定性 [0, 1]
        ).to(self.device)
        
        print("✅ 增量式QoT计算器初始化")
        
    def calculate_incremental_change(self, lightpath_features: torch.Tensor, 
                                   impact_features: torch.Tensor) -> Tuple[float, float]:
        """
        计算增量QoT变化
        
        Args:
            lightpath_features: 光路特征
            impact_features: 影响特征
            
        Returns:
            qot_change: QoT变化量
            uncertainty: 不确定性
        """
        combined_features = torch.cat([lightpath_features, impact_features], dim=0)
        
        with torch.no_grad():
            qot_change = self.incremental_model(combined_features.unsqueeze(0))
            uncertainty = self.uncertainty_model(combined_features.unsqueeze(0))
        
        return qot_change.item(), uncertainty.item()

class QoTUpdateCache:
    """QoT更新缓存管理器"""
    
    def __init__(self, max_cache_size=1000, cache_ttl=300):
        """
        初始化缓存
        
        Args:
            max_cache_size: 最大缓存大小
            cache_ttl: 缓存存活时间（秒）
        """
        self.max_size = max_cache_size
        self.ttl = cache_ttl
        self.cache: Dict[int, QoTCacheEntry] = {}
        self.access_order = deque()  # LRU淘汰
        self.lock = threading.RLock()
        
        # 缓存统计
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'updates': 0
        }
        
        print(f"✅ QoT缓存初始化: 大小={max_cache_size}, TTL={cache_ttl}s")
    
    def get(self, lightpath_id: int, dependency_hash: str) -> Optional[float]:
        """获取缓存的QoT值"""
        with self.lock:
            if lightpath_id not in self.cache:
                self.stats['misses'] += 1
                return None
            
            entry = self.cache[lightpath_id]
            current_time = time.time()
            
            # 检查TTL和依赖
            if (current_time - entry.cache_timestamp > self.ttl or 
                entry.dependency_hash != dependency_hash):
                del self.cache[lightpath_id]
                self.stats['misses'] += 1
                return None
            
            # 缓存命中
            entry.hit_count += 1
            self.access_order.remove(lightpath_id)
            self.access_order.append(lightpath_id)
            self.stats['hits'] += 1
            
            return entry.cached_qot
    
    def put(self, lightpath_id: int, qot_value: float, dependency_hash: str, confidence: float):
        """存储QoT值到缓存"""
        with self.lock:
            current_time = time.time()
            
            # 检查缓存大小，执行LRU淘汰
            while len(self.cache) >= self.max_size:
                oldest_id = self.access_order.popleft()
                del self.cache[oldest_id]
                self.stats['evictions'] += 1
            
            # 添加/更新缓存条目
            entry = QoTCacheEntry(
                lightpath_id=lightpath_id,
                cached_qot=qot_value,
                cache_timestamp=current_time,
                dependency_hash=dependency_hash,
                confidence=confidence
            )
            
            self.cache[lightpath_id] = entry
            
            if lightpath_id in self.access_order:
                self.access_order.remove(lightpath_id)
            self.access_order.append(lightpath_id)
            
            self.stats['updates'] += 1
    
    def invalidate(self, lightpath_ids: List[int]):
        """使指定光路的缓存失效"""
        with self.lock:
            for lp_id in lightpath_ids:
                if lp_id in self.cache:
                    del self.cache[lp_id]
                    try:
                        self.access_order.remove(lp_id)
                    except ValueError:
                        pass
    
    def get_stats(self) -> Dict:
        """获取缓存统计信息"""
        with self.lock:
            total_requests = self.stats['hits'] + self.stats['misses']
            hit_rate = self.stats['hits'] / max(total_requests, 1)
            
            return {
                'cache_size': len(self.cache),
                'hit_rate': hit_rate,
                'total_hits': self.stats['hits'],
                'total_misses': self.stats['misses'],
                'evictions': self.stats['evictions'],
                'updates': self.stats['updates']
            }

class OptimizedQoTUpdater:
    """优化的QoT更新器 - 主要类"""
    
    def __init__(self, network_graph, impact_detector: DynamicImpactDetector, 
                 intelligent_model_path=None, max_workers=4):
        """
        初始化优化的QoT更新器
        
        Args:
            network_graph: 网络拓扑图
            impact_detector: 动态影响检测器
            intelligent_model_path: 智能模型路径
            max_workers: 最大工作线程数
        """
        self.network_graph = network_graph
        self.impact_detector = impact_detector
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 初始化组件
        self.incremental_calculator = IncrementalQoTCalculator()
        self.cache = QoTUpdateCache()
        
        # 线程池用于并行更新
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # 更新任务队列（按优先级排序）
        self.update_queue = queue.PriorityQueue()
        
        # 加载智能模型
        if intelligent_model_path:
            try:
                self.intelligent_model = IntelligentSubgraphGAT(
                    node_feature_dim=10, hidden_dim=128, num_layers=3
                ).to(self.device)
                self.intelligent_model.load_state_dict(torch.load(intelligent_model_path))
                self.intelligent_model.eval()
                print(f"✅ 加载智能QoT模型: {intelligent_model_path}")
            except Exception as e:
                print(f"⚠️ 无法加载智能模型: {e}")
                self.intelligent_model = None
        else:
            self.intelligent_model = None
        
        # QoT更新策略配置
        self.update_config = {
            'high_priority_threshold': 0.7,    # 高优先级影响阈值
            'medium_priority_threshold': 0.4,  # 中优先级影响阈值
            'batch_size': 16,                  # 批量更新大小
            'update_interval': 0.1,            # 更新间隔（秒）
            'max_retry': 3,                    # 最大重试次数
            'uncertainty_threshold': 0.8       # 不确定性阈值
        }
        
        # 更新统计
        self.update_stats = {
            'total_updates': 0,
            'successful_updates': 0,
            'cached_updates': 0,
            'failed_updates': 0,
            'avg_update_time': 0.0,
            'batch_updates': 0
        }
        
        # 启动后台更新线程
        self.running = True
        self.update_thread = threading.Thread(target=self._background_updater, daemon=True)
        self.update_thread.start()
        
        print(f"✅ 优化QoT更新器初始化完成")
        print(f"   最大工作线程: {max_workers}")
        print(f"   批量更新大小: {self.update_config['batch_size']}")
    
    def schedule_update(self, lightpath_id: int, impact_result: ImpactAnalysisResult, 
                       update_type: str = 'incremental'):
        """
        调度QoT更新任务
        
        Args:
            lightpath_id: 光路ID
            impact_result: 影响分析结果
            update_type: 更新类型
        """
        # 确定优先级
        priority = self._determine_priority(impact_result)
        
        # 创建更新任务
        task = QoTUpdateTask(
            lightpath_id=lightpath_id,
            priority=priority,
            update_type=update_type,
            estimated_change=impact_result.estimated_qot_change,
            last_updated=time.time()
        )
        
        # 添加到更新队列
        self.update_queue.put((priority, time.time(), task))
        
        print(f"📋 调度QoT更新: LP-{lightpath_id}, 优先级={priority}, 类型={update_type}")
    
    def execute_immediate_update(self, lightpath_id: int, force_full=False) -> Dict:
        """
        立即执行QoT更新
        
        Args:
            lightpath_id: 光路ID
            force_full: 强制全量更新
            
        Returns:
            更新结果
        """
        start_time = time.time()
        
        lightpath = self.impact_detector.active_lightpaths.get(lightpath_id)
        if not lightpath:
            return {'error': f'光路 LP-{lightpath_id} 不存在'}
        
        # 生成依赖哈希
        dependency_hash = self._generate_dependency_hash(lightpath)
        
        # 尝试缓存查找
        if not force_full:
            cached_qot = self.cache.get(lightpath_id, dependency_hash)
            if cached_qot is not None:
                self.update_stats['cached_updates'] += 1
                return {
                    'lightpath_id': lightpath_id,
                    'qot_value': cached_qot,
                    'update_time': time.time() - start_time,
                    'source': 'cache',
                    'success': True
                }
        
        # 执行实际更新
        try:
            if self.intelligent_model and not force_full:
                # 使用智能模型进行快速更新
                qot_value, confidence = self._intelligent_update(lightpath)
            else:
                # 使用增量计算
                qot_value, confidence = self._incremental_update(lightpath)
            
            # 更新光路信息
            lightpath.current_qot = qot_value
            
            # 缓存结果
            self.cache.put(lightpath_id, qot_value, dependency_hash, confidence)
            
            # 更新统计
            update_time = time.time() - start_time
            self.update_stats['successful_updates'] += 1
            self.update_stats['total_updates'] += 1
            self._update_avg_time(update_time)
            
            return {
                'lightpath_id': lightpath_id,
                'qot_value': qot_value,
                'confidence': confidence,
                'update_time': update_time,
                'source': 'calculation',
                'success': True
            }
            
        except Exception as e:
            self.update_stats['failed_updates'] += 1
            self.update_stats['total_updates'] += 1
            
            return {
                'lightpath_id': lightpath_id,
                'error': str(e),
                'update_time': time.time() - start_time,
                'success': False
            }
    
    def execute_batch_update(self, lightpath_ids: List[int]) -> List[Dict]:
        """
        执行批量QoT更新
        
        Args:
            lightpath_ids: 光路ID列表
            
        Returns:
            更新结果列表
        """
        print(f"🔄 执行批量更新: {len(lightpath_ids)} 条光路")
        
        start_time = time.time()
        
        # 并行执行更新
        futures = []
        for lp_id in lightpath_ids:
            future = self.executor.submit(self.execute_immediate_update, lp_id)
            futures.append((lp_id, future))
        
        # 收集结果
        results = []
        for lp_id, future in futures:
            try:
                result = future.result(timeout=30)  # 30秒超时
                results.append(result)
            except Exception as e:
                results.append({
                    'lightpath_id': lp_id,
                    'error': f'批量更新超时: {str(e)}',
                    'success': False
                })
        
        # 更新统计
        batch_time = time.time() - start_time
        self.update_stats['batch_updates'] += 1
        
        print(f"✅ 批量更新完成: {len(results)} 个结果, 耗时 {batch_time:.3f}s")
        
        return results
    
    def _background_updater(self):
        """后台更新线程"""
        print("🔄 启动后台QoT更新线程")
        
        batch_tasks = []
        last_batch_time = time.time()
        
        while self.running:
            try:
                # 获取更新任务（带超时）
                try:
                    priority, timestamp, task = self.update_queue.get(timeout=self.update_config['update_interval'])
                    batch_tasks.append(task)
                except queue.Empty:
                    pass
                
                current_time = time.time()
                
                # 检查是否应该执行批量更新
                should_batch = (
                    len(batch_tasks) >= self.update_config['batch_size'] or
                    (batch_tasks and current_time - last_batch_time > self.update_config['update_interval'] * 10)
                )
                
                if should_batch and batch_tasks:
                    # 按优先级排序任务
                    batch_tasks.sort(key=lambda t: t.priority)
                    
                    # 执行批量更新
                    lightpath_ids = [task.lightpath_id for task in batch_tasks]
                    self.execute_batch_update(lightpath_ids)
                    
                    # 清空批次
                    batch_tasks.clear()
                    last_batch_time = current_time
                
            except Exception as e:
                print(f"❌ 后台更新线程错误: {e}")
                time.sleep(1)
        
        print("🛑 后台QoT更新线程停止")
    
    def _determine_priority(self, impact_result: ImpactAnalysisResult) -> int:
        """确定更新优先级"""
        impact_prob = impact_result.impact_probability
        confidence = impact_result.confidence_score
        
        # 综合影响概率和置信度确定优先级
        weighted_score = impact_prob * confidence
        
        if weighted_score > self.update_config['high_priority_threshold']:
            return 1  # 高优先级
        elif weighted_score > self.update_config['medium_priority_threshold']:
            return 2  # 中优先级
        else:
            return 3  # 低优先级
    
    def _generate_dependency_hash(self, lightpath: LightpathInfo) -> str:
        """生成依赖哈希值"""
        # 基于影响该光路的其他光路生成哈希
        affecting_lightpaths = []
        
        for other_id, other_lp in self.impact_detector.active_lightpaths.items():
            if other_id != lightpath.id:
                # 简化的影响关系判断
                wavelength_close = abs(lightpath.wavelength - other_lp.wavelength) <= 5
                path_overlap = self._calculate_path_overlap(lightpath.path, other_lp.path) > 0.1
                
                if wavelength_close and path_overlap:
                    affecting_lightpaths.append(f"{other_id}:{other_lp.wavelength}:{other_lp.power_dbm}")
        
        # 生成哈希
        dependency_str = "|".join(sorted(affecting_lightpaths))
        return str(hash(dependency_str))
    
    def _calculate_path_overlap(self, path1: List[int], path2: List[int]) -> float:
        """计算路径重叠比例"""
        if not path1 or not path2:
            return 0.0
        
        edges1 = set()
        edges2 = set()
        
        for i in range(len(path1) - 1):
            edges1.add(tuple(sorted([path1[i], path1[i+1]])))
        
        for i in range(len(path2) - 1):
            edges2.add(tuple(sorted([path2[i], path2[i+1]])))
        
        if not edges2:
            return 0.0
        
        overlap = len(edges1.intersection(edges2))
        return overlap / len(edges2.union(edges1))
    
    def _intelligent_update(self, lightpath: LightpathInfo) -> Tuple[float, float]:
        """使用智能模型进行QoT更新"""
        # 构建节点特征
        node_features = self._build_node_features()
        node_features_tensor = torch.tensor(node_features, dtype=torch.float32).to(self.device)
        
        # 构建光路节点
        lightpath_nodes = [lightpath.source, lightpath.destination]
        
        # 找一个参考光路（如果存在）
        reference_nodes = lightpath_nodes  # 简化处理
        
        with torch.no_grad():
            _, qot_prediction = self.intelligent_model(
                self.network_graph.to(self.device), node_features_tensor,
                lightpath_nodes, reference_nodes
            )
            
            predicted_qot = qot_prediction.item()
            
            # 简单的置信度估计
            confidence = min(1.0, abs(predicted_qot) / 10.0 + 0.5)
        
        return predicted_qot, confidence
    
    def _incremental_update(self, lightpath: LightpathInfo) -> Tuple[float, float]:
        """增量式QoT更新"""
        # 构建光路特征
        lightpath_features = torch.tensor([
            lightpath.power_dbm,
            float(lightpath.wavelength),
            len(lightpath.path),
            lightpath.bitrate_gbps
        ], dtype=torch.float32)
        
        # 构建影响特征（基于当前网络状态）
        impact_features = torch.tensor([
            self._get_current_congestion(lightpath),
            self._get_wavelength_usage(lightpath.wavelength),
            self._get_power_interference(lightpath),
            self._get_path_utilization(lightpath.path)
        ], dtype=torch.float32)
        
        # 使用增量计算器
        qot_change, uncertainty = self.incremental_calculator.calculate_incremental_change(
            lightpath_features, impact_features
        )
        
        # 基于历史QoT值计算新值
        base_qot = lightpath.current_qot if lightpath.current_qot > 0 else 25.0
        new_qot = base_qot + qot_change
        
        confidence = 1.0 - uncertainty
        
        return new_qot, confidence
    
    def _build_node_features(self) -> List[List[float]]:
        """构建节点特征"""
        num_nodes = self.network_graph.num_nodes()
        features = []
        
        for node_id in range(num_nodes):
            node_features = [
                float(self.network_graph.in_degrees()[node_id].item()),  # 度数
                self.impact_detector.network_state['power_levels'].get(node_id, 0.0),  # 功率
                0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0  # 其他特征（简化）
            ]
            features.append(node_features)
        
        return features
    
    def _get_current_congestion(self, lightpath: LightpathInfo) -> float:
        """获取当前拥塞水平"""
        congestion = 0.0
        for i in range(len(lightpath.path) - 1):
            node1, node2 = lightpath.path[i], lightpath.path[i+1]
            congestion += self.impact_detector.network_state['congestion_matrix'][node1][node2]
        
        return congestion / max(len(lightpath.path) - 1, 1)
    
    def _get_wavelength_usage(self, wavelength: int) -> float:
        """获取波长使用情况"""
        wavelength_edges = self.impact_detector.network_state['wavelength_usage'].get(wavelength, set())
        total_edges = self.network_graph.num_edges() // 2  # 无向图
        
        return len(wavelength_edges) / max(total_edges, 1)
    
    def _get_power_interference(self, lightpath: LightpathInfo) -> float:
        """获取功率干扰水平"""
        interference = 0.0
        
        for other_lp in self.impact_detector.active_lightpaths.values():
            if other_lp.id != lightpath.id:
                power_diff = abs(lightpath.power_dbm - other_lp.power_dbm)
                path_overlap = self._calculate_path_overlap(lightpath.path, other_lp.path)
                interference += path_overlap / (1.0 + power_diff * 0.1)
        
        return interference
    
    def _get_path_utilization(self, path: List[int]) -> float:
        """获取路径利用率"""
        utilization = 0.0
        
        for i in range(len(path) - 1):
            node1, node2 = path[i], path[i+1]
            edge_usage = 0
            
            # 统计该边上的光路数量
            for lp in self.impact_detector.active_lightpaths.values():
                for j in range(len(lp.path) - 1):
                    if ((lp.path[j] == node1 and lp.path[j+1] == node2) or
                        (lp.path[j] == node2 and lp.path[j+1] == node1)):
                        edge_usage += 1
            
            utilization += edge_usage
        
        return utilization / max(len(path) - 1, 1)
    
    def _update_avg_time(self, new_time: float):
        """更新平均更新时间"""
        total_updates = self.update_stats['total_updates']
        current_avg = self.update_stats['avg_update_time']
        
        # 增量平均计算
        self.update_stats['avg_update_time'] = (
            (current_avg * (total_updates - 1) + new_time) / total_updates
        )
    
    def get_update_statistics(self) -> Dict:
        """获取更新统计信息"""
        cache_stats = self.cache.get_stats()
        
        return {
            'update_stats': self.update_stats.copy(),
            'cache_stats': cache_stats,
            'queue_size': self.update_queue.qsize(),
            'active_lightpaths': len(self.impact_detector.active_lightpaths),
            'update_config': self.update_config.copy()
        }
    
    def optimize_configuration(self):
        """基于运行统计优化配置"""
        stats = self.get_update_statistics()
        
        # 基于缓存命中率调整缓存策略
        hit_rate = stats['cache_stats']['hit_rate']
        if hit_rate < 0.3:  # 命中率低
            self.cache.ttl = min(600, self.cache.ttl * 1.2)  # 增加TTL
        elif hit_rate > 0.8:  # 命中率高
            self.cache.ttl = max(60, self.cache.ttl * 0.9)   # 减少TTL
        
        # 基于平均更新时间调整批量大小
        avg_time = stats['update_stats']['avg_update_time']
        if avg_time > 1.0:  # 更新太慢
            self.update_config['batch_size'] = max(4, self.update_config['batch_size'] - 2)
        elif avg_time < 0.1:  # 更新很快
            self.update_config['batch_size'] = min(32, self.update_config['batch_size'] + 2)
        
        print(f"🔧 配置优化完成: 批量大小={self.update_config['batch_size']}, 缓存TTL={self.cache.ttl}")
    
    def shutdown(self):
        """优雅关闭更新器"""
        print("🛑 关闭QoT更新器...")
        
        self.running = False
        
        # 等待后台线程结束
        if self.update_thread.is_alive():
            self.update_thread.join(timeout=5)
        
        # 关闭线程池
        self.executor.shutdown(wait=True)
        
        print("✅ QoT更新器已关闭")

def test_optimized_qot_updater():
    """测试优化的QoT更新器"""
    print("🧪 测试优化QoT更新器")
    print("=" * 50)
    
    # 创建测试网络和组件
    u = torch.tensor([0,0,1,2,3,3,4,4,4,5,6,6,7,8,11,9,8,13,11,11,13,10])
    v = torch.tensor([1,2,3,4,4,5,5,8,6,7,7,11,9,11,9,10,13,11,10,12,12,12])
    test_graph = dgl.graph((u, v))
    test_graph = dgl.to_bidirected(test_graph)
    
    from dynamic_lightpath_impact_detector import DynamicImpactDetector
    impact_detector = DynamicImpactDetector(test_graph)
    
    # 创建优化更新器
    updater = OptimizedQoTUpdater(test_graph, impact_detector, max_workers=2)
    
    # 添加测试光路
    test_lightpaths = [
        LightpathInfo(
            id=1, source=0, destination=5, path=[0, 1, 3, 4, 5],
            wavelength=40, power_dbm=0.0, modulation='16QAM',
            bitrate_gbps=100, current_qot=25.5, establishment_time=time.time()
        ),
        LightpathInfo(
            id=2, source=2, destination=6, path=[2, 3, 4, 6],
            wavelength=42, power_dbm=1.0, modulation='16QAM',
            bitrate_gbps=100, current_qot=24.8, establishment_time=time.time()
        )
    ]
    
    # 添加光路到检测器
    for lp in test_lightpaths:
        impact_detector.active_lightpaths[lp.id] = lp
    
    print(f"\n🔄 测试立即更新...")
    # 测试立即更新
    result1 = updater.execute_immediate_update(1)
    print(f"更新结果1: {result1}")
    
    result2 = updater.execute_immediate_update(2)
    print(f"更新结果2: {result2}")
    
    print(f"\n🔄 测试批量更新...")
    # 测试批量更新
    batch_results = updater.execute_batch_update([1, 2])
    print(f"批量更新结果: {len(batch_results)} 个")
    
    # 等待一段时间观察后台更新
    print(f"\n⏳ 等待后台更新...")
    time.sleep(2)
    
    # 显示统计信息
    stats = updater.get_update_statistics()
    print(f"\n📊 更新器统计:")
    print(f"  总更新次数: {stats['update_stats']['total_updates']}")
    print(f"  成功更新: {stats['update_stats']['successful_updates']}")
    print(f"  缓存更新: {stats['update_stats']['cached_updates']}")
    print(f"  平均更新时间: {stats['update_stats']['avg_update_time']:.4f}s")
    print(f"  缓存命中率: {stats['cache_stats']['hit_rate']:.3f}")
    
    # 优化配置
    updater.optimize_configuration()
    
    # 关闭更新器
    updater.shutdown()
    
    print(f"\n✅ 优化QoT更新器测试完成!")

if __name__ == "__main__":
    test_optimized_qot_updater()