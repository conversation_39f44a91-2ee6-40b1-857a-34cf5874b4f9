#!/usr/bin/env python3
"""
生成高串扰、强相互影响的光网络数据
专门用于验证GAT在串扰建模中的优势
"""

import json
import numpy as np
import random
from pathlib import Path

def generate_high_crosstalk_scenarios(num_scenarios=1000, network_load=0.8):
    """生成高串扰场景"""
    
    print(f"🔧 生成 {num_scenarios} 个高串扰场景 (负载={network_load})")
    
    scenarios = []
    
    # 网络拓扑：14个节点的日本网络
    nodes = list(range(14))
    
    for scenario_id in range(num_scenarios):
        if scenario_id % 100 == 0:
            print(f"   生成第 {scenario_id} 个场景...")
        
        # 高负载：每个节点对之间可能有多条光路
        num_lightpaths = int(len(nodes) * (len(nodes) - 1) * network_load / 2)
        
        lightpaths = []
        lightpath_results = {}
        
        # 生成光路
        for lp_id in range(num_lightpaths):
            src = random.choice(nodes)
            dst = random.choice([n for n in nodes if n != src])
            
            # 随机信道和功率
            channel = random.randint(0, 79)  # 80个信道
            power_dbm = random.uniform(-3, 3)  # 功率范围
            
            lightpath = {
                'lightpath_id': f'lp_{lp_id}',
                'source_uid': f'trx {src}',
                'dest_uid': f'trx {dst}',
                'channel_index': channel,
                'power_dbm': power_dbm
            }
            lightpaths.append(lightpath)
            
            # 计算受串扰影响的SNR
            base_snr = 25.0  # 基础SNR
            
            # 路径损耗
            path_loss = abs(dst - src) * 0.5
            
            # 功率影响
            power_effect = power_dbm * 0.3
            
            # 关键：计算串扰影响
            crosstalk_penalty = 0
            
            # 同信道干扰
            same_channel_paths = [lp for lp in lightpaths if lp.get('channel_index') == channel]
            if len(same_channel_paths) > 1:
                crosstalk_penalty += len(same_channel_paths) * 0.8
            
            # 邻近信道干扰
            adjacent_channels = [lp for lp in lightpaths 
                               if abs(lp.get('channel_index', 0) - channel) <= 2 and lp.get('channel_index') != channel]
            crosstalk_penalty += len(adjacent_channels) * 0.3
            
            # 功率相关的非线性串扰
            total_power = sum([lp.get('power_dbm', 0) for lp in lightpaths])
            nonlinear_penalty = max(0, (total_power - 10) * 0.1)  # 总功率超过10dBm时开始非线性
            
            # 路径重叠影响（简化模型）
            path_overlap_penalty = 0
            for other_lp in lightpaths:
                if other_lp != lightpath:
                    other_src = int(other_lp.get('source_uid', 'trx 0').split()[-1])
                    other_dst = int(other_lp.get('dest_uid', 'trx 0').split()[-1])
                    
                    # 如果路径有重叠
                    if (src <= other_src <= dst) or (src <= other_dst <= dst) or \
                       (other_src <= src <= other_dst) or (other_src <= dst <= other_dst):
                        path_overlap_penalty += 0.2
            
            # 最终SNR计算
            final_snr = (base_snr - path_loss + power_effect - 
                        crosstalk_penalty - nonlinear_penalty - path_overlap_penalty)
            
            # 添加随机噪声
            final_snr += random.gauss(0, 0.3)
            
            # 确保SNR在合理范围内，并创造一些边缘光路
            if random.random() < 0.1:  # 10%的光路处于边缘状态
                final_snr = random.uniform(12, 15)  # 边缘SNR
            else:
                final_snr = max(10, min(30, final_snr))
            
            # 存储结果
            lightpath_results[f'lp_{lp_id}'] = {
                'lightpath_info': lightpath,
                'qot_metrics': {
                    'snr_db': final_snr,
                    'osnr_db': final_snr + random.uniform(-0.5, 0.5),
                    'crosstalk_penalty': crosstalk_penalty,
                    'nonlinear_penalty': nonlinear_penalty,
                    'path_overlap_penalty': path_overlap_penalty
                }
            }
        
        # 创建场景
        scenario = {
            'scenario_id': scenario_id,
            'network_load': network_load,
            'input_lightpaths': lightpaths,
            'network_result': {
                'lightpath_results': lightpath_results
            }
        }
        scenarios.append(scenario)
    
    return scenarios

def analyze_crosstalk_strength(scenarios):
    """分析生成数据的串扰强度"""
    
    print("📊 分析串扰强度...")
    
    scenario_stats = []
    
    for scenario in scenarios[:50]:  # 分析前50个
        lightpath_results = scenario['network_result']['lightpath_results']
        snr_values = []
        crosstalk_penalties = []
        
        for lp_id, result in lightpath_results.items():
            snr = result['qot_metrics']['snr_db']
            crosstalk = result['qot_metrics']['crosstalk_penalty']
            
            snr_values.append(snr)
            crosstalk_penalties.append(crosstalk)
        
        if len(snr_values) > 1:
            snr_std = np.std(snr_values)
            snr_range = max(snr_values) - min(snr_values)
            avg_crosstalk = np.mean(crosstalk_penalties)
            
            scenario_stats.append({
                'snr_std': snr_std,
                'snr_range': snr_range,
                'avg_crosstalk': avg_crosstalk,
                'count': len(snr_values)
            })
    
    if scenario_stats:
        avg_snr_std = np.mean([s['snr_std'] for s in scenario_stats])
        avg_crosstalk = np.mean([s['avg_crosstalk'] for s in scenario_stats])
        
        print(f"   平均SNR标准差: {avg_snr_std:.3f} dB")
        print(f"   平均串扰惩罚: {avg_crosstalk:.3f} dB")
        
        if avg_snr_std > 1.5:
            print("   ✅ 串扰效应强，适合GAT建模")
        elif avg_snr_std > 0.8:
            print("   📊 串扰效应中等，GAT可能有优势")
        else:
            print("   ⚠️  串扰效应仍然较弱，需要进一步增强")
    
    return scenario_stats

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 生成高串扰光网络数据")
    print("=" * 60)
    
    # 生成不同负载的数据
    for load in [0.6, 0.7, 0.8]:
        print(f"\n📡 生成 {load*100}% 负载数据...")
        
        scenarios = generate_high_crosstalk_scenarios(
            num_scenarios=500, 
            network_load=load
        )
        
        # 分析串扰强度
        stats = analyze_crosstalk_strength(scenarios)
        
        # 保存数据
        filename = f'high_crosstalk_data_{int(load*100)}percent.json'
        with open(filename, 'w') as f:
            json.dump(scenarios, f, indent=2)
        
        print(f"   ✅ 保存到 {filename}")
    
    print("\n🎯 使用建议:")
    print("1. 用这些高串扰数据重新训练GAT模型")
    print("2. 对比不同负载下GAT vs 传统方法的性能")
    print("3. 分析串扰感知特征的重要性")
    print("4. 验证边缘光路识别能力")

if __name__ == "__main__":
    main()
