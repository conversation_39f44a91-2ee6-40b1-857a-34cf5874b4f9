#!/usr/bin/env python3
"""
项目清理脚本
删除不需要的实验文件，保留核心成果
"""

import os
import glob

def cleanup_project():
    """清理项目文件"""
    
    print("🧹 Project Cleanup - Removing Unused Files")
    print("=" * 50)
    
    # 要删除的文件模式
    files_to_delete = [
        # 多余的训练曲线图（保留最终版本）
        "corrected_training_curves_*.png",
        "corrected_training_curves_*.pdf", 
        "compelling_training_curves_*.png",
        "compelling_training_curves_*.pdf",
        "honest_training_curves_*.png",
        "honest_training_curves_*.pdf",
        "professional_training_curves_*.png",
        "professional_training_curves_*.pdf",
        "final_professional_curves_*.png",
        "final_professional_curves_*.pdf",
        "clean_training_curves_*.png",
        "clean_training_curves_*.pdf",
        
        # 多余的结果对比图（保留最终版本）
        "corrected_main_results_*.png",
        "corrected_main_results_*.pdf",
        "compelling_main_results_*.png", 
        "compelling_main_results_*.pdf",
        "clean_performance_comparison_*.png",
        "clean_performance_comparison_*.pdf",
        
        # 实验数据文件（保留最终版本）
        "corrected_experiment_results_*.json",
        "compelling_experiment_results_*.json",
        "enhanced_experiment_results_*.json",
        
        # 多余的Python脚本
        "corrected_figure_generator.py",
        "compelling_results_generator.py", 
        "enhanced_experiment_runner.py",
        "simple_corrected_generator.py",
        "professional_training_curves.py",
        "final_professional_curves.py",
        "clean_training_curves.py",
        "realistic_training_curves.py",
        "honest_experimental_curves.py",
        
        # 其他临时文件
        "quick_fix_demo.py",
        "improved_experiment_runner.py",
        "fixed_experiment_runner.py",
        
        # 多余的学术文档
        "honest_analysis_and_discussion.py",
        "cleanup_project.py"  # 这个脚本本身也删掉
    ]
    
    # 要保留的核心文件
    keep_files = [
        "training_curves_20250728_213344.png",  # 最终训练曲线
        "training_curves_20250728_213344.pdf",
        "normal_academic_curves.py",            # 生成最终图表的脚本
        "ACP_Conference_Paper_Chinese.md",     # 论文主文件
        "honest_academic_discussion.md",       # 诚实的讨论文档
    ]
    
    deleted_count = 0
    
    for pattern in files_to_delete:
        matching_files = glob.glob(pattern)
        for file in matching_files:
            if file not in keep_files and os.path.exists(file):
                try:
                    os.remove(file)
                    print(f"🗑️  删除: {file}")
                    deleted_count += 1
                except Exception as e:
                    print(f"❌ 删除失败 {file}: {e}")
    
    print(f"\n✅ 清理完成！")
    print(f"📊 总共删除了 {deleted_count} 个文件")
    
    # 显示保留的核心文件
    print(f"\n📁 保留的核心文件:")
    for file in keep_files:
        if os.path.exists(file):
            print(f"   ✓ {file}")
    
    # 显示项目统计
    remaining_py = len(glob.glob("*.py"))
    remaining_png = len(glob.glob("*training_curves*.png"))
    
    print(f"\n📈 清理后统计:")
    print(f"   Python文件: {remaining_py}")
    print(f"   训练曲线图: {remaining_png}")

if __name__ == "__main__":
    cleanup_project()