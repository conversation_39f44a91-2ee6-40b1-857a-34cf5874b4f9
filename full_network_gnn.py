import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.data import DataLoader
from torch_geometric.nn import GCNConv, global_mean_pool
import matplotlib.pyplot as plt
import numpy as np

# 1. 定义真正的GNN模型
class FullNetworkGNN(nn.Module):
    def __init__(self, num_node_features, num_channels, embedding_dim=64, gcn_hidden_dim=128):
        super(FullNetworkGNN, self).__init__()
        
        # GCN层用于学习节点在拓扑中的表示
        self.conv1 = GCNConv(num_node_features, gcn_hidden_dim)
        self.conv2 = GCNConv(gcn_hidden_dim, gcn_hidden_dim)
        
        # 边（流量）特征的编码器
        self.edge_encoder = nn.Linear(num_channels, embedding_dim)
        
        # 路径节点和全局图表示的组合器
        self.combiner = nn.Linear(gcn_hidden_dim + embedding_dim, gcn_hidden_dim)
        
        # 最终的回归器
        self.regressor = nn.Sequential(
            nn.Linear(gcn_hidden_dim + 2, gcn_hidden_dim), # +2 for channel and power
            nn.ReLU(),
            nn.Linear(gcn_hidden_dim, 1)
        )

    def forward(self, data):
        x, edge_index, edge_attr = data.x, data.edge_index, data.edge_attr
        
        # 1. 通过GCN层传播节点特征
        x = self.conv1(x, edge_index)
        x = F.relu(x)
        x = self.conv2(x, edge_index)
        
        # 2. 从全图中提取目标路径节点的表示
        # The 'target_path_nodes_batch' attribute is now automatically created by the DataLoader
        path_nodes_features = x[data.target_path_nodes]
        
        # 3. 对路径上节点的特征进行平均池化，得到路径的整体表示
        path_embedding = global_mean_pool(path_nodes_features, data.target_path_nodes_batch)
        
        # 4. 对边（流量）特征进行编码和池化，得到网络的流量状态表示
        encoded_edge_attr = self.edge_encoder(edge_attr)
        traffic_embedding = global_mean_pool(encoded_edge_attr, data.batch[edge_index[0]])
        
        # 5. 结合路径表示和流量表示
        combined_graph_features = torch.cat([path_embedding, traffic_embedding], dim=1)
        combined_graph_features = F.relu(self.combiner(combined_graph_features))
        
        # 6. 加入目标信道和功率信息
        # 归一化信道和功率以获得更好的性能
        target_channel_norm = data.target_channel.unsqueeze(1) / 80.0
        target_power_norm = data.target_power.unsqueeze(1) / 5.0 # 假设功率在-2到2之间
        
        final_features = torch.cat([combined_graph_features, target_channel_norm, target_power_norm], dim=1)
        
        # 7. 预测SNR
        return self.regressor(final_features)

# 2. 准备数据加载器
def create_dataloaders(data_file, batch_size):
    print(f"正在从 '{data_file}' 加载数据...")
    dataset = torch.load(data_file)
    # FIX: Use the 'follow_batch' argument to tell the loader to create a batch index
    # for our custom 'target_path_nodes' attribute. This solves the AttributeError.
    train_loader = DataLoader(dataset['train'], batch_size=batch_size, shuffle=True, follow_batch=['target_path_nodes'])
    val_loader = DataLoader(dataset['val'], batch_size=batch_size, shuffle=False, follow_batch=['target_path_nodes'])
    print("数据加载器已创建。")
    return train_loader, val_loader, dataset

# 3. 训练和评估循环
def train_and_evaluate(model, train_loader, val_loader, epochs, learning_rate):
    from sklearn.metrics import r2_score
    
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate, weight_decay=1e-5)
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=10, verbose=True)
    
    train_losses, val_losses = [], []
    train_r2s, val_r2s = [], []
    best_val_loss = float('inf')
    
    # 检查数据范围
    print("🔍 检查数据范围...")
    sample_data = next(iter(train_loader))
    print(f"   目标值范围: {sample_data.y.min().item():.2f} - {sample_data.y.max().item():.2f}")
    
    for epoch in range(epochs):
        model.train()
        total_train_loss = 0
        train_preds, train_targets = [], []
        
        for data in train_loader:
            optimizer.zero_grad()
            predictions = model(data)
            loss = criterion(predictions, data.y.view(-1, 1))
            
            # 检查损失是否异常
            if torch.isnan(loss) or torch.isinf(loss):
                print(f"⚠️  检测到异常损失: {loss.item()}")
                continue
                
            loss.backward()
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            total_train_loss += loss.item()
            train_preds.extend(predictions.view(-1).detach().cpu().numpy())
            train_targets.extend(data.y.view(-1).cpu().numpy())
        
        avg_train_loss = total_train_loss / len(train_loader)
        train_losses.append(avg_train_loss)
        
        model.eval()
        total_val_loss = 0
        val_preds, val_targets = [], []
        
        with torch.no_grad():
            for data in val_loader:
                predictions = model(data)
                loss = criterion(predictions, data.y.view(-1, 1))
                total_val_loss += loss.item()
                val_preds.extend(predictions.view(-1).cpu().numpy())
                val_targets.extend(data.y.view(-1).cpu().numpy())
        
        avg_val_loss = total_val_loss / len(val_loader)
        val_losses.append(avg_val_loss)
        
        # 计算R²分数
        train_r2 = r2_score(train_targets, train_preds) if len(train_targets) > 0 else -1
        val_r2 = r2_score(val_targets, val_preds) if len(val_targets) > 0 else -1
        train_r2s.append(train_r2)
        val_r2s.append(val_r2)
        
        # 学习率调度
        scheduler.step(avg_val_loss)
        
        print(f"Epoch {epoch+1}/{epochs} | Train Loss: {avg_train_loss:.4f}, R²: {train_r2:.4f} | "
              f"Val Loss: {avg_val_loss:.4f}, R²: {val_r2:.4f} | RMSE: {np.sqrt(avg_val_loss):.4f}")
        
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            torch.save(model.state_dict(), 'best_full_network_gnn.pth')
            print(f"  -> 模型已保存，验证RMSE为 {np.sqrt(best_val_loss):.4f}。")
            
    return {
        'train_losses': train_losses,
        'val_losses': val_losses, 
        'train_r2s': train_r2s,
        'val_r2s': val_r2s
    }

# 4. 绘图函数
def plot_losses(history):
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 损失曲线
    ax1.plot(history['train_losses'], label='Training Loss')
    ax1.plot(history['val_losses'], label='Validation Loss')
    ax1.set_title('FullNetworkGNN - Loss vs. Epochs')
    ax1.set_xlabel('Epochs')
    ax1.set_ylabel('MSE Loss')
    ax1.legend()
    ax1.grid(True)
    
    # R²曲线
    ax2.plot(history['train_r2s'], label='Training R²')
    ax2.plot(history['val_r2s'], label='Validation R²')
    ax2.set_title('FullNetworkGNN - R² vs. Epochs')
    ax2.set_xlabel('Epochs')
    ax2.set_ylabel('R² Score')
    ax2.legend()
    ax2.grid(True)
    
    plt.tight_layout()
    plt.savefig('FullNetworkGNN_training_curves.png')
    print("训练曲线图已保存为 'FullNetworkGNN_training_curves.png'")

# 5. 主执行函数
if __name__ == '__main__':
    # 超参数
    DATA_FILE = 'processed_scenarios.pt'
    BATCH_SIZE = 16 # 由于图数据更复杂，减小batch size
    EPOCHS = 200
    LEARNING_RATE = 0.0005

    # 准备数据
    train_loader, val_loader, dataset_info = create_dataloaders(DATA_FILE, BATCH_SIZE)
    
    # 初始化模型 - 使用 num_node_features
    model = FullNetworkGNN(
        num_node_features=dataset_info['num_node_features'],
        num_channels=dataset_info['num_channels']
    )
    print("\n模型结构:")
    print(model)
    
    # 训练模型
    print("\n开始训练...")
    history = train_and_evaluate(model, train_loader, val_loader, EPOCHS, LEARNING_RATE)
    
    # 绘制结果
    plot_losses(history)
