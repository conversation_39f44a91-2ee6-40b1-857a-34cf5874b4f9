import torch
import torch.nn as nn
import torch.nn.functional as F
import dgl
import numpy as np
import json
import pickle
from torch.utils.data import Dataset, DataLoader
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.preprocessing import StandardScaler, RobustScaler
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')
import warnings
warnings.filterwarnings('ignore')

class AdvancedSubgraphDataset(Dataset):
    """高级子图数据集，包含更好的特征工程和数据增强"""
    
    def __init__(self, data_file, scaler=None, augment=False, noise_level=0.01):
        with open(data_file, 'rb') as f:
            self.data = pickle.load(f)
        
        self.augment = augment
        self.noise_level = noise_level
        self.scaler = scaler
        
        # 提取增强特征
        self.features = []
        self.targets = []
        self.subgraphs = []
        
        for item in self.data:
            features = self._extract_comprehensive_features(item)
            self.features.append(features)
            self.targets.append(item['qot'])
            self.subgraphs.append(item['subgraph'])
        
        self.features = np.array(self.features)
        self.targets = np.array(self.targets)
        
        # 使用RobustScaler，对异常值更鲁棒
        if self.scaler is None:
            self.scaler = RobustScaler()
            self.features = self.scaler.fit_transform(self.features)
        else:
            self.features = self.scaler.transform(self.features)
    
    def _extract_comprehensive_features(self, item):
        """全面的特征提取"""
        features = []
        
        # 基础路径特征
        path_length = item['path_length']
        num_spans = item['num_spans']
        path_nodes = len(item['path'])
        
        features.extend([
            path_length,
            num_spans,
            path_nodes,
            path_length / max(1, num_spans),  # 平均跨距长度
            path_length / max(1, path_nodes),  # 平均节点间距离
            item.get('total_power', 0),
            item.get('launch_power', 0)
        ])
        
        # 高级子图特征
        subgraph = item['subgraph']
        num_nodes = subgraph.number_of_nodes()
        num_edges = subgraph.number_of_edges()
        
        # 图的结构特征
        if num_nodes > 0:
            in_degrees = [subgraph.in_degrees(i).item() for i in range(num_nodes)]
            out_degrees = [subgraph.out_degrees(i).item() for i in range(num_nodes)]
            
            features.extend([
                num_nodes,
                num_edges,
                num_edges / max(1, num_nodes),  # 边节点比
                np.mean(in_degrees),
                np.std(in_degrees) if len(in_degrees) > 1 else 0,
                np.max(in_degrees) if in_degrees else 0,
                np.mean(out_degrees),
                np.std(out_degrees) if len(out_degrees) > 1 else 0,
                np.max(out_degrees) if out_degrees else 0,
                num_nodes / max(1, path_nodes),  # 子图覆盖率
            ])
        else:
            features.extend([0] * 10)
        
        # 物理层增强特征
        if 'span_losses' in item and item['span_losses']:
            losses = np.array(item['span_losses'])
            features.extend([
                np.mean(losses),
                np.std(losses),
                np.max(losses),
                np.min(losses),
                np.median(losses),
                np.percentile(losses, 75) - np.percentile(losses, 25),  # IQR
                np.sum(losses),  # 总损耗
                len(losses)  # 跨距数量
            ])
        else:
            features.extend([0] * 8)
        
        # 频率和调制格式特征
        features.extend([
            item.get('frequency', 193.1),
            item.get('baud_rate', 32),
            item.get('roll_off', 0.2)
        ])
        
        # 网络拓扑特征
        if 'node_types' in item:
            node_types = item['node_types']
            features.extend([
                node_types.count('roadm') if node_types else 0,
                node_types.count('edfa') if node_types else 0,
                len(node_types) if node_types else 0
            ])
        else:
            features.extend([0, 0, 0])
        
        return features
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        features = torch.FloatTensor(self.features[idx])
        target = torch.FloatTensor([self.targets[idx]])
        subgraph = self.subgraphs[idx]
        
        # 智能数据增强
        if self.augment and np.random.random() < 0.2:
            # 特征噪声
            noise = torch.randn_like(features) * self.noise_level
            features = features + noise
            
            # 目标值微调（模拟测量不确定性）
            target_noise = torch.randn_like(target) * 0.1
            target = target + target_noise
        
        return subgraph, features, target

class GraphTransformerLayer(nn.Module):
    """图Transformer层"""
    
    def __init__(self, hidden_dim, num_heads=8, dropout=0.1):
        super().__init__()
        self.hidden_dim = hidden_dim
        self.num_heads = num_heads
        self.head_dim = hidden_dim // num_heads
        
        self.q_proj = nn.Linear(hidden_dim, hidden_dim)
        self.k_proj = nn.Linear(hidden_dim, hidden_dim)
        self.v_proj = nn.Linear(hidden_dim, hidden_dim)
        self.out_proj = nn.Linear(hidden_dim, hidden_dim)
        
        self.norm1 = nn.LayerNorm(hidden_dim)
        self.norm2 = nn.LayerNorm(hidden_dim)
        
        self.ffn = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 4),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 4, hidden_dim),
            nn.Dropout(dropout)
        )
        
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x, adj_mask=None):
        # Multi-head self-attention
        residual = x
        x = self.norm1(x)
        
        batch_size, seq_len, _ = x.shape
        
        q = self.q_proj(x).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        k = self.k_proj(x).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        v = self.v_proj(x).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        
        scores = torch.matmul(q, k.transpose(-2, -1)) / np.sqrt(self.head_dim)
        
        if adj_mask is not None:
            scores = scores.masked_fill(adj_mask.unsqueeze(1) == 0, float('-inf'))
        
        attn = F.softmax(scores, dim=-1)
        attn = self.dropout(attn)
        
        out = torch.matmul(attn, v).transpose(1, 2).contiguous().view(batch_size, seq_len, self.hidden_dim)
        out = self.out_proj(out)
        out = residual + self.dropout(out)
        
        # Feed-forward
        residual = out
        out = self.norm2(out)
        out = self.ffn(out)
        out = residual + out
        
        return out

class AdvancedSubgraphGNN(nn.Module):
    """高级子图GNN模型"""
    
    def __init__(self, feature_dim, hidden_dim=128, num_layers=4, num_heads=8, dropout=0.1):
        super().__init__()
        
        self.hidden_dim = hidden_dim
        
        # 节点特征编码器
        self.node_encoder = nn.Sequential(
            nn.Linear(1, hidden_dim // 2),
            nn.LayerNorm(hidden_dim // 2),
            nn.GELU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(hidden_dim // 2, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU()
        )
        
        # 图Transformer层
        self.graph_layers = nn.ModuleList([
            GraphTransformerLayer(hidden_dim, num_heads, dropout)
            for _ in range(num_layers)
        ])
        
        # 多尺度图池化
        self.global_pool = nn.Sequential(
            nn.Linear(hidden_dim * 3, hidden_dim),  # mean + max + attention
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout)
        )
        
        # 注意力池化
        self.attention_pool = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.Tanh(),
            nn.Linear(hidden_dim // 2, 1)
        )
        
        # 特征融合网络
        self.feature_encoder = nn.Sequential(
            nn.Linear(feature_dim, hidden_dim // 2),
            nn.LayerNorm(hidden_dim // 2),
            nn.GELU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(hidden_dim // 2, hidden_dim // 2),
            nn.LayerNorm(hidden_dim // 2),
            nn.GELU()
        )
        
        # 深度预测网络
        self.predictor = nn.Sequential(
            nn.Linear(hidden_dim + hidden_dim // 2, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout),
            
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.LayerNorm(hidden_dim // 2),
            nn.GELU(),
            nn.Dropout(dropout),
            
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.LayerNorm(hidden_dim // 4),
            nn.GELU(),
            nn.Dropout(dropout * 0.5),
            
            nn.Linear(hidden_dim // 4, 1)
        )
        
        # 权重初始化
        self.apply(self._init_weights)
    
    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            torch.nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.ones_(module.weight)
            torch.nn.init.zeros_(module.bias)
    
    def forward(self, graphs, features):
        if not isinstance(graphs, list):
            graphs = [graphs]
        
        batch_graph_features = []
        
        for graph in graphs:
            # 节点特征编码
            node_degrees = graph.in_degrees().float().unsqueeze(-1)
            h = self.node_encoder(node_degrees)  # [num_nodes, hidden_dim]
            
            # 添加batch维度
            h = h.unsqueeze(0)  # [1, num_nodes, hidden_dim]
            
            # 邻接矩阵掩码
            adj_matrix = graph.adjacency_matrix().to_dense()
            
            # 图Transformer层
            for layer in self.graph_layers:
                h = layer(h, adj_matrix.unsqueeze(0))
            
            h = h.squeeze(0)  # [num_nodes, hidden_dim]
            
            # 多尺度池化
            mean_pool = torch.mean(h, dim=0)
            max_pool = torch.max(h, dim=0)[0]
            
            # 注意力池化
            attn_weights = F.softmax(self.attention_pool(h), dim=0)
            attn_pool = torch.sum(attn_weights * h, dim=0)
            
            # 组合池化特征
            graph_feat = torch.cat([mean_pool, max_pool, attn_pool], dim=0)
            graph_feat = self.global_pool(graph_feat)
            
            batch_graph_features.append(graph_feat)
        
        # 批次处理
        graph_features = torch.stack(batch_graph_features)
        
        # 特征编码
        if features.dim() == 1:
            features = features.unsqueeze(0)
        
        feature_emb = self.feature_encoder(features)
        
        # 特征融合
        combined_features = torch.cat([graph_features, feature_emb], dim=-1)
        
        # 预测
        output = self.predictor(combined_features)
        
        return output.squeeze(-1)

class AdvancedTrainer:
    """高级训练器"""
    
    def __init__(self, model, train_loader, val_loader, test_loader=None, 
                 device='cuda', lr=8e-4, weight_decay=1e-4):
        self.model = model.to(device)
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.test_loader = test_loader
        self.device = device
        
        # 优化器
        self.optimizer = torch.optim.AdamW(
            model.parameters(), 
            lr=lr, 
            weight_decay=weight_decay,
            betas=(0.9, 0.999)
        )
        
        # 学习率调度器
        self.scheduler = torch.optim.lr_scheduler.OneCycleLR(
            self.optimizer,
            max_lr=lr,
            epochs=100,
            steps_per_epoch=len(train_loader),
            pct_start=0.1,
            anneal_strategy='cos'
        )
        
        # 早停和模型保存
        self.best_val_loss = float('inf')
        self.patience = 20
        self.patience_counter = 0
        
        # 训练历史
        self.history = {
            'train_loss': [], 'val_loss': [],
            'train_r2': [], 'val_r2': [],
            'lr': []
        }
    
    def train_epoch(self):
        self.model.train()
        total_loss = 0
        predictions, targets = [], []
        
        for batch_idx, (subgraphs, features, target) in enumerate(self.train_loader):
            features = features.to(self.device)
            target = target.to(self.device).squeeze()
            
            self.optimizer.zero_grad()
            
            # 前向传播
            output = self.model(subgraphs, features)
            
            # 损失计算（组合损失）
            mse_loss = F.mse_loss(output, target)
            mae_loss = F.l1_loss(output, target)
            loss = 0.7 * mse_loss + 0.3 * mae_loss
            
            # 反向传播
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=0.5)
            
            self.optimizer.step()
            self.scheduler.step()
            
            total_loss += loss.item()
            predictions.extend(output.detach().cpu().numpy())
            targets.extend(target.detach().cpu().numpy())
        
        avg_loss = total_loss / len(self.train_loader)
        r2 = r2_score(targets, predictions)
        
        return avg_loss, r2
    
    def validate(self):
        self.model.eval()
        total_loss = 0
        predictions, targets = [], []
        
        with torch.no_grad():
            for subgraphs, features, target in self.val_loader:
                features = features.to(self.device)
                target = target.to(self.device).squeeze()
                
                output = self.model(subgraphs, features)
                
                mse_loss = F.mse_loss(output, target)
                mae_loss = F.l1_loss(output, target)
                loss = 0.7 * mse_loss + 0.3 * mae_loss
                
                total_loss += loss.item()
                predictions.extend(output.cpu().numpy())
                targets.extend(target.cpu().numpy())
        
        avg_loss = total_loss / len(self.val_loader)
        r2 = r2_score(targets, predictions)
        
        return avg_loss, r2
    
    def train(self, epochs=100, verbose=True):
        for epoch in range(epochs):
            train_loss, train_r2 = self.train_epoch()
            val_loss, val_r2 = self.validate()
            
            current_lr = self.optimizer.param_groups[0]['lr']
            
            # 记录历史
            self.history['train_loss'].append(train_loss)
            self.history['val_loss'].append(val_loss)
            self.history['train_r2'].append(train_r2)
            self.history['val_r2'].append(val_r2)
            self.history['lr'].append(current_lr)
            
            # 早停检查
            if val_loss < self.best_val_loss:
                self.best_val_loss = val_loss
                self.patience_counter = 0
                torch.save(self.model.state_dict(), 'advanced_subgraph_gnn_best.pth')
            else:
                self.patience_counter += 1
            
            # 打印进度
            if verbose and (epoch + 1) % 5 == 0:
                print(f"轮次 {epoch+1}/{epochs}:")
                print(f"  训练损失: {train_loss:.6f}, 训练R²: {train_r2:.4f}")
                print(f"  验证损失: {val_loss:.6f}, 验证R²: {val_r2:.4f}")
                print(f"  学习率: {current_lr:.2e}")
                
                # 计算平均子图大小
                sample_batch = next(iter(self.train_loader))
                avg_size = np.mean([g.number_of_nodes() for g in sample_batch[0]])
                print(f"  平均子图大小: {avg_size:.1f}")
            
            # 早停
            if self.patience_counter >= self.patience:
                print(f"早停于轮次 {epoch+1}")
                break
        
        # 加载最佳模型
        self.model.load_state_dict(torch.load('advanced_subgraph_gnn_best.pth'))
        return self.history
    
    def test(self):
        if self.test_loader is None:
            return None
        
        self.model.eval()
        predictions, targets = [], []
        subgraph_sizes = []
        
        with torch.no_grad():
            for i, (subgraphs, features, target) in enumerate(self.test_loader):
                if i % 100 == 0:
                    print(f"测试进度: {i}/{len(self.test_loader)}")
                
                features = features.to(self.device)
                target = target.to(self.device).squeeze()
                
                output = self.model(subgraphs, features)
                
                predictions.extend(output.cpu().numpy())
                targets.extend(target.cpu().numpy())
                
                # 记录子图大小
                if isinstance(subgraphs, list):
                    subgraph_sizes.extend([g.number_of_nodes() for g in subgraphs])
                else:
                    subgraph_sizes.append(subgraphs.number_of_nodes())
        
        # 计算指标
        r2 = r2_score(targets, predictions)
        rmse = np.sqrt(mean_squared_error(targets, predictions))
        mae = mean_absolute_error(targets, predictions)
        
        return {
            'r2': r2,
            'rmse': rmse,
            'mae': mae,
            'predictions': predictions,
            'targets': targets,
            'avg_subgraph_size': np.mean(subgraph_sizes)
        }

def collate_fn(batch):
    """批处理函数"""
    subgraphs, features, targets = zip(*batch)
    features = torch.stack(features)
    targets = torch.stack(targets)
    return list(subgraphs), features, targets

def main():
    print("开始高级子图GNN训练...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 加载数据
    print("加载数据...")
    train_dataset = AdvancedSubgraphDataset('physics_based_dataset.pkl', augment=True, noise_level=0.005)
    val_dataset = AdvancedSubgraphDataset('physics_based_dataset.pkl', 
                                        scaler=train_dataset.scaler, augment=False)
    
    # 数据加载器
    train_loader = DataLoader(train_dataset, batch_size=12, shuffle=True, 
                            collate_fn=collate_fn, num_workers=2, pin_memory=True)
    val_loader = DataLoader(val_dataset, batch_size=24, shuffle=False, 
                          collate_fn=collate_fn, num_workers=2, pin_memory=True)
    
    print(f"训练样本数: {len(train_dataset)}")
    print(f"验证样本数: {len(val_dataset)}")
    print(f"特征维度: {train_dataset.features.shape[1]}")
    
    # 创建模型
    model = AdvancedSubgraphGNN(
        feature_dim=train_dataset.features.shape[1],
        hidden_dim=128,
        num_layers=4,
        num_heads=8,
        dropout=0.1
    )
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 创建训练器
    trainer = AdvancedTrainer(
        model=model,
        train_loader=train_loader,
        val_loader=val_loader,
        test_loader=val_loader,
        device=device,
        lr=8e-4,
        weight_decay=1e-4
    )
    
    # 训练模型
    print("开始训练...")
    history = trainer.train(epochs=100, verbose=True)
    
    # 测试模型
    print("\n开始测试...")
    test_results = trainer.test()
    
    if test_results:
        print("\n最终测试结果:")
        print(f"R² 分数: {test_results['r2']:.4f}")
        print(f"RMSE: {test_results['rmse']:.4f} dB")
        print(f"MAE: {test_results['mae']:.4f} dB")
        print(f"平均子图大小: {test_results['avg_subgraph_size']:.1f}")
        
        # 复杂度分析
        full_network_size = 392
        complexity_reduction = (1 - test_results['avg_subgraph_size'] / full_network_size) * 100
        print(f"复杂度降低: {complexity_reduction:.1f}%")
    
    # 绘制训练曲线
    plt.figure(figsize=(15, 5))
    
    plt.subplot(1, 3, 1)
    plt.plot(history['train_loss'], label='训练损失', alpha=0.8)
    plt.plot(history['val_loss'], label='验证损失', alpha=0.8)
    plt.xlabel('轮次')
    plt.ylabel('损失')
    plt.legend()
    plt.title('训练和验证损失')
    plt.grid(True, alpha=0.3)
    
    plt.subplot(1, 3, 2)
    plt.plot(history['train_r2'], label='训练R²', alpha=0.8)
    plt.plot(history['val_r2'], label='验证R²', alpha=0.8)
    plt.xlabel('轮次')
    plt.ylabel('R² 分数')
    plt.legend()
    plt.title('R² 分数变化')
    plt.grid(True, alpha=0.3)
    
    plt.subplot(1, 3, 3)
    plt.plot(history['lr'], alpha=0.8)
    plt.xlabel('轮次')
    plt.ylabel('学习率')
    plt.title('学习率调度')
    plt.yscale('log')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('advanced_subgraph_gnn_training_curves.png', dpi=300, bbox_inches='tight')
    print("训练曲线已保存为 advanced_subgraph_gnn_training_curves.png")
    
    # 保存结果
    results = {
        'test_results': test_results,
        'training_history': history,
        'model_config': {
            'feature_dim': train_dataset.features.shape[1],
            'hidden_dim': 128,
            'num_layers': 4,
            'num_heads': 8,
            'dropout': 0.1
        }
    }
    
    with open('advanced_subgraph_gnn_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print("结果已保存为 advanced_subgraph_gnn_results.json")

if __name__ == "__main__":
    main() 