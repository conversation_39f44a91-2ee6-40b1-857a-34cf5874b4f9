#!/usr/bin/env python3
"""
串扰感知的GAT模型
专门设计用于建模光网络中的串扰效应和光路相互影响
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import dgl
import numpy as np
from sklearn.metrics import r2_score, mean_squared_error

class CrosstalkAttention(nn.Module):
    """专门的串扰注意力机制"""
    
    def __init__(self, input_dim, hidden_dim, num_heads=8):
        super().__init__()
        self.num_heads = num_heads
        self.hidden_dim = hidden_dim
        self.head_dim = hidden_dim // num_heads
        
        # 标准注意力
        self.q_linear = nn.Linear(input_dim, hidden_dim)
        self.k_linear = nn.Linear(input_dim, hidden_dim)
        self.v_linear = nn.Linear(input_dim, hidden_dim)
        
        # 串扰特定的注意力
        self.crosstalk_q = nn.Linear(input_dim, hidden_dim)
        self.crosstalk_k = nn.Linear(input_dim, hidden_dim)
        
        # 功率和信道相关的权重
        self.power_weight = nn.Linear(1, num_heads)
        self.channel_weight = nn.Linear(1, num_heads)
        
        self.dropout = nn.Dropout(0.1)
        self.layer_norm = nn.LayerNorm(hidden_dim)
        
    def forward(self, g, h):
        batch_size, num_nodes, input_dim = h.shape
        
        # 标准注意力计算
        Q = self.q_linear(h).view(batch_size, num_nodes, self.num_heads, self.head_dim)
        K = self.k_linear(h).view(batch_size, num_nodes, self.num_heads, self.head_dim)
        V = self.v_linear(h).view(batch_size, num_nodes, self.num_heads, self.head_dim)
        
        # 串扰注意力计算
        Q_cross = self.crosstalk_q(h).view(batch_size, num_nodes, self.num_heads, self.head_dim)
        K_cross = self.crosstalk_k(h).view(batch_size, num_nodes, self.num_heads, self.head_dim)
        
        # 计算注意力分数
        attention_scores = torch.matmul(Q, K.transpose(-2, -1)) / np.sqrt(self.head_dim)
        crosstalk_scores = torch.matmul(Q_cross, K_cross.transpose(-2, -1)) / np.sqrt(self.head_dim)
        
        # 功率和信道相关的调制
        if h.shape[-1] >= 4:  # 假设功率在第4个特征，信道在第3个特征
            power_features = h[:, :, 3:4]  # 功率特征
            channel_features = h[:, :, 2:3]  # 信道特征
            
            power_weights = self.power_weight(power_features).unsqueeze(-1)
            channel_weights = self.channel_weight(channel_features).unsqueeze(-1)
            
            # 调制串扰注意力
            crosstalk_scores = crosstalk_scores * power_weights * channel_weights
        
        # 组合注意力分数
        combined_scores = attention_scores + 0.3 * crosstalk_scores
        
        # Softmax归一化
        attention_weights = F.softmax(combined_scores, dim=-1)
        attention_weights = self.dropout(attention_weights)
        
        # 应用注意力
        output = torch.matmul(attention_weights, V)
        output = output.view(batch_size, num_nodes, self.hidden_dim)
        
        # 残差连接和层归一化
        if output.shape == h.shape:
            output = self.layer_norm(output + h)
        else:
            output = self.layer_norm(output)
        
        return output

class CrosstalkAwareGAT(nn.Module):
    """串扰感知的GAT模型"""
    
    def __init__(self, input_dim, hidden_dim=128, num_heads=8, num_layers=3, dropout=0.1):
        super().__init__()
        
        self.num_layers = num_layers
        self.dropout = dropout
        
        # 输入投影
        self.input_projection = nn.Linear(input_dim, hidden_dim)
        
        # 多层串扰感知注意力
        self.attention_layers = nn.ModuleList()
        for i in range(num_layers):
            self.attention_layers.append(
                CrosstalkAttention(hidden_dim, hidden_dim, num_heads)
            )
        
        # 边缘光路识别模块
        self.edge_detector = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, 1),
            nn.Sigmoid()
        )
        
        # 串扰强度预测模块
        self.crosstalk_predictor = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, 1)
        )
        
        # QoT预测模块
        self.qot_predictor = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),  # 包含边缘检测信息
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, 1)
        )
        
    def forward(self, g, x):
        # 输入投影
        h = self.input_projection(x)
        
        # 多层注意力传播
        for attention_layer in self.attention_layers:
            h = attention_layer(g, h.unsqueeze(0)).squeeze(0)
        
        # 图级表示
        g.ndata['h'] = h
        graph_repr = dgl.readout_nodes(g, 'h', op='mean')
        
        # 边缘光路检测
        edge_prob = self.edge_detector(graph_repr)
        
        # 串扰强度预测
        crosstalk_strength = self.crosstalk_predictor(graph_repr)
        
        # 组合特征用于QoT预测
        combined_features = torch.cat([graph_repr, edge_prob], dim=-1)
        qot_prediction = self.qot_predictor(combined_features)
        
        return {
            'qot': qot_prediction.squeeze(-1),
            'edge_prob': edge_prob.squeeze(-1),
            'crosstalk': crosstalk_strength.squeeze(-1)
        }

def extract_crosstalk_features(data, max_samples=1000):
    """提取串扰感知特征"""
    
    print(f"🔧 提取串扰感知特征...")
    
    graphs, features_list, targets, edge_labels, crosstalk_labels = [], [], [], [], []
    
    for i, scenario in enumerate(data[:max_samples]):
        if i % 100 == 0:
            print(f"   处理第 {i} 个scenario...")
        
        lightpaths = scenario.get('input_lightpaths', [])
        network_result = scenario.get('network_result', {})
        
        if not lightpaths or 'lightpath_results' not in network_result:
            continue
        
        # 构建图
        nodes = set()
        edges = []
        lightpath_info = []
        
        for lp in lightpaths:
            src_str = lp.get('source_uid', '').replace('trx ', '')
            dst_str = lp.get('dest_uid', '').replace('trx ', '')
            
            if src_str.isdigit() and dst_str.isdigit():
                src_id, dst_id = int(src_str), int(dst_str)
                nodes.add(src_id)
                nodes.add(dst_id)
                edges.append((src_id, dst_id))
                
                channel = lp.get('channel_index', 0)
                power = lp.get('power_dbm', 0)
                
                lightpath_info.append({
                    'src': src_id, 'dst': dst_id, 'channel': channel, 'power': power
                })
        
        if len(nodes) < 2:
            continue
        
        # 构建DGL图
        node_list = sorted(list(nodes))
        node_map = {node: i for i, node in enumerate(node_list)}
        
        u, v = [], []
        for src, dst in edges:
            u.append(node_map[src])
            v.append(node_map[dst])
        
        if not u:
            continue
        
        g = dgl.graph((u, v), num_nodes=len(node_list))
        g = dgl.to_bidirectional(g)
        
        # 增强的节点特征
        node_features = []
        for node in node_list:
            # 该节点的lightpath
            node_lightpaths = [lp for lp in lightpath_info if lp['src'] == node or lp['dst'] == node]
            
            # 基础特征
            degree = len(node_lightpaths)
            total_power = sum(lp['power'] for lp in node_lightpaths)
            avg_channel = np.mean([lp['channel'] for lp in node_lightpaths]) if node_lightpaths else 0
            
            # 串扰相关特征
            same_channel_count = 0
            adjacent_channel_count = 0
            
            for lp in node_lightpaths:
                # 同信道干扰
                same_channel = [other for other in lightpath_info 
                              if other['channel'] == lp['channel'] and other != lp]
                same_channel_count += len(same_channel)
                
                # 邻近信道干扰
                adjacent = [other for other in lightpath_info 
                          if abs(other['channel'] - lp['channel']) <= 2 and other['channel'] != lp['channel']]
                adjacent_channel_count += len(adjacent)
            
            # 功率相关特征
            neighbor_power = sum(lp['power'] for lp in lightpath_info 
                               if lp['src'] != node and lp['dst'] != node)
            
            features = [
                node,                      # 节点ID
                degree,                    # 度数
                total_power,               # 总功率
                avg_channel,               # 平均信道
                same_channel_count,        # 同信道干扰数
                adjacent_channel_count,    # 邻近信道干扰数
                neighbor_power,            # 邻居功率
                len(lightpath_info),       # 总lightpath数
                scenario.get('network_load', 0.3)  # 网络负载
            ]
            node_features.append(features)
        
        node_features = torch.tensor(node_features, dtype=torch.float32)
        
        # 提取目标值
        lightpath_results = network_result['lightpath_results']
        snr_values = []
        crosstalk_values = []
        edge_indicators = []
        
        for lp_id, result in lightpath_results.items():
            if isinstance(result, dict) and 'qot_metrics' in result:
                qot = result['qot_metrics']
                snr = qot.get('snr_db', None)
                crosstalk = qot.get('crosstalk_penalty', 0)
                
                if snr is not None and -50 < snr < 50:
                    snr_values.append(snr)
                    crosstalk_values.append(crosstalk)
                    
                    # 边缘光路标签（SNR < 15dB认为是边缘）
                    edge_indicators.append(1 if snr < 15 else 0)
        
        if snr_values:
            avg_snr = np.mean(snr_values)
            avg_crosstalk = np.mean(crosstalk_values)
            edge_ratio = np.mean(edge_indicators)
            
            graphs.append(g)
            features_list.append(node_features)
            targets.append(avg_snr)
            crosstalk_labels.append(avg_crosstalk)
            edge_labels.append(edge_ratio)
    
    print(f"✅ 提取了 {len(graphs)} 个样本")
    return graphs, features_list, targets, edge_labels, crosstalk_labels

def main():
    """测试串扰感知GAT"""
    print("=" * 60)
    print("🧪 测试串扰感知GAT模型")
    print("=" * 60)
    
    # 首先生成高串扰数据
    print("1. 生成高串扰测试数据...")
    from generate_high_crosstalk_data import generate_high_crosstalk_scenarios
    
    test_scenarios = generate_high_crosstalk_scenarios(num_scenarios=100, network_load=0.8)
    
    # 提取特征
    print("\\n2. 提取串扰感知特征...")
    graphs, features, targets, edge_labels, crosstalk_labels = extract_crosstalk_features(test_scenarios)
    
    if len(graphs) > 10:
        print("\\n3. 创建串扰感知GAT模型...")
        model = CrosstalkAwareGAT(
            input_dim=features[0].shape[1],
            hidden_dim=64,
            num_heads=4,
            num_layers=3
        )
        
        print(f"   模型参数数量: {sum(p.numel() for p in model.parameters())}")
        print(f"   输入特征维度: {features[0].shape[1]}")
        
        # 简单测试
        with torch.no_grad():
            test_output = model(graphs[0], features[0])
            print(f"   测试输出: QoT={test_output['qot']:.3f}, Edge={test_output['edge_prob']:.3f}, Crosstalk={test_output['crosstalk']:.3f}")
        
        print("\\n✅ 串扰感知GAT模型创建成功！")
        print("\\n🎯 下一步建议:")
        print("1. 在高串扰数据上训练完整模型")
        print("2. 对比多任务学习效果（QoT + 边缘检测 + 串扰预测）")
        print("3. 分析注意力权重，验证串扰建模效果")
    else:
        print("❌ 样本数量不足，无法测试模型")

if __name__ == "__main__":
    main()
