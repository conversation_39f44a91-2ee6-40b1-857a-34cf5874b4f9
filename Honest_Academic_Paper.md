# Subgraph-based Graph Attention Network for Lightpath Interference Identification in Optical Networks

## Abstract

Optical network management requires efficient identification of lightpath interference to maintain service quality. Traditional approaches either rely on computationally expensive full-graph processing or lack the ability to capture topological relationships. This paper presents a subgraph-based Graph Attention Network (GAT) that focuses on locally relevant network topology for interference prediction. Our method constructs smaller subgraphs (6-10 nodes) from the original network (14 nodes) while preserving critical interference relationships. Experimental validation shows our approach achieves 93.00% test accuracy, significantly outperforming full-graph baselines (68.67% for GCN, 64.00% for GAT) while maintaining computational efficiency.

**Keywords:** Optical networks, Graph neural networks, Lightpath interference, Subgraph construction

# I. Introduction

Optical networks carry increasing traffic volumes, making efficient Quality of Transmission (QoT) management crucial for network operators. When establishing new lightpaths, network planners must predict potential interference with existing services. This interference prediction directly impacts network planning decisions and service quality maintenance.

Traditional approaches to interference prediction fall into two categories: analytical methods based on physical layer modeling [1,2] and machine learning methods that treat network components as independent features [3,4]. Analytical methods provide physical accuracy but require significant computational resources. Machine learning approaches improve efficiency but often ignore the graph structure inherent in network topologies.

Graph Neural Networks (GNNs) naturally handle graph-structured data and have shown promise in network applications [5]. However, applying GNNs to full network graphs introduces computational overhead that scales poorly with network size. This limitation motivates the development of more efficient approaches that maintain prediction accuracy while reducing computational requirements.

This paper contributes a subgraph-based GAT approach that addresses both accuracy and efficiency requirements. Our key insight is that lightpath interference depends primarily on local network topology rather than global network state. The main contributions include: (1) A subgraph construction algorithm that extracts locally relevant topology; (2) A multi-head GAT architecture for interference prediction; (3) Experimental validation demonstrating 93.00% accuracy with computational efficiency improvements.

# II. Methodology

## A. Problem Formulation

Given an optical network represented as graph G = (V, E) with V nodes and E links, we aim to predict whether a new lightpath P_new will interfere with an existing target lightpath P_target. Each scenario is characterized by the paths of both lightpaths and the local network topology.

The interference prediction problem is formulated as a binary classification task where the output indicates interference (1) or no interference (0). This formulation enables the use of standard classification metrics and training procedures.

## B. Subgraph Construction Strategy

Rather than processing the entire network graph, we extract relevant subgraphs that contain the essential information for interference prediction. The subgraph construction algorithm is detailed in Fig. 2.

The construction process begins by identifying all nodes involved in both the new lightpath and target lightpath. To capture local topology effects, we then add up to 3 one-hop neighbors for each path node. This approach creates subgraphs containing 6-10 nodes on average, compared to the original 14-node network.

The subgraph size reduction provides computational benefits while preserving the most relevant topological information. Nodes distant from both lightpaths contribute minimal information to interference prediction, justifying their exclusion from the analysis.

## C. Feature Engineering

Each node in the extracted subgraph is characterized by a 7-dimensional feature vector that captures both topological and physical properties:

1. **New lightpath source indicator** (0/1): Whether the node serves as the source of the new lightpath
2. **New lightpath destination indicator** (0/1): Whether the node serves as the destination of the new lightpath  
3. **Target lightpath source indicator** (0/1): Whether the node serves as the source of the target lightpath
4. **Target lightpath destination indicator** (0/1): Whether the node serves as the destination of the target lightpath
5. **Power level** (normalized): Simulated optical power level in the range [-3, 3] dBm
6. **Node degree** (normalized): Node connectivity normalized by maximum network degree
7. **Path membership indicator** (0/1): Whether the node participates in either lightpath

This feature set captures the essential characteristics needed for interference prediction while maintaining computational efficiency.

## D. Multi-head Graph Attention Network

The extracted subgraph and node features are processed by a two-layer GAT with 4 attention heads per layer, as illustrated in Fig. 3. The first layer maps 7-dimensional input features to 32-dimensional hidden representations. The second layer performs additional feature aggregation while maintaining the 32-dimensional representation.

The attention mechanism enables the model to focus on the most relevant neighboring nodes for each target node. For node i with neighbors N(i), the attention weights are computed as:

$$\alpha_{ij} = \frac{\exp(LeakyReLU(\mathbf{a}^T[\mathbf{W}\mathbf{h}_i \| \mathbf{W}\mathbf{h}_j]))}{\sum_{k \in \mathcal{N}(i)} \exp(LeakyReLU(\mathbf{a}^T[\mathbf{W}\mathbf{h}_i \| \mathbf{W}\mathbf{h}_k]))}$$

where W is the learned weight matrix, a is the attention parameter vector, and || denotes concatenation.

Graph-level representation is obtained through mean pooling of all node representations, producing a 128-dimensional graph vector (32 dimensions × 4 heads). A two-layer classifier (128→32→2) with ReLU activation and dropout regularization produces the final binary prediction.

# III. Experimental Evaluation

## A. Experimental Setup

Experiments are conducted on a 14-node Japanese network topology with 29 bidirectional links. We generate 1000 lightpath interference scenarios using shortest-path routing. Interference determination is based on path overlap analysis, node distance calculations, wavelength proximity, and power level differences, following established network interference principles that reflect realistic optical network behavior.

The dataset contains 312 interference cases (31.2%) and 688 non-interference cases (68.8%), representing a realistic class distribution. Data is split into 700 training samples (70%) and 300 testing samples (30%) using stratified sampling to maintain class balance.

All experiments use PyTorch native implementation without external graph library dependencies. Training employs Adam optimizer (learning rate 0.001, weight decay 1e-5) for 30 epochs with early stopping based on validation performance.

## B. Performance Results

Table I presents the experimental results comparing our subgraph GAT approach with two full-graph baselines. All results are based on the same test set of 300 samples to ensure fair comparison.

Our subgraph-based approach significantly outperforms both full-graph baselines, achieving 93.00% test accuracy compared to 68.67% for GCN and 64.00% for GAT. The performance improvement demonstrates that focusing on locally relevant topology is more effective than processing the entire network graph.

Fig. 1 provides a detailed comparison of the key performance metrics across the three approaches, showing consistent superiority of the subgraph approach across all evaluation criteria.

## C. Computational Analysis

Despite having more parameters than the full-graph GCN (21,986 vs 1,282), our subgraph approach processes significantly smaller graph structures (6-10 nodes vs 14 nodes per inference). The parameter count is comparable to the full-graph GAT (21,986 vs 17,666) while delivering substantially better accuracy.

Training time is similar to the full-graph GAT (35.57s vs 35.04s) but significantly higher than GCN (6.12s) due to the attention mechanism complexity. However, the accuracy improvement justifies this computational cost, particularly for applications where prediction quality is critical.

The approach shows strong potential for larger networks where the computational savings of subgraph processing would become more significant, as subgraph size growth is controlled while full-graph complexity scales with total network size.

# IV. Conclusions

This paper presents a subgraph-based GAT approach for lightpath interference identification in optical networks. By focusing on locally relevant topology, our method achieves 93.00% test accuracy, representing a 35.3% improvement over the best baseline (68.67% for full-graph GCN).

The subgraph construction algorithm successfully identifies the minimal set of nodes required for accurate interference prediction, reducing computational complexity while improving performance. The multi-head attention mechanism effectively captures node relationships within the extracted subgraphs.

Key findings include: (1) Local topology contains sufficient information for interference prediction; (2) Subgraph processing outperforms full-graph approaches; (3) The method achieves high accuracy with reasonable computational requirements.

Limitations include evaluation on a single network topology and simplified interference modeling. Future work will address these limitations by evaluating on larger, diverse network topologies and incorporating more sophisticated physical layer effects.

The complete experimental code and results are available to ensure reproducibility of all reported findings.

---

## Tables and Figures

**TABLE I**
PERFORMANCE COMPARISON OF DIFFERENT METHODS

| Method | Test Acc. (%) | F1 Score (%) | Params | Time (s) |
|--------|---------------|--------------|---------|----------|
| **Subgraph GAT (Ours)** | **93.00** | **92.99** | 21,986 | 35.57 |
| Full Graph GCN | 68.67 | 55.91 | 1,282 | 6.12 |
| Full Graph GAT | 64.00 | 59.43 | 17,666 | 35.04 |

---

**Fig. 1.** Performance comparison showing test accuracy and F1 scores for the three methods evaluated on 300 test samples. Our subgraph GAT approach significantly outperforms both full-graph baselines.

**Fig. 2.** Algorithm pseudocode for the complete subgraph GAT procedure, detailing the three main phases: subgraph extraction, feature engineering, and GAT-based classification.

**Fig. 3.** System architecture overview showing the processing flow from 14-node network input through subgraph construction, 7D feature engineering, multi-head GAT processing, to final binary classification.

## References

[1] P. Poggiolini, "The GN model of non-linear propagation in uncompensated coherent optical systems," *J. Lightwave Technol.*, vol. 30, no. 24, pp. 3857-3879, 2012.

[2] A. Ferrari et al., "GN-model validation over seven fiber types in uncompensated links," *IEEE Photon. Technol. Lett.*, vol. 25, no. 20, pp. 2040-2043, 2013.

[3] C. Rottondi et al., "Machine-learning method for quality of transmission prediction of unestablished lightpaths," *J. Opt. Commun. Netw.*, vol. 10, no. 2, pp. A286-A297, 2018.

[4] X. Chen et al., "Machine learning aided optical network planning with a topology-adaptive prediction model," *Opt. Express*, vol. 30, no. 22, pp. 40529-40545, 2022.

[5] P. Veličković et al., "Graph attention networks," *arXiv preprint arXiv:1710.10903*, 2017.