#!/usr/bin/env python3
"""
生成符合IEEE会议论文格式的图表
- 双栏格式适配
- 标准字体和尺寸
- 符合会议论文要求
"""

import matplotlib.pyplot as plt
import numpy as np

def set_conference_style():
    """设置符合IEEE会议论文的图表样式"""
    plt.rcParams.update({
        'font.size': 8,           # 会议论文标准字体大小
        'axes.titlesize': 9,      # 标题字体
        'axes.labelsize': 8,      # 坐标轴标签字体
        'legend.fontsize': 7,     # 图例字体
        'xtick.labelsize': 7,     # x轴刻度字体
        'ytick.labelsize': 7,     # y轴刻度字体
        'lines.linewidth': 1.5,   # 线宽
        'font.family': 'serif',   # 使用serif字体
        'text.usetex': False,     # 不使用LaTeX渲染
        'figure.dpi': 300         # 高分辨率
    })

def create_conference_performance_figure():
    """生成符合会议格式的性能对比图"""
    set_conference_style()
    
    # 双栏论文的图片宽度通常为3.5英寸
    fig, ax = plt.subplots(1, 1, figsize=(3.5, 2.5))
    
    # 真实实验数据
    methods = ['Subgraph\nGAT', 'Full Graph\nGCN', 'Full Graph\nGAT']
    test_accuracy = [93.00, 68.67, 64.00]
    f1_score = [92.99, 55.91, 59.43]
    
    x = np.arange(len(methods))
    width = 0.35
    
    # 绘制柱状图
    bars1 = ax.bar(x - width/2, test_accuracy, width, label='Test Accuracy', 
                   color='#2E8B57', alpha=0.8, edgecolor='black', linewidth=0.5)
    bars2 = ax.bar(x + width/2, f1_score, width, label='F1 Score', 
                   color='#DC143C', alpha=0.8, edgecolor='black', linewidth=0.5)
    
    # 设置标签和标题
    ax.set_xlabel('Methods')
    ax.set_ylabel('Performance (%)')
    ax.set_title('Performance Comparison')
    ax.set_xticks(x)
    ax.set_xticklabels(methods)
    ax.legend(loc='upper right')
    
    # 添加数值标签（小字体）
    for bar, value in zip(bars1, test_accuracy):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 1,
               f'{value:.1f}', ha='center', va='bottom', fontsize=6, fontweight='bold')
    
    for bar, value in zip(bars2, f1_score):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 1,
               f'{value:.1f}', ha='center', va='bottom', fontsize=6, fontweight='bold')
    
    # 设置y轴范围和网格
    ax.set_ylim(0, 100)
    ax.grid(True, alpha=0.3, axis='y', linewidth=0.5)
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    
    # ⭐ 加粗我们的方法
    ax.get_xticklabels()[0].set_weight('bold')  # Subgraph GAT
    
    plt.tight_layout()
    plt.savefig('Fig1_performance.png', dpi=300, bbox_inches='tight', 
                facecolor='white', pad_inches=0.02)
    plt.savefig('Fig1_performance.pdf', bbox_inches='tight', 
                facecolor='white', pad_inches=0.02)
    plt.close()
    
    print("✅ Fig. 1 性能对比图已生成 (会议格式)")

def create_conference_algorithm_figure():
    """生成符合会议格式的算法伪代码"""
    set_conference_style()
    
    # 单栏宽度的算法图
    fig, ax = plt.subplots(1, 1, figsize=(7, 4))  # 跨双栏
    ax.axis('off')
    
    # 会议论文的算法伪代码格式
    algorithm_text = """Algorithm 1: Subgraph GAT for Lightpath Interference Identification
Input: Network G = (V, E), new lightpath P_new, target lightpath P_target
Output: Binary interference prediction (0: no interference, 1: interference)

1: function EXTRACT_SUBGRAPH(G, P_new, P_target)
2:    relevant_nodes ← nodes(P_new) ∪ nodes(P_target)
3:    for each v ∈ relevant_nodes do
4:        neighbors ← 1-hop neighbors of v in G
5:        relevant_nodes ← relevant_nodes ∪ neighbors[1:3]  ▷ Add up to 3 neighbors
6:    G_sub ← induced subgraph of G on relevant_nodes
7:    return G_sub, relevant_nodes

8: function GENERATE_FEATURES(relevant_nodes, P_new, P_target)
9:    for each v ∈ relevant_nodes do
10:       f_v ← [I_src(v,P_new), I_dst(v,P_new), I_src(v,P_target), I_dst(v,P_target),
11:              power(v), degree(v)/max_degree, I_path(v,P_new∪P_target)]
12:   return F ∈ ℝ^{|relevant_nodes| × 7}

13: function SUBGRAPH_GAT(G_sub, F)
14:    H^(1) ← MultiHeadGAT(F, G_sub, heads=4, dim=32)        ▷ First GAT layer  
15:    H^(2) ← MultiHeadGAT(H^(1), G_sub, heads=4, dim=32)    ▷ Second GAT layer
16:    h_graph ← MeanPooling(H^(2))                           ▷ Graph representation
17:    logits ← MLP(h_graph)                                  ▷ 128→32→2 classifier
18:    return argmax(Softmax(logits))

19: G_sub, nodes ← EXTRACT_SUBGRAPH(G, P_new, P_target)
20: F ← GENERATE_FEATURES(nodes, P_new, P_target)  
21: return SUBGRAPH_GAT(G_sub, F)"""
    
    # 在图上显示算法（使用等宽字体）
    ax.text(0.02, 0.98, algorithm_text, transform=ax.transAxes, fontsize=7,
            verticalalignment='top', horizontalalignment='left',
            bbox=dict(boxstyle="round,pad=0.3", facecolor='#f8f8f8', alpha=0.8, 
                     edgecolor='gray', linewidth=0.5),
            family='monospace')
    
    plt.tight_layout()
    plt.savefig('Fig2_algorithm.png', dpi=300, bbox_inches='tight',
                facecolor='white', pad_inches=0.02)
    plt.savefig('Fig2_algorithm.pdf', bbox_inches='tight',
                facecolor='white', pad_inches=0.02)
    plt.close()
    
    print("✅ Fig. 2 算法伪代码已生成 (会议格式)")

def create_conference_architecture_figure():
    """生成符合会议格式的架构图"""
    set_conference_style()
    
    # 单栏宽度的架构图
    fig, ax = plt.subplots(1, 1, figsize=(7, 2.5))  # 跨双栏
    ax.set_xlim(0, 10)
    ax.set_ylim(0, 3)
    ax.axis('off')
    
    # 会议论文标准的组件样式
    components = [
        {"name": "14-node\nNetwork", "pos": (1, 1.5), "size": (1.2, 1), "color": "#E6F3FF"},
        {"name": "Subgraph\nConstruction", "pos": (3, 1.5), "size": (1.4, 1), "color": "#E6FFE6"},
        {"name": "7D Feature\nEngineering", "pos": (5, 1.5), "size": (1.4, 1), "color": "#FFF9E6"},
        {"name": "Multi-head GAT\n(4×2 layers)", "pos": (7, 1.5), "size": (1.4, 1), "color": "#FFE6E6"},
        {"name": "Binary\nClassifier", "pos": (9, 1.5), "size": (1.2, 1), "color": "#F0E6FF"}
    ]
    
    # 绘制组件（会议论文标准样式）
    for comp in components:
        rect = plt.Rectangle((comp["pos"][0] - comp["size"][0]/2, comp["pos"][1] - comp["size"][1]/2), 
                           comp["size"][0], comp["size"][1], 
                           facecolor=comp["color"], edgecolor='black', linewidth=1)
        ax.add_patch(rect)
        ax.text(comp["pos"][0], comp["pos"][1], comp["name"], 
               ha='center', va='center', fontsize=7, fontweight='bold')
    
    # 添加箭头（更细更专业）
    arrow_positions = [
        ((1.6, 1.5), (2.3, 1.5)),    # Network → Subgraph
        ((3.7, 1.5), (4.3, 1.5)),    # Subgraph → Feature  
        ((5.7, 1.5), (6.3, 1.5)),    # Feature → GAT
        ((7.7, 1.5), (8.4, 1.5))     # GAT → Classifier
    ]
    
    for start, end in arrow_positions:
        ax.annotate('', xy=end, xytext=start,
                   arrowprops=dict(arrowstyle='->', lw=1.5, color='black'))
    
    # 添加详细信息标注（会议论文风格）
    ax.text(3, 0.5, '6-10 nodes', ha='center', va='center', fontsize=6, style='italic')
    ax.text(5, 0.5, 'Path indicators\nPower, Degree', ha='center', va='center', fontsize=6, style='italic')
    ax.text(7, 0.5, '128-dim output', ha='center', va='center', fontsize=6, style='italic')
    ax.text(9, 0.5, '128→32→2', ha='center', va='center', fontsize=6, style='italic')
    
    plt.tight_layout()
    plt.savefig('Fig3_architecture.png', dpi=300, bbox_inches='tight',
                facecolor='white', pad_inches=0.02)
    plt.savefig('Fig3_architecture.pdf', bbox_inches='tight',
                facecolor='white', pad_inches=0.02)
    plt.close()
    
    print("✅ Fig. 3 系统架构图已生成 (会议格式)")

def create_results_table():
    """生成符合会议格式的结果表格数据"""
    table_data = {
        'Method': ['Subgraph GAT (Ours)', 'Full Graph GCN', 'Full Graph GAT'],
        'Test Acc. (%)': [93.00, 68.67, 64.00],
        'Precision (%)': [92.98, 47.15, 58.16],
        'Recall (%)': [93.00, 68.67, 64.00],
        'F1 Score (%)': [92.99, 55.91, 59.43],
        'Parameters': [21986, 1282, 17666],
        'Time (s)': [35.57, 6.12, 35.04]
    }
    
    # 生成LaTeX格式的表格代码
    latex_table = """
\\begin{table}[htbp]
\\centering
\\caption{Performance Comparison of Different Methods}
\\label{tab:performance}
\\begin{tabular}{lccccc}
\\hline
Method & Test Acc. & F1 Score & Params & Time \\\\
& (\\%) & (\\%) & & (s) \\\\
\\hline
\\textbf{Subgraph GAT (Ours)} & \\textbf{93.00} & \\textbf{92.99} & 21,986 & 35.57 \\\\
Full Graph GCN & 68.67 & 55.91 & 1,282 & 6.12 \\\\
Full Graph GAT & 64.00 & 59.43 & 17,666 & 35.04 \\\\
\\hline
\\end{tabular}
\\end{table}
"""
    
    with open('table_results.tex', 'w') as f:
        f.write(latex_table)
    
    print("✅ LaTeX表格代码已生成: table_results.tex")

def main():
    """生成所有符合会议格式的图表"""
    print("📄 生成符合IEEE会议论文格式的图表...")
    print("=" * 50)
    
    create_conference_performance_figure()
    create_conference_algorithm_figure()
    create_conference_architecture_figure()
    create_results_table()
    
    print(f"\n✅ 会议格式图表生成完成!")
    print("📊 生成的文件:")
    print("   - Fig1_performance.png/pdf (性能对比 - 双栏适配)")
    print("   - Fig2_algorithm.png/pdf (算法伪代码 - 跨栏)")
    print("   - Fig3_architecture.png/pdf (系统架构 - 跨栏)")
    print("   - table_results.tex (LaTeX表格代码)")
    print("\n💡 图表已优化为IEEE会议论文标准格式")
    print("   - 字体大小适合双栏论文")
    print("   - 图片尺寸符合会议要求")
    print("   - 包含LaTeX表格代码")

if __name__ == "__main__":
    main()