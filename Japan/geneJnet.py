#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import copy
import re

from gnpy.core.elements import Transceiver, <PERSON><PERSON>, <PERSON><PERSON>, Roadm
from gnpy.core.utils import db2lin, lin2db
from gnpy.core.info import create_input_spectral_information, Channel
from gnpy.core.network import build_network
from gnpy.tools.json_io import load_network, load_equipment
from pathlib import Path
from networkx import dijkstra_path
from networkx import all_simple_paths

import numpy as np
import pandas as pd
from random import choice
import random
import os
import sys, os
sys.path.insert(0, "/home/<USER>/zyh_qotcode/DYcode/gnpy")
print("当前工作目录：", os.getcwd())
import gnpy
print("✅ 当前使用的 gnpy 路径:", gnpy.__file__)

# 网络数据来源
network_file_name = Path('../Data/JapanLink.json') #具体器件参数，如EDFA、fiber等等
eqpt_library_name = Path('../Data/eqpt_configJapan.json') #包含elements与connection两类，描述网络拓扑

###############################################################################初始化信息########################################################################
con_in = 0
con_out = 0
nodes = ['trx 1', 'trx 2', 'trx 3', 'trx 4', 'trx 5', 'trx 6', 'trx 7', 'trx 8', 'trx 9', 'trx 10', 'trx 11',
         'trx 12', 'trx 13', 'trx 14']
wavelength_start = 191.3e12
wavelength_spacing = 50e9
Baud_rate = 32e9
channel_num = 80

equipment = load_equipment(eqpt_library_name)  # 读取元件详细信息
network = load_network(network_file_name, equipment)  # 读取具体网络拓扑
# equipment = load_equipment('eqpt_configJapan.json')  # 读取元件详细信息
# network = load_network('JapanLink.json', equipment)  # 读取具体网络拓扑
build_network(network, equipment, 0, 20)   # build_network(network, equipment, pref_ch_db, pref_total_db)  具体网络拓扑、元件详细信息、
w = 0
LP_N = w#记录上一次仿真中网络存在的光路数
simulation_num = 0
maxhop_num = 10
nearwave_num = 4
symple_number = 550
symple = np.empty((symple_number,7+maxhop_num*(nearwave_num*2+1)))
data = np.empty((symple_number, 3), dtype=object)
while True:
    link_carraies = np.empty((14,14),dtype=object)#记录链路频谱信息，发射功率等
    link_waveAva = np.empty((14,14), dtype=object)
    link_waveState = np.empty((14,14), dtype=object)#链路的波长占用情况
    edge_feature_P = np.empty((14,14), dtype=object)#记录链路特征功率
    # edge_feature_W = np.empty((14,14), dtype=object)#记录链路波长占用
    edge_SNR = np.empty((14,14), dtype=object)#记录链路上接收SNR
    # P_array = np.zeros((14,channel_num))#节点发射功率
    # GSNR_array = np.zeros((14,channel_num))#节点接收SNR
    Traffic_array = np.empty(symple_number,dtype=object)#业务源目的节点记录
    Route_array = np.empty(symple_number,dtype=object)#业务路由记录
    wavelength_array = np.empty(symple_number,dtype=object)#波长记录
    path_Route_array = np.empty(symple_number,dtype=object)#记录路由每一跳经过的器件
    # 对于网络中的各个元件分别计算
    for e in network.nodes():
        if isinstance(e, Fiber):  # 判断e是否为光纤，如果是则计算噪声
            loss = e.params.loss_coef * e.params.length  # 光纤损耗=损耗系数*长度
            e.params.con_in = con_in  #
            e.params.con_out = con_out
        if isinstance(e, Edfa):  # 判断是否为EDFA
            e.operational.gain_target = loss + con_in + con_out  # EDFA增益,con in 与 con out 均为connector损耗

    transceivers = {n.uid: n for n in network.nodes() if isinstance(n, Transceiver)}  # 找到网络中的收发器
    wavelength = np.zeros((1,channel_num))
    for i in range(channel_num):
        wavelength[0,i] = wavelength_start + i * wavelength_spacing
    for i in range(len(link_waveAva)):
        for j in range(len(link_waveAva)):
            link_waveAva[i,j] = np.ones((1,channel_num))
    for i in range(len(link_carraies)):
        for j in range(len(link_carraies)):
            link_carraies[i,j] = create_input_spectral_information(wavelength_start, wavelength_start + channel_num * wavelength_spacing, 0.15, Baud_rate, 0, wavelength_spacing)
            link_waveAva[i, j] = np.ones((1, channel_num))
            edge_SNR[i, j] = np.zeros((1, channel_num))
            edge_feature_P[i, j] = np.zeros((1, channel_num))
            link_waveState[i, j] = np.zeros((1, channel_num))
    # 输入频谱信息，参数：最低频率、最高频率(79个信道间隔)，滚降系数，波特率，功率，信道间隔）10e9:GHz
    # frequency = []
    ############################################################################################################################################################
    K = 3 #KSP中的K参数
    while True:
        input_power_dbm = random.randint(-30, 0) * 0.1  # 输入功率dbm
        input_power_lin = db2lin(input_power_dbm) * 1e-3
        srcc = choice(nodes)
        dest = choice(nodes)
        while srcc == dest:
            srcc = choice(nodes)
            dest = choice(nodes)
        source = next(transceivers[uid] for uid in transceivers if uid == srcc)  # 根据收发器型号判断源节点
        sink = next(transceivers[uid] for uid in transceivers if uid == dest)

        paths = list(all_simple_paths(network, source, sink, cutoff=150))
        path_length = []
        paths_new = []
        for i in range(0, len(paths)):
            path_single = paths[i]
            TotalLength_single = 0
            for el in path_single:
                if isinstance(el, Fiber):
                    L1 = el.params.length
                    TotalLength_single += L1
            path_length.append(TotalLength_single)
        path_length = np.array(path_length)
        index = np.argsort(path_length)
        for i in range(0, len(index)):
            tmp = index[i]
            paths_new.append(paths[tmp])
        if len(paths_new)>=K:
            path = paths_new[choice(range(K))] ##选择前K条最短路中的某一条
        else:
            path = paths_new[choice(range(len(paths_new)))]
        path_inter = []#路由
        path_transceiver = []#路径上经过的所有收发机

        # for i in paths_new:
        #     path_inter = []
        #     for j in i:
        #         if isinstance(j,Transceiver):
        #             path_inter.append(int(re.sub("\D", "",j.uid )))
        #     print(path_inter)

        waveAva = np.ones((1,channel_num)) #波长可用情况
        path_Route = np.empty((1,20),dtype=object)#每跳经过的器件
        el_hop = [] #一跳经过的器件
        h=0
        for j in path:#路由及每段路由经过的器件
            el_hop.append(j)
            if isinstance(j,Transceiver) and j.uid == source.uid:
                path_inter.append(int(re.sub("\D", "", j.uid)))
                path_transceiver.append(j)#将每个节点上的收发机放入数组中
                continue
            elif isinstance(j,Transceiver):
                path_inter.append(int(re.sub("\D", "",j.uid )))
                path_transceiver.append(j)
                path_Route[0,h] = el_hop
                h = h+1
                el_hop = []
                el_hop.append(j)
        for i in range(len(path_inter)-1):#找出路由上的所有可用波长
            waveAva = waveAva * link_waveAva[path_inter[i]-1,path_inter[i+1]-1]
        Ava = waveAva.nonzero()
        route_num = 0
        while Ava[0].shape[0] == 0: #若找不到可用波长,则重新选源目的节点最多选route——num次
            route_num = route_num + 1
            srcc = choice(nodes)
            dest = choice(nodes)
            while srcc == dest:
                srcc = choice(nodes)
                dest = choice(nodes)
            source = next(transceivers[uid] for uid in transceivers if uid == srcc)  # 根据收发器型号判断源节点
            sink = next(transceivers[uid] for uid in transceivers if uid == dest)

            paths = list(all_simple_paths(network, source, sink, cutoff=150))
            path_length = []
            paths_new = []
            for i in range(0, len(paths)):
                path_single = paths[i]
                TotalLength_single = 0
                for el in path_single:
                    if isinstance(el, Fiber):
                        L1 = el.params.length
                        TotalLength_single += L1
                path_length.append(TotalLength_single)
            path_length = np.array(path_length)
            index = np.argsort(path_length)
            for i in range(0, len(index)):
                tmp = index[i]
                paths_new.append(paths[tmp])
            if len(paths_new) >= K:
                path = paths_new[choice(range(K))]  ##选择前K条最短路中的某一条
            else:
                path = paths_new[choice(range(len(paths_new)))]
            # paths = list(all_shortest_paths(network, source, sink))
            # path = choice(paths)
            path_inter = []
            path_transceiver = []

            waveAva = np.ones((1, channel_num))
            path_Route = np.empty((1, 20), dtype=object)  # 每跳经过的器件
            el_hop = []  # 一跳经过的器件
            h = 0
            for j in path:  # 路由及每段路由经过的器件
                el_hop.append(j)
                if isinstance(j, Transceiver) and j.uid == source.uid:
                    path_inter.append(int(re.sub("\D", "", j.uid)))
                    path_transceiver.append(j)  # 将每个节点上的收发机放入数组中
                    continue
                elif isinstance(j, Transceiver):
                    path_inter.append(int(re.sub("\D", "", j.uid)))
                    path_transceiver.append(j)  # 将每个节点上的收发机放入数组中
                    path_Route[0, h] = el_hop
                    h = h + 1
                    el_hop = []
                    el_hop.append(j)
            for i in range(len(path_inter) - 1):  # 找出路由上的所有可用波长
                waveAva = waveAva * link_waveAva[path_inter[i] - 1, path_inter[i + 1] - 1]
            Ava = waveAva.nonzero()
            if route_num >= 10:
                break
        if Ava[0].shape[0] == 0:#如果重路由route_num次后仍然没有可用波长，则跳出循环重新生成网络数据
            break
        SelectWave = Ava[1][0]
        for i in range(len(path_inter)-1):
                edge_feature_P[path_inter[i]-1,path_inter[i+1]-1][0,SelectWave] = input_power_lin#链路发射功率记录
        wavelength_allocation = np.array([])
        Route_array[w] = path_inter
        wavelength_array[w] = SelectWave
        path_Route_array[w] = path_Route#经过的所有器件
        Traffic_array[w] = path_transceiver
        print("wavelength:", SelectWave)
        for i in range(len(path_inter)-1): # 逐跳分配波长
            # wavelength_hop = []
            link_waveAva[path_inter[i]-1,path_inter[i+1]-1][0,SelectWave] = 0 #可用波长矩阵更新
            link_waveState[path_inter[i]-1,path_inter[i+1]-1][0,SelectWave] = 1 #链路状态矩阵更新
            # wavelength_allo = link_waveState[path_inter[i]-1,path_inter[i+1]-1][0]*wavelength #本跳的链路波长分配情况
            # if SelectWave < nearwave_num: #最邻近四条信道开通状态
            #     for ii in range(nearwave_num-SelectWave):
            #         wavelength_allocation = np.append(wavelength_allocation,0)
            #     wavelength_allocation = np.concatenate((wavelength_allocation,wavelength_allo[0,0:SelectWave]), axis = 0)
            #     wavelength_allocation = np.concatenate((wavelength_allocation, wavelength_allo[0, SelectWave:SelectWave + nearwave_num + 1]), axis=0)
            # elif SelectWave > channel_num - nearwave_num-1:
            #     wavelength_allocation = np.concatenate((wavelength_allocation, wavelength_allo[0, SelectWave - nearwave_num:SelectWave]), axis=0)
            #     wavelength_allocation = np.concatenate((wavelength_allocation, wavelength_allo[0, SelectWave:channel_num]),axis=0)
            #     for ii in range(SelectWave - (channel_num - nearwave_num-1)):
            #         wavelength_allocation = np.append(wavelength_allocation,0)
            # else:
            #     wavelength_allocation = np.concatenate((wavelength_allocation, wavelength_allo[0, SelectWave-nearwave_num:SelectWave]), axis=0)
            #     wavelength_allocation = np.concatenate((wavelength_allocation,wavelength_allo[0,SelectWave:SelectWave+ nearwave_num +1]), axis=0)
            # print("wavelength_len:", len(wavelength_allocation))
            # if i == 0:#初始化分配波长的功率以及pref
            pw = link_carraies[path_inter[i] - 1, path_inter[i + 1] - 1].carriers[SelectWave].power._replace(signal = input_power_lin)
            channel = link_carraies[path_inter[i] - 1, path_inter[i + 1] - 1].carriers[SelectWave]._replace(power = pw)
            chan = []
            for ii in range(len(link_carraies[path_inter[i] - 1, path_inter[i + 1] - 1].carriers)):
                if ii == SelectWave:
                    chan.append(channel)
                else:
                    chan.append(link_carraies[path_inter[i] - 1, path_inter[i + 1] - 1].carriers[ii])
            carriers = tuple(f for f in chan)
            link_carraies[path_inter[i] - 1, path_inter[i + 1] - 1] = link_carraies[path_inter[i] - 1, path_inter[i + 1] - 1]._replace(carriers = carriers)
            pre = link_carraies[path_inter[i] - 1, path_inter[i + 1] - 1].pref._replace(p_span0 = input_power_dbm, p_spani = input_power_dbm, neq_ch = lin2db(80))
            link_carraies[path_inter[i] - 1, path_inter[i + 1] - 1] = link_carraies[path_inter[i] - 1, path_inter[i + 1] - 1]._replace(pref = pre)
            # for el in path_Route[0,i]: #单跳运算其噪声
            #     link_carraies[path_inter[i] - 1, path_inter[i + 1] - 1] = el(link_carraies[path_inter[i] - 1, path_inter[i + 1] - 1], SelectWave)
            # if i<(len(path_inter)-2): #将噪声功率传递给下一跳
            #     channel = link_carraies[path_inter[i] - 1, path_inter[i + 1] - 1].carriers[SelectWave]
            #     chan = []
            #     for ii in range(len(link_carraies[path_inter[i+1] - 1, path_inter[i + 2] - 1].carriers)):
            #         if ii == SelectWave:
            #             chan.append(channel)
            #         else:
            #             chan.append(link_carraies[path_inter[i+1] - 1, path_inter[i + 2] - 1].carriers[ii])
            #     carriers = tuple(f for f in chan)
            #     link_carraies[path_inter[i+1] - 1, path_inter[i + 2] - 1] = link_carraies[path_inter[i+1] - 1, path_inter[i + 2] - 1]._replace(carriers=carriers)
            #     # link_carraies[path_inter[i+1] - 1, path_inter[i + 2] - 1].carriers[SelectWave] = link_carraies[path_inter[i] - 1, path_inter[i + 1] - 1].carriers[SelectWave]
            #     pre = link_carraies[path_inter[i] - 1, path_inter[i + 1] - 1].pref
            #     link_carraies[path_inter[i + 1] - 1, path_inter[i + 2] - 1] = link_carraies[path_inter[i + 1] - 1, path_inter[i + 2] - 1]._replace(pref = pre)
        link_carraies_array = copy.deepcopy(link_carraies)
        for j in range(LP_N,w+1):#对已部署的光路全部重新计算GSNR
            path_inter = Route_array[j]
            SelectWave = wavelength_array[j]
            path_Route = path_Route_array[j]
            for i in range(len(path_inter)-1):#对于路由上的每一跳
                for el in path_Route[0, i]:  # 单跳运算其噪声
                    link_carraies_array[path_inter[i] - 1, path_inter[i + 1] - 1] = el(
                        link_carraies_array[path_inter[i] - 1, path_inter[i + 1] - 1], SelectWave)
                    # link_carraies[path_inter[i] - 1, path_inter[i + 1] - 1] = el(
                    #     link_carraies[path_inter[i] - 1, path_inter[i + 1] - 1], SelectWave)
                if i < (len(path_inter) - 2):  # 将噪声功率传递给下一跳
                    channel = link_carraies_array[path_inter[i] - 1, path_inter[i + 1] - 1].carriers[SelectWave]
                    chan = []
                    for ii in range(len(link_carraies_array[path_inter[i + 1] - 1, path_inter[i + 2] - 1].carriers)):
                        if ii == SelectWave:
                            chan.append(channel)
                        else:
                            chan.append(link_carraies_array[path_inter[i + 1] - 1, path_inter[i + 2] - 1].carriers[ii])
                    carriers = tuple(f for f in chan)
                    link_carraies_array[path_inter[i + 1] - 1, path_inter[i + 2] - 1] = link_carraies_array[
                        path_inter[i + 1] - 1, path_inter[i + 2] - 1]._replace(carriers=carriers)
                    # link_carraies[path_inter[i+1] - 1, path_inter[i + 2] - 1].carriers[SelectWave] = link_carraies[path_inter[i] - 1, path_inter[i + 1] - 1].carriers[SelectWave]
                    pre = link_carraies_array[path_inter[i] - 1, path_inter[i + 1] - 1].pref
                    link_carraies_array[path_inter[i + 1] - 1, path_inter[i + 2] - 1] = link_carraies_array[
                        path_inter[i + 1] - 1, path_inter[i + 2] - 1]._replace(pref=pre)
                edge_SNR[path_inter[i] - 1, path_inter[i+1] - 1][0,SelectWave] = Traffic_array[j][i+1].snr[SelectWave] #链路标签为目标节点的接收SNR值
    #     if wavelength_allocation.shape[0]< (nearwave_num * 2+1) * maxhop_num:
    #         for ii in range((nearwave_num * 2+1) * maxhop_num-wavelength_allocation.shape[0]):
    #             wavelength_allocation = np.append(wavelength_allocation, 0)
    #
    #
    #     print("source: ", source)
    #     print("route:", len(path_inter))
    #     print("wavelrngth_size",wavelength_allocation.shape[0] )
    #     print("dest: ", sink)
    #     list_path = []
    #     length = []
    #     Spans = 0
    #     Hops = 0
    #     TotalLength = 0
    #     HopLength = 0
    #     for el in path:  # 对于每条路径,路径中包含有源节点、目的节点以及途中经过的EDFA、光纤
    #         if isinstance(el, Fiber):
    #             Spans = Spans + 1
    #             L1 = el.params.length
    #             TotalLength += L1
    #         if isinstance(el, Transceiver):
    #             Hops = Hops + 1
    #             list_path.append(el.uid)
    #     for i in range(0, len(list_path)-1):
    #         source1 = next(transceivers[uid] for uid in transceivers if uid == list_path[i])
    #         sink1 = next(transceivers[uid] for uid in transceivers if uid == list_path[i+1])
    #         path2 = dijkstra_path(network, source1, sink1)
    #         for el1 in path2:
    #             if isinstance(el1, Fiber):
    #                 Length = el1.params.length
    #                 HopLength += Length
    #         # print("length : ", HopLength/1000)
    #         length.append(HopLength)
    #         HopLength = 0
    #     maxLength = max(length)
    #     print("max length (km): ", maxLength/1000)
    #     print("Span Number : ", Spans)
    #     print("Hop Number : ", Hops-1)
    #     print("Total Length (km) : ", TotalLength/1000)
    # # 输出最终的具体参数
    #     print(f'pw: {input_power_dbm} con in: {con_in} con out: {con_out}',
    #           f'OSNR@0.1nm: {sink.osnr_ase_01nm}',  # round函数，取整，2位
    #           f'SNR@bandwitdth: {sink.snr}')
        data[w,0] = copy.deepcopy(edge_feature_P)
        data[w,1] = copy.deepcopy(edge_SNR)
        data[w,2] = copy.deepcopy(link_waveState)
        # data = []
        # data.append(input_power_lin)
        # data.extend(wavelength_allocation.tolist())
        # # GSNR_array[sink,SelectWave] = sink.snr[SelectWave]
        # data.extend([maxLength/1000, Spans, Hops-1, TotalLength/1000,  sink.osnr_ase_01nm[SelectWave], sink.snr[SelectWave]])
        # symple[w,:] = np.array(data)
        w=w+1
        print("data number：", w)
        if w % 10 ==0:
            # test = pd.DataFrame(data=data)
            # test.to_csv('data/Japan data KSP_train_GNN.csv')
            np.save('data/Jdata.npy', data)
        if w == symple_number:
            break
    simulation_num = simulation_num + 1
    LP_N = w
    if w == symple_number:
        break
print("Simulation_num: ", simulation_num)

# name = ['Ptx(W)','Wavelength','max length(km)', 'Span number', 'Hop number', 'total length(km)',  'OSNR', 'GSNR']
# test = pd.DataFrame( data=symple)
# test.to_csv('data/Jdata.csv')


print('\n')