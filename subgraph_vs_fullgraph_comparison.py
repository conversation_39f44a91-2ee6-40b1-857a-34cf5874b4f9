#!/usr/bin/env python3
"""
子图GAT vs 普通GNN对比实验
针对光路影响识别的分类任务，评估子图方法的优势
"""

import time
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import dgl
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, classification_report
from dgl.nn.pytorch.conv import GATConv, GCNConv
import dgl.function as fn
from dgl.dataloading import GraphDataLoader
import copy
import json
from datetime import datetime
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

class SubgraphGAT(nn.Module):
    """子图GAT模型 - 只在相关子图上运行"""
    def __init__(self, in_dim, hidden_dim, num_heads, num_layers, num_classes, dropout=0.1):
        super(SubgraphGAT, self).__init__()
        self.num_layers = num_layers
        self.gat_layers = nn.ModuleList()
        
        # 第一层
        self.gat_layers.append(GATConv(
            in_dim, hidden_dim, num_heads, 
            feat_drop=dropout, attn_drop=dropout, allow_zero_in_degree=True
        ))
        
        # 中间层
        for _ in range(num_layers - 1):
            self.gat_layers.append(GATConv(
                hidden_dim * num_heads, hidden_dim, num_heads,
                feat_drop=dropout, attn_drop=dropout, allow_zero_in_degree=True
            ))
        
        # 分类器
        self.classifier = nn.Sequential(
            nn.Linear(hidden_dim * num_heads, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, num_classes)
        )
        
    def forward(self, g, node_features):
        h = node_features
        
        # GAT层
        for gat_layer in self.gat_layers:
            h = gat_layer(g, h).flatten(1)
            h = F.relu(h)
        
        # 图级池化
        g.ndata['h'] = h
        graph_repr = dgl.readout_nodes(g, 'h', op='mean')
        
        # 分类
        logits = self.classifier(graph_repr)
        return logits

class FullGraphGNN(nn.Module):
    """普通全图GNN模型 - 在整个网络图上运行"""
    def __init__(self, in_dim, hidden_dim, num_layers, num_classes, gnn_type='GCN', dropout=0.1):
        super(FullGraphGNN, self).__init__()
        self.num_layers = num_layers
        self.gnn_layers = nn.ModuleList()
        
        # 选择GNN类型
        if gnn_type == 'GCN':
            conv_layer = GCNConv
        elif gnn_type == 'GAT':
            conv_layer = lambda in_f, out_f: GATConv(in_f, out_f, num_heads=4, allow_zero_in_degree=True)
        
        # 第一层
        if gnn_type == 'GAT':
            self.gnn_layers.append(GATConv(in_dim, hidden_dim, num_heads=4, allow_zero_in_degree=True))
        else:
            self.gnn_layers.append(GCNConv(in_dim, hidden_dim, allow_zero_in_degree=True))
        
        # 中间层
        for _ in range(num_layers - 1):
            if gnn_type == 'GAT':
                self.gnn_layers.append(GATConv(hidden_dim * 4, hidden_dim, num_heads=4, allow_zero_in_degree=True))
            else:
                self.gnn_layers.append(GCNConv(hidden_dim, hidden_dim, allow_zero_in_degree=True))
        
        # 节点分类器
        if gnn_type == 'GAT':
            self.classifier = nn.Linear(hidden_dim * 4, num_classes)
        else:
            self.classifier = nn.Linear(hidden_dim, num_classes)
            
        self.dropout = nn.Dropout(dropout)
        self.gnn_type = gnn_type
        
    def forward(self, g, node_features, target_node_mask):
        h = node_features
        
        # GNN层
        for i, gnn_layer in enumerate(self.gnn_layers):
            if self.gnn_type == 'GAT':
                h = gnn_layer(g, h).flatten(1)
            else:
                h = gnn_layer(g, h)
            
            if i < len(self.gnn_layers) - 1:
                h = F.relu(h)
                h = self.dropout(h)
        
        # 只对目标节点进行分类
        target_logits = self.classifier(h[target_node_mask])
        return target_logits

class LightpathImpactDataGenerator:
    """光路影响数据生成器 - 支持子图和全图"""
    
    def __init__(self):
        self.create_japan_network()
        
    def create_japan_network(self):
        """创建真实的日本网络拓扑"""
        u = torch.tensor([0,0,1,2,3,3,4,4,4,5,6,6,7,8,11,9,8,13,11,11,13,10])
        v = torch.tensor([1,2,3,4,4,5,5,8,6,7,7,11,9,11,9,10,13,11,10,12,12,12])
        
        self.link_length = np.array([
            [0,160,240,0,0,0,0,0,0,0,0,0,0,0],
            [160,0,0,240,0,0,0,0,0,0,0,0,0,0],
            [240,0,0,0,240,0,0,0,0,0,0,0,0,0],
            [0,240,0,0,80,40,0,0,0,0,0,0,0,0],
            [0,0,240,80,0,40,80,0,240,0,0,0,0,0],
            [0,0,0,40,40,0,0,160,0,0,0,0,0,0],
            [0,0,0,0,80,0,0,80,0,0,0,240,0,0],
            [0,0,0,0,0,160,80,0,0,160,0,0,0,0],
            [0,0,0,0,240,0,0,0,0,0,0,240,0,240],
            [0,0,0,0,0,0,0,160,0,0,40,40,0,0],
            [0,0,0,0,0,0,0,0,0,40,0,40,320,0],
            [0,0,0,0,0,0,240,0,240,40,40,0,320,240],
            [0,0,0,0,0,0,0,0,0,0,320,320,0,160],
            [0,0,0,0,0,0,0,0,240,0,0,240,160,0]
        ])
        
        self.full_graph = dgl.graph((u, v))
        self.full_graph = dgl.to_bidirected(self.full_graph)
        
        print(f"🌐 创建日本网络: {self.full_graph.num_nodes()}节点, {self.full_graph.num_edges()}边")
        
    def generate_lightpath_scenarios(self, num_scenarios: int = 1000):
        """生成光路影响场景数据"""
        print(f"🔄 生成 {num_scenarios} 个光路影响场景...")
        
        scenarios = []
        
        for i in range(num_scenarios):
            if i % 200 == 0:
                print(f"   进度: {i}/{num_scenarios}")
            
            # 随机选择新光路和目标光路
            nodes = list(range(self.full_graph.num_nodes()))
            
            # 新光路：源节点和目标节点
            new_src = np.random.choice(nodes)
            new_dst = np.random.choice([n for n in nodes if n != new_src])
            
            # 目标光路：源节点和目标节点  
            target_src = np.random.choice(nodes)
            target_dst = np.random.choice([n for n in nodes if n != target_src])
            
            # 计算路径
            try:
                import networkx as nx
                nx_graph = dgl.to_networkx(self.full_graph).to_undirected()
                
                # 为边添加权重
                for edge in nx_graph.edges():
                    i, j = edge
                    nx_graph[i][j]['weight'] = self.link_length[i][j] if self.link_length[i][j] > 0 else 1000
                
                new_path = nx.shortest_path(nx_graph, new_src, new_dst, weight='weight')
                target_path = nx.shortest_path(nx_graph, target_src, target_dst, weight='weight')
                
            except:
                # 如果路径计算失败，使用直连
                new_path = [new_src, new_dst]
                target_path = [target_src, target_dst]
            
            # 计算影响（基于路径重叠、波长距离等）
            is_affected = self._calculate_impact(new_path, target_path, new_src, new_dst, target_src, target_dst)
            
            # 生成特征
            scenario_data = {
                'new_lightpath': {'src': new_src, 'dst': new_dst, 'path': new_path},
                'target_lightpath': {'src': target_src, 'dst': target_dst, 'path': target_path},
                'full_graph_features': self._generate_full_graph_features(),
                'subgraph': self._extract_subgraph(new_path, target_path),
                'label': 1 if is_affected else 0,
                'target_node': target_src  # 用于全图GNN的目标节点
            }
            
            scenarios.append(scenario_data)
        
        print(f"✅ 场景生成完成! 总数: {len(scenarios)}")
        
        # 统计标签分布
        affected_count = sum(1 for s in scenarios if s['label'] == 1)
        print(f"   受影响: {affected_count} ({affected_count/len(scenarios)*100:.1f}%)")
        print(f"   未受影响: {len(scenarios)-affected_count} ({(len(scenarios)-affected_count)/len(scenarios)*100:.1f}%)")
        
        return scenarios
    
    def _calculate_impact(self, new_path, target_path, new_src, new_dst, target_src, target_dst):
        """计算光路影响"""
        # 1. 路径重叠
        new_edges = set((min(new_path[i], new_path[i+1]), max(new_path[i], new_path[i+1])) 
                       for i in range(len(new_path)-1))
        target_edges = set((min(target_path[i], target_path[i+1]), max(target_path[i], target_path[i+1])) 
                          for i in range(len(target_path)-1))
        
        path_overlap = len(new_edges.intersection(target_edges)) / max(len(new_edges.union(target_edges)), 1)
        
        # 2. 节点距离
        node_distance = min(
            abs(new_src - target_src), abs(new_src - target_dst),
            abs(new_dst - target_src), abs(new_dst - target_dst)
        )
        
        # 3. 波长距离（模拟）
        new_wavelength = np.random.randint(1, 81)
        target_wavelength = np.random.randint(1, 81)
        wavelength_distance = abs(new_wavelength - target_wavelength)
        
        # 4. 功率差异（模拟）
        new_power = np.random.uniform(-3, 3)
        target_power = np.random.uniform(-3, 3)
        power_diff = abs(new_power - target_power)
        
        # 综合判断影响
        impact_score = (
            path_overlap * 0.4 +                    # 路径重叠权重最高
            (1 / (node_distance + 1)) * 0.3 +       # 节点距离越近影响越大
            (1 / (wavelength_distance + 1)) * 0.2 + # 波长距离越近影响越大
            (power_diff / 6) * 0.1                  # 功率差异影响
        )
        
        return impact_score > 0.3  # 阈值判断
    
    def _generate_full_graph_features(self):
        """生成全图节点特征"""
        num_nodes = self.full_graph.num_nodes()
        
        # 节点特征：[功率, 负载, 中心性, 度数]
        features = []
        for i in range(num_nodes):
            power = np.random.uniform(-3, 3)  # 发射功率
            load = np.random.uniform(0, 1)    # 节点负载
            centrality = self.full_graph.in_degrees()[i].item() / max(self.full_graph.in_degrees())  # 归一化度数
            betweenness = np.random.uniform(0, 1)  # 模拟中介中心性
            
            features.append([power, load, centrality, betweenness])
        
        return torch.tensor(features, dtype=torch.float32)
    
    def _extract_subgraph(self, new_path, target_path):
        """提取相关子图"""
        # 1. 获取相关节点
        relevant_nodes = set(new_path + target_path)
        
        # 2. 添加1-hop邻居
        for node in list(relevant_nodes):
            neighbors = self.full_graph.successors(node).tolist()
            relevant_nodes.update(neighbors[:2])  # 最多添加2个邻居
        
        relevant_nodes = sorted(list(relevant_nodes))
        
        # 3. 创建节点映射
        node_mapping = {old_id: new_id for new_id, old_id in enumerate(relevant_nodes)}
        
        # 4. 提取子图
        subgraph = dgl.node_subgraph(self.full_graph, relevant_nodes)
        
        # 5. 生成子图节点特征
        subgraph_features = []
        for old_node_id in relevant_nodes:
            # 特征：[是否新光路源, 是否新光路目标, 是否目标光路源, 是否目标光路目标, 
            #       功率, 度数, 路径重叠指示]
            features = [
                1.0 if old_node_id in new_path[:1] else 0.0,      # 新光路源
                1.0 if old_node_id in new_path[-1:] else 0.0,     # 新光路目标
                1.0 if old_node_id in target_path[:1] else 0.0,   # 目标光路源
                1.0 if old_node_id in target_path[-1:] else 0.0,  # 目标光路目标
                np.random.uniform(-3, 3),                          # 功率
                float(self.full_graph.in_degrees()[old_node_id]) / 10.0,  # 归一化度数
                1.0 if old_node_id in (new_path + target_path) else 0.0   # 是否在关键路径上
            ]
            subgraph_features.append(features)
        
        subgraph_data = {
            'graph': subgraph,
            'features': torch.tensor(subgraph_features, dtype=torch.float32),
            'node_mapping': node_mapping,
            'relevant_nodes': relevant_nodes
        }
        
        return subgraph_data

def train_and_compare_models():
    """训练并对比子图GAT和全图GNN"""
    print("🏁 子图GAT vs 普通GNN对比实验")
    print("=" * 60)
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🔧 使用设备: {device}")
    
    # 生成数据
    data_generator = LightpathImpactDataGenerator()
    scenarios = data_generator.generate_lightpath_scenarios(num_scenarios=2000)
    
    # 数据划分
    train_scenarios, test_scenarios = train_test_split(scenarios, test_size=0.3, random_state=42,
                                                     stratify=[s['label'] for s in scenarios])
    
    print(f"📊 数据划分: 训练{len(train_scenarios)}, 测试{len(test_scenarios)}")
    
    # 模型参数
    hidden_dim = 64
    num_layers = 2
    num_classes = 2
    dropout = 0.1
    
    results = {}
    
    # ==================== 子图GAT训练 ====================
    print(f"\n🎯 训练子图GAT...")
    
    subgraph_model = SubgraphGAT(
        in_dim=7,  # 子图节点特征维度
        hidden_dim=hidden_dim,
        num_heads=4,
        num_layers=num_layers,
        num_classes=num_classes,
        dropout=dropout
    ).to(device)
    
    print(f"   子图GAT参数: {sum(p.numel() for p in subgraph_model.parameters())}")
    
    # 准备子图数据
    subgraph_train_data = []
    subgraph_test_data = []
    
    for scenario in train_scenarios:
        subgraph_data = scenario['subgraph']
        subgraph_train_data.append((subgraph_data['graph'], subgraph_data['features'], scenario['label']))
    
    for scenario in test_scenarios:
        subgraph_data = scenario['subgraph']
        subgraph_test_data.append((subgraph_data['graph'], subgraph_data['features'], scenario['label']))
    
    # 训练子图GAT
    subgraph_results = train_model(subgraph_model, subgraph_train_data, subgraph_test_data, 
                                 device, "SubgraphGAT", is_subgraph=True)
    results['SubgraphGAT'] = subgraph_results
    
    # ==================== 全图GCN训练 ====================
    print(f"\n🎯 训练全图GCN...")
    
    fullgraph_gcn = FullGraphGNN(
        in_dim=4,  # 全图节点特征维度
        hidden_dim=hidden_dim,
        num_layers=num_layers,
        num_classes=num_classes,
        gnn_type='GCN',
        dropout=dropout
    ).to(device)
    
    print(f"   全图GCN参数: {sum(p.numel() for p in fullgraph_gcn.parameters())}")
    
    # 准备全图数据
    fullgraph_train_data = []
    fullgraph_test_data = []
    
    for scenario in train_scenarios:
        target_mask = torch.zeros(data_generator.full_graph.num_nodes(), dtype=torch.bool)
        target_mask[scenario['target_node']] = True
        fullgraph_train_data.append((data_generator.full_graph, scenario['full_graph_features'], 
                                   target_mask, scenario['label']))
    
    for scenario in test_scenarios:
        target_mask = torch.zeros(data_generator.full_graph.num_nodes(), dtype=torch.bool)
        target_mask[scenario['target_node']] = True
        fullgraph_test_data.append((data_generator.full_graph, scenario['full_graph_features'], 
                                  target_mask, scenario['label']))
    
    # 训练全图GCN
    fullgraph_gcn_results = train_model(fullgraph_gcn, fullgraph_train_data, fullgraph_test_data, 
                                      device, "FullGraphGCN", is_subgraph=False)
    results['FullGraphGCN'] = fullgraph_gcn_results
    
    # ==================== 全图GAT训练 ====================
    print(f"\n🎯 训练全图GAT...")
    
    fullgraph_gat = FullGraphGNN(
        in_dim=4,
        hidden_dim=hidden_dim,
        num_layers=num_layers,
        num_classes=num_classes,
        gnn_type='GAT',
        dropout=dropout
    ).to(device)
    
    print(f"   全图GAT参数: {sum(p.numel() for p in fullgraph_gat.parameters())}")
    
    # 训练全图GAT
    fullgraph_gat_results = train_model(fullgraph_gat, fullgraph_train_data, fullgraph_test_data, 
                                      device, "FullGraphGAT", is_subgraph=False)
    results['FullGraphGAT'] = fullgraph_gat_results
    
    # ==================== 结果对比 ====================
    print(f"\n📊 实验结果对比:")
    print("=" * 80)
    print(f"{'方法':<15} {'测试准确率':<12} {'精确率':<10} {'召回率':<10} {'F1分数':<10} {'训练时间(s)':<12}")
    print("-" * 80)
    
    for method_name, result in results.items():
        print(f"{method_name:<15} {result['test_accuracy']:<12.4f} {result['precision']:<10.4f} "
              f"{result['recall']:<10.4f} {result['f1_score']:<10.4f} {result['train_time']:<12.2f}")
    
    # 保存详细结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    final_results = {
        'experiment_info': {
            'timestamp': timestamp,
            'task': 'Lightpath Impact Classification',
            'num_scenarios': len(scenarios),
            'train_samples': len(train_scenarios),
            'test_samples': len(test_scenarios)
        },
        'results': results
    }
    
    results_file = f'subgraph_vs_fullgraph_results_{timestamp}.json'
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(final_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📁 详细结果保存至: {results_file}")
    
    return final_results

def train_model(model, train_data, test_data, device, model_name, is_subgraph=True, epochs=50):
    """训练单个模型"""
    print(f"   开始训练{model_name}...")
    
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-5)
    criterion = nn.CrossEntropyLoss()
    
    start_time = time.time()
    
    # 训练循环
    for epoch in range(epochs):
        model.train()
        total_loss = 0
        
        for batch_data in train_data:
            if is_subgraph:
                graph, features, label = batch_data
                graph = graph.to(device)
                features = features.to(device)
                label = torch.tensor([label], dtype=torch.long).to(device)
                
                logits = model(graph, features)
                loss = criterion(logits, label)
            else:
                graph, features, target_mask, label = batch_data
                graph = graph.to(device)
                features = features.to(device)
                target_mask = target_mask.to(device)
                label = torch.tensor([label], dtype=torch.long).to(device)
                
                logits = model(graph, features, target_mask)
                loss = criterion(logits, label)
            
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
        
        if epoch % 10 == 0:
            avg_loss = total_loss / len(train_data)
            print(f"     Epoch {epoch}: Loss = {avg_loss:.4f}")
    
    train_time = time.time() - start_time
    
    # 测试评估
    model.eval()
    predictions = []
    true_labels = []
    
    with torch.no_grad():
        for batch_data in test_data:
            if is_subgraph:
                graph, features, label = batch_data
                graph = graph.to(device)
                features = features.to(device)
                
                logits = model(graph, features)
                pred = torch.argmax(logits, dim=1).cpu().item()
            else:
                graph, features, target_mask, label = batch_data
                graph = graph.to(device)
                features = features.to(device)
                target_mask = target_mask.to(device)
                
                logits = model(graph, features, target_mask)
                pred = torch.argmax(logits, dim=1).cpu().item()
            
            predictions.append(pred)
            true_labels.append(label)
    
    # 计算性能指标
    accuracy = accuracy_score(true_labels, predictions)
    precision, recall, f1, _ = precision_recall_fscore_support(true_labels, predictions, average='weighted')
    
    print(f"   {model_name} 测试准确率: {accuracy:.4f}")
    
    return {
        'test_accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1,
        'train_time': train_time,
        'predictions': predictions,
        'true_labels': true_labels
    }

if __name__ == "__main__":
    results = train_and_compare_models() 