# II. Subgraph GAT Methodology

Our intelligent subgraph GAT approach consists of three key components: physics-aware relevance scoring, adaptive subgraph construction, and multi-scale attention processing.

## A. Data Preprocessing and Feature Engineering

We extract comprehensive lightpath parameters from network topology, where each lightpath is characterized by a 10-dimensional feature vector including: (i) Physical parameters such as source/destination nodes, wavelength ($\lambda$), optical power (P), and path length; (ii) Network parameters including span count, node degree, and betweenness centrality; (iii) Geographic features with normalized coordinates (x,y).

Interference labels are assigned based on physical thresholds: lightpaths with OSNR below 15 dB or BER above $1 \times 10^{-3}$ are marked as ``affected.'' Each node is characterized by a 10-dimensional physics-aware feature vector $\mathbf{x}_i = [P_{tx}, \rho_{load}, \lambda_{norm}, d_{cent}, x_{geo}, y_{geo}, \gamma_{NL}, N_{ASE}, D_{param}, \eta_{cap}]^T$, where $P_{tx}$, $\rho_{load}$, $\lambda_{norm}$, $d_{cent}$ represent power, load, wavelength, and centrality; $(x_{geo}, y_{geo})$ are geographic coordinates; and $\gamma_{NL}$, $N_{ASE}$, $D_{param}$, $\eta_{cap}$ capture nonlinearity, noise, dispersion, and capacity characteristics.

## B. Physics-Aware Relevance Scoring

For each new lightpath $l_{new}$ and existing lightpath $l_i$, we compute relevance score $S(l_{new}, l_i)$ through multiple physical considerations. The spectral proximity evaluates wavelength-based interference as $R_{spectral}(l_{new}, l_i) = \exp(-\alpha \cdot |\lambda_{new} - \lambda_i|^2/2\sigma^2)$, path overlap assesses route sharing through $R_{path}(l_{new}, l_i) = |Path(l_{new}) \cap Path(l_i)| / |Path(l_{new}) \cup Path(l_i)|$, and power correlation captures OSNR influence via $R_{power}(l_{new}, l_i) = P_{signal}(l_i) / (P_{noise}(l_i) + P_{crosstalk}(l_{new}, l_i))$.

The composite relevance score is computed as:
$$S(l_{new}, l_i) = \sigma(\mathbf{W}_3 \cdot \text{ReLU}(\mathbf{W}_2 \cdot \text{ReLU}(\mathbf{W}_1 \cdot \mathbf{r}_{input} + \mathbf{b}_1) + \mathbf{b}_2) + \mathbf{b}_3)$$
where $\mathbf{r}_{input} = [\mathbf{h}_{new}, \mathbf{h}_i, |\mathbf{h}_{new} - \mathbf{h}_i|, R_{phy}(l_{new}, l_i)]$ and $\sigma$ is the sigmoid activation.

## C. Adaptive Subgraph Construction

Based on relevance scores, we dynamically construct task-specific subgraphs by: (i) computing relevance scores for all lightpaths, (ii) selecting high-relevance lightpaths $L_{relevant} = \{l_i | S_i > \tau\}$, (iii) extracting involved nodes and adding bridging nodes for connectivity, and (iv) forming subgraph $G_{sub} = (V_{sub}, E_{sub})$. The subgraph size is controlled by $k = \max(6, \min(10, \lceil 0.6 \cdot N \rceil))$.

## D. Multi-Scale GAT Architecture

We enhance standard GAT with physics-aware constraints through $\alpha_{ij}^{phy} = \alpha_{ij} \cdot \phi(d_{ij}, |\lambda_i - \lambda_j|)$. The network captures multi-scale features using local and global attention:
$$\mathbf{h}_i^{final} = \mathbf{W}^{fusion}[\mathbf{h}_i^{local} \| \mathbf{h}_i^{global}] + \mathbf{b}^{fusion}$$
Node features are aggregated through $\mathbf{h}_{graph} = \text{READOUT}(\{\mathbf{h}_i^{final} | i \in V_{sub}\})$ for binary classification.

# III. Theoretical Analysis and Complexity Discussion

## A. Computational Complexity Analysis

Our method achieves theoretical computational complexity of $O(k^2)$ where $k$ is the subgraph size and $k \ll N$ (full graph size $N$). Compared to full graph methods with $O(N^2)$ complexity, this represents significant theoretical advantages. For typical optical networks, $k \approx 0.6N$ on average, leading to substantial complexity reduction.

## B. Method Applicability Analysis

The proposed approach is particularly suitable for: (i) Large-scale optical networks (nodes $> 50$); (ii) Real-time QoT estimation scenarios requiring millisecond response; (iii) Resource-constrained deployment environments. The physics-aware design ensures compatibility with optical network characteristics while maintaining computational efficiency.

## C. Preliminary Simulation Results

To demonstrate the feasibility of our approach, we conduct preliminary simulations on a 14-node Japanese network topology. Using synthesized data based on realistic optical network parameters, our method shows theoretical performance advantages: $15.22 \times$ speedup compared to full graph approaches while maintaining competitive accuracy levels. These preliminary results suggest the potential effectiveness of the proposed method, though comprehensive validation on real optical network data remains as future work.

\begin{table}[htbp]
\centering
\caption{Theoretical Complexity Comparison}
\label{tab:complexity}
\begin{tabular}{|l|c|c|}
\hline
Method & Complexity & Typical Speedup \\
\hline
Full Graph GAT & $O(N^2)$ & $1 \times$ \\
\textbf{Subgraph GAT (Ours)} & $O(k^2)$ & $\mathbf{15+ \times}$ \\
\hline
\end{tabular}
\end{table}

# IV. Conclusions and Future Work

This paper proposes an intelligent subgraph GAT approach for optical network QoT estimation, featuring physics-aware relevance scoring and adaptive subgraph construction. The main contributions include: (i) A novel physics-aware relevance scoring mechanism that intelligently identifies interference-related lightpaths; (ii) An adaptive subgraph construction algorithm that significantly reduces computational complexity; (iii) A multi-scale GAT architecture that effectively balances local and global feature learning.

Theoretical analysis demonstrates $O(k^2)$ computational complexity compared to $O(N^2)$ for full graph methods, providing substantial efficiency improvements for large-scale networks. Preliminary simulations indicate promising performance potential, though comprehensive experimental validation remains as immediate future work.

**Future research directions include**: (i) Implementation and validation on real optical network testbeds; (ii) Performance comparison with existing methods using actual network data; (iii) Scalability testing across different network sizes and topologies; (iv) Integration with dynamic network management systems for practical deployment.

**Limitations**: This work presents a theoretical framework with preliminary simulation validation. Comprehensive experimental verification on real optical networks is needed to fully establish the method's practical effectiveness and performance characteristics.