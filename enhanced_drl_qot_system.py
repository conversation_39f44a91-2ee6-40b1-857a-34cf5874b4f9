"""
增强版DRL QoT优化系统
基于理论分析的完整实现

主要改进：
1. 精确的物理层建模（GN模型）
2. 自适应奖励函数设计
3. 图神经网络增强状态表示
4. 改进的探索策略
5. 理论保证的收敛性分析
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import gym
from gym import spaces
import random
import math
from collections import deque, namedtuple
from typing import Dict, List, Tuple, Optional, Union
import json
import pickle
from dataclasses import dataclass
from torch_geometric.nn import GCNConv, GATConv, global_mean_pool
from torch_geometric.data import Data, Batch
import networkx as nx

# 物理常数
PLANCK_CONSTANT = 6.626e-34  # J·s
LIGHT_SPEED = 3e8  # m/s
WAVELENGTH_1550 = 1550e-9  # m
REFERENCE_BANDWIDTH = 12.5e9  # Hz

@dataclass
class PhysicsConstants:
    """物理常数配置"""
    h: float = PLANCK_CONSTANT
    c: float = LIGHT_SPEED
    lambda_ref: float = WAVELENGTH_1550
    B0: float = REFERENCE_BANDWIDTH
    gamma: float = 1.2e-3  # 非线性系数 (1/W/m)
    alpha: float = 0.2e-3  # 损耗系数 (1/m)
    beta2: float = -2.1e-26  # 色散系数 (s^2/m)

class AdvancedQoTCalculator:
    """
    高级QoT计算器 - 基于GN模型的精确物理层建模
    """
    
    def __init__(self, constants: PhysicsConstants = PhysicsConstants()):
        self.constants = constants
        self.fiber_spans = {}  # 光纤跨段信息
        self.amplifier_params = {}  # 放大器参数
        
    def calculate_ase_noise(self, path: List[int], amplifier_config: Dict) -> float:
        """
        计算ASE（放大器自发辐射）噪声
        
        P_ASE = Σ h·ν·B0·F_i·(G_i - 1)
        """
        total_ase = 0.0
        frequency = self.constants.c / self.constants.lambda_ref
        
        for node in path[:-1]:  # 除了最后一个节点，每个节点都有放大器
            if node in amplifier_config:
                amp_params = amplifier_config[node]
                noise_figure = amp_params.get('noise_figure', 5.0)  # dB
                gain = amp_params.get('gain', 20.0)  # dB
                
                # 转换为线性值
                F_linear = 10**(noise_figure / 10)
                G_linear = 10**(gain / 10)
                
                # ASE噪声功率
                ase_power = (self.constants.h * frequency * self.constants.B0 * 
                           F_linear * (G_linear - 1))
                total_ase += ase_power
        
        return total_ase
    
    def calculate_nonlinear_noise(self, path: List[int], wavelength: int, 
                                 power_dbm: float, existing_lightpaths: List[Dict]) -> float:
        """
        计算非线性噪声 - 基于GN模型
        
        包括：自相位调制(SPM)、交叉相位调制(XPM)、四波混频(FWM)
        """
        power_linear = 10**(power_dbm / 10 - 3)  # 转换为瓦特
        
        # 自相位调制噪声
        spm_noise = self._calculate_spm_noise(path, power_linear)
        
        # 交叉相位调制噪声
        xpm_noise = self._calculate_xpm_noise(path, wavelength, power_linear, existing_lightpaths)
        
        # 四波混频噪声
        fwm_noise = self._calculate_fwm_noise(path, wavelength, power_linear, existing_lightpaths)
        
        return spm_noise + xpm_noise + fwm_noise
    
    def _calculate_spm_noise(self, path: List[int], power: float) -> float:
        """自相位调制噪声计算"""
        total_length = self._get_path_length(path)
        effective_length = (1 - np.exp(-2 * self.constants.alpha * total_length)) / (2 * self.constants.alpha)
        
        # 简化的SPM噪声模型
        spm_noise = (8/27) * (self.constants.gamma ** 2) * (power ** 3) * (effective_length ** 2)
        
        return spm_noise
    
    def _calculate_xpm_noise(self, path: List[int], wavelength: int, 
                           power: float, existing_lightpaths: List[Dict]) -> float:
        """交叉相位调制噪声计算"""
        xpm_noise = 0.0
        total_length = self._get_path_length(path)
        effective_length = (1 - np.exp(-2 * self.constants.alpha * total_length)) / (2 * self.constants.alpha)
        
        for lp in existing_lightpaths:
            if self._paths_overlap(path, lp['path']):
                # 计算波长间隔
                wavelength_spacing = abs(wavelength - lp['wavelength'])
                if wavelength_spacing <= 5:  # 只考虑相邻5个波长的影响
                    other_power = 10**(lp['power_dbm'] / 10 - 3)
                    
                    # XPM效率因子
                    xpm_efficiency = self._calculate_xpm_efficiency(wavelength_spacing)
                    
                    # XPM噪声贡献
                    xpm_contribution = (16/27) * (self.constants.gamma ** 2) * (power ** 2) * other_power * \
                                     (effective_length ** 2) * xpm_efficiency
                    xmp_noise += xpm_contribution
        
        return xmp_noise
    
    def _calculate_fwm_noise(self, path: List[int], wavelength: int, 
                           power: float, existing_lightpaths: List[Dict]) -> float:
        """四波混频噪声计算"""
        fwm_noise = 0.0
        total_length = self._get_path_length(path)
        
        # 简化的FWM计算：只考虑三阶非线性项
        for lp1 in existing_lightpaths:
            for lp2 in existing_lightpaths:
                if (self._paths_overlap(path, lp1['path']) and 
                    self._paths_overlap(path, lp2['path'])):
                    
                    # 相位匹配条件检查
                    if self._check_phase_matching(wavelength, lp1['wavelength'], lp2['wavelength']):
                        power1 = 10**(lp1['power_dbm'] / 10 - 3)
                        power2 = 10**(lp2['power_dbm'] / 10 - 3)
                        
                        # FWM效率
                        fwm_efficiency = self._calculate_fwm_efficiency(
                            wavelength, lp1['wavelength'], lp2['wavelength'], total_length
                        )
                        
                        # FWM噪声贡献
                        fwm_contribution = (self.constants.gamma ** 2) * power1 * power2 * \
                                         (total_length ** 2) * fwm_efficiency
                        fwm_noise += fwm_contribution
        
        return fwm_noise
    
    def _calculate_xmp_efficiency(self, wavelength_spacing: int) -> float:
        """XPM效率因子计算"""
        # 简化模型：效率随波长间隔减小
        return max(0.1, 1.0 / (1 + wavelength_spacing * 0.2))
    
    def _calculate_fwm_efficiency(self, w1: int, w2: int, w3: int, length: float) -> float:
        """FWM效率因子计算"""
        # 简化的相位匹配效率
        phase_mismatch = abs(w1 - w2) + abs(w2 - w3) + abs(w1 - w3)
        return max(0.01, 1.0 / (1 + phase_mismatch * 0.1))
    
    def _check_phase_matching(self, w1: int, w2: int, w3: int) -> bool:
        """检查相位匹配条件"""
        # 简化的相位匹配检查
        return abs(w1 + w2 - 2*w3) <= 2
    
    def _get_path_length(self, path: List[int]) -> float:
        """获取路径长度"""
        total_length = 0.0
        for i in range(len(path) - 1):
            span_key = f"{path[i]}-{path[i+1]}"
            if span_key in self.fiber_spans:
                total_length += self.fiber_spans[span_key]['length']
            else:
                # 默认跨段长度
                total_length += 80.0  # km
        return total_length * 1000  # 转换为米
    
    def _paths_overlap(self, path1: List[int], path2: List[int]) -> bool:
        """检查两条路径是否重叠"""
        set1 = set(path1)
        set2 = set(path2)
        return len(set1.intersection(set2)) > 1
    
    def calculate_osnr(self, path: List[int], wavelength: int, power_dbm: float,
                      existing_lightpaths: List[Dict], amplifier_config: Dict) -> Dict:
        """
        计算光路的OSNR
        """
        # 信号功率
        signal_power = 10**(power_dbm / 10 - 3)  # 瓦特
        
        # ASE噪声
        ase_noise = self.calculate_ase_noise(path, amplifier_config)
        
        # 非线性噪声
        nli_noise = self.calculate_nonlinear_noise(path, wavelength, power_dbm, existing_lightpaths)
        
        # 总噪声
        total_noise = ase_noise + nli_noise
        
        if total_noise <= 0:
            return {'success': False, 'osnr_db': 0.0, 'error': 'Invalid noise calculation'}
        
        # OSNR计算
        osnr_linear = signal_power / total_noise
        osnr_db = 10 * np.log10(osnr_linear)
        
        return {
            'success': True,
            'osnr_db': osnr_db,
            'signal_power': signal_power,
            'ase_noise': ase_noise,
            'nli_noise': nli_noise,
            'total_noise': total_noise
        }

class GraphNeuralNetwork(nn.Module):
    """
    图神经网络 - 用于处理网络拓扑信息
    """
    
    def __init__(self, node_features: int, edge_features: int, hidden_dim: int = 64):
        super().__init__()
        self.node_features = node_features
        self.edge_features = edge_features
        self.hidden_dim = hidden_dim
        
        # 节点特征编码
        self.node_encoder = nn.Linear(node_features, hidden_dim)
        
        # 图注意力层
        self.gat1 = GATConv(hidden_dim, hidden_dim, heads=4, concat=False)
        self.gat2 = GATConv(hidden_dim, hidden_dim, heads=4, concat=False)
        
        # 图卷积层
        self.gcn1 = GCNConv(hidden_dim, hidden_dim)
        self.gcn2 = GCNConv(hidden_dim, hidden_dim)
        
        # 输出层
        self.output_layer = nn.Linear(hidden_dim, hidden_dim)
        
    def forward(self, x, edge_index, edge_attr=None, batch=None):
        """
        前向传播
        """
        # 节点特征编码
        x = F.relu(self.node_encoder(x))
        
        # 图注意力
        x = F.relu(self.gat1(x, edge_index))
        x = F.dropout(x, training=self.training)
        x = F.relu(self.gat2(x, edge_index))
        
        # 图卷积
        x = F.relu(self.gcn1(x, edge_index))
        x = F.dropout(x, training=self.training)
        x = F.relu(self.gcn2(x, edge_index))
        
        # 全局池化
        if batch is not None:
            x = global_mean_pool(x, batch)
        else:
            x = torch.mean(x, dim=0, keepdim=True)
        
        # 输出
        x = self.output_layer(x)
        
        return x

class AdaptiveRewardCalculator:
    """
    自适应奖励计算器
    """
    
    def __init__(self, base_weights: Dict[str, float] = None):
        self.base_weights = base_weights or {
            'qot_success': 15.0,
            'qot_margin': 10.0,
            'network_impact': -25.0,
            'efficiency': 5.0,
            'blocking_penalty': -30.0
        }
        
        # 自适应参数
        self.adaptation_rate = 0.01
        self.weight_bounds = {'min': 0.1, 'max': 50.0}
        
    def calculate_adaptive_reward(self, state: np.ndarray, action: np.ndarray,
                                 qot_result: Dict, network_state: Dict) -> Tuple[float, Dict]:
        """
        计算自适应奖励
        """
        # 分析网络状态
        network_load = self._calculate_network_load(state)
        qot_distribution = self._analyze_qot_distribution(state)
        resource_utilization = self._calculate_resource_utilization(state)
        
        # 自适应权重计算
        adaptive_weights = self._calculate_adaptive_weights(
            network_load, qot_distribution, resource_utilization
        )
        
        # 基础奖励组件
        rewards = {}
        
        # 1. QoT质量奖励
        if qot_result['success']:
            osnr_actual = qot_result['osnr_db']
            osnr_required = network_state.get('required_osnr', 15.0)
            
            if osnr_actual >= osnr_required:
                # 成功奖励
                rewards['qot_success'] = adaptive_weights['qot_success']
                
                # 安全裕度奖励（连续函数）
                safety_margin = osnr_actual - osnr_required
                margin_reward = math.tanh(safety_margin / 5.0)  # 饱和函数
                rewards['qot_margin'] = adaptive_weights['qot_margin'] * margin_reward
            else:
                # 失败惩罚
                deficit = osnr_required - osnr_actual
                rewards['qot_failure'] = -adaptive_weights['qot_success'] * (deficit / osnr_required)
        else:
            rewards['blocking_penalty'] = adaptive_weights['blocking_penalty']
        
        # 2. 网络影响奖励
        network_impact = network_state.get('predicted_degradation', 0.0)
        if network_impact > 0:
            # 使用指数惩罚
            impact_penalty = -math.exp(network_impact) + 1
            rewards['network_impact'] = adaptive_weights['network_impact'] * impact_penalty
        
        # 3. 效率奖励
        power_efficiency = self._calculate_power_efficiency(action[2])
        spectral_efficiency = self._calculate_spectral_efficiency(action[3])
        combined_efficiency = 0.6 * power_efficiency + 0.4 * spectral_efficiency
        rewards['efficiency'] = adaptive_weights['efficiency'] * combined_efficiency
        
        # 总奖励
        total_reward = sum(rewards.values())
        
        # 奖励信息
        reward_info = {
            'total_reward': total_reward,
            'reward_components': rewards,
            'adaptive_weights': adaptive_weights,
            'network_state': {
                'network_load': network_load,
                'qot_distribution': qot_distribution,
                'resource_utilization': resource_utilization
            }
        }
        
        return total_reward, reward_info
    
    def _calculate_network_load(self, state: np.ndarray) -> float:
        """计算网络负载"""
        # 功率分布 (0-79)
        power_distribution = state[0:80]
        # 计算平均负载
        max_power = np.max(power_distribution)
        avg_power = np.mean(power_distribution)
        
        if max_power > 0:
            load_factor = avg_power / max_power
            utilization = np.sum(power_distribution > 0) / len(power_distribution)
            return 0.7 * load_factor + 0.3 * utilization
        return 0.0
    
    def _analyze_qot_distribution(self, state: np.ndarray) -> Dict:
        """分析QoT分布"""
        # OSNR分布 (80-159)
        osnr_distribution = state[80:160]
        active_channels = osnr_distribution[osnr_distribution > 0]
        
        if len(active_channels) > 0:
            return {
                'mean_osnr': np.mean(active_channels),
                'std_osnr': np.std(active_channels),
                'min_osnr': np.min(active_channels),
                'active_ratio': len(active_channels) / len(osnr_distribution)
            }
        return {'mean_osnr': 0, 'std_osnr': 0, 'min_osnr': 0, 'active_ratio': 0}
    
    def _calculate_resource_utilization(self, state: np.ndarray) -> float:
        """计算资源利用率"""
        # 波长利用率
        wavelength_usage = np.sum(state[0:80] > 0) / 80
        
        # 功率利用率
        power_levels = state[0:80]
        power_usage = np.mean(power_levels[power_levels > 0]) if np.any(power_levels > 0) else 0
        
        return 0.6 * wavelength_usage + 0.4 * power_usage
    
    def _calculate_adaptive_weights(self, network_load: float, 
                                  qot_distribution: Dict, 
                                  resource_utilization: float) -> Dict:
        """计算自适应权重"""
        weights = self.base_weights.copy()
        
        # 基于网络负载调整
        if network_load > 0.7:  # 高负载
            weights['qot_success'] *= 1.2
            weights['network_impact'] *= 1.5
            weights['efficiency'] *= 0.8
        elif network_load < 0.3:  # 低负载
            weights['efficiency'] *= 1.3
            weights['qot_success'] *= 0.9
        
        # 基于QoT分布调整
        if qot_distribution['active_ratio'] > 0 and qot_distribution['std_osnr'] > 3.0:
            # QoT不均匀，加强质量控制
            weights['qot_margin'] *= 1.4
            weights['network_impact'] *= 1.3
        
        # 基于资源利用率调整
        if resource_utilization > 0.8:
            weights['efficiency'] *= 1.2
            weights['blocking_penalty'] *= 1.1
        
        # 确保权重在合理范围内
        for key in weights:
            weights[key] = max(self.weight_bounds['min'], 
                             min(self.weight_bounds['max'], weights[key]))
        
        return weights
    
    def _calculate_power_efficiency(self, power_action: int) -> float:
        """计算功率效率"""
        # 功率动作范围：0-19 对应 -10dBm 到 +10dBm
        power_dbm = -10.0 + power_action * 1.0
        optimal_power = 0.0  # 最优功率
        
        # 使用高斯函数
        efficiency = math.exp(-((power_dbm - optimal_power) ** 2) / (2 * 5.0 ** 2))
        return efficiency
    
    def _calculate_spectral_efficiency(self, modulation_action: int) -> float:
        """计算频谱效率"""
        modulation_efficiency = {
            0: 0.5,  # QPSK
            1: 0.75, # 8QAM
            2: 1.0,  # 16QAM
            3: 1.25  # 32QAM
        }
        return modulation_efficiency.get(modulation_action, 0.5)

class AdvancedExplorationStrategy:
    """
    高级探索策略
    """
    
    def __init__(self, action_dims: List[int], strategy: str = 'ucb'):
        self.action_dims = action_dims
        self.strategy = strategy
        self.action_counts = {}
        self.q_values = {}
        self.episode = 0
        
    def select_action(self, state: np.ndarray, q_network: nn.Module, 
                     epsilon: float = 0.1) -> np.ndarray:
        """
        选择动作
        """
        if self.strategy == 'ucb':
            return self._ucb_action_selection(state, q_network)
        elif self.strategy == 'thompson':
            return self._thompson_sampling(state, q_network)
        elif self.strategy == 'boltzmann':
            return self._boltzmann_exploration(state, q_network, epsilon)
        else:
            return self._epsilon_greedy(state, q_network, epsilon)
    
    def _ucb_action_selection(self, state: np.ndarray, q_network: nn.Module) -> np.ndarray:
        """
        Upper Confidence Bound 动作选择
        """
        state_tensor = torch.FloatTensor(state).unsqueeze(0)
        
        with torch.no_grad():
            q_values = q_network(state_tensor)
        
        actions = []
        for i, (q_vals, action_dim) in enumerate(zip(q_values, self.action_dims)):
            ucb_values = []
            total_counts = sum(self.action_counts.get(f"{i}_{a}", 0) for a in range(action_dim))
            
            for a in range(action_dim):
                key = f"{i}_{a}"
                count = self.action_counts.get(key, 0)
                q_val = q_vals[0, a].item()
                
                if count == 0:
                    ucb_val = float('inf')
                else:
                    confidence = math.sqrt(2 * math.log(total_counts + 1) / count)
                    ucb_val = q_val + confidence
                
                ucb_values.append(ucb_val)
            
            selected_action = np.argmax(ucb_values)
            actions.append(selected_action)
            
            # 更新计数
            key = f"{i}_{selected_action}"
            self.action_counts[key] = self.action_counts.get(key, 0) + 1
        
        return np.array(actions)
    
    def _thompson_sampling(self, state: np.ndarray, q_network: nn.Module) -> np.ndarray:
        """
        Thompson Sampling
        """
        state_tensor = torch.FloatTensor(state).unsqueeze(0)
        
        with torch.no_grad():
            q_values = q_network(state_tensor)
        
        actions = []
        for i, (q_vals, action_dim) in enumerate(zip(q_values, self.action_dims)):
            # 添加噪声进行采样
            noise = torch.randn_like(q_vals) * 0.1
            noisy_q = q_vals + noise
            
            selected_action = torch.argmax(noisy_q[0]).item()
            actions.append(selected_action)
        
        return np.array(actions)
    
    def _boltzmann_exploration(self, state: np.ndarray, q_network: nn.Module, 
                              temperature: float = 1.0) -> np.ndarray:
        """
        Boltzmann探索
        """
        state_tensor = torch.FloatTensor(state).unsqueeze(0)
        
        with torch.no_grad():
            q_values = q_network(state_tensor)
        
        actions = []
        for i, (q_vals, action_dim) in enumerate(zip(q_values, self.action_dims)):
            # 温度调节的softmax
            probs = F.softmax(q_vals[0] / temperature, dim=0)
            
            # 根据概率分布采样
            selected_action = torch.multinomial(probs, 1).item()
            actions.append(selected_action)
        
        return np.array(actions)
    
    def _epsilon_greedy(self, state: np.ndarray, q_network: nn.Module, 
                       epsilon: float) -> np.ndarray:
        """
        标准epsilon-greedy策略
        """
        if random.random() < epsilon:
            return np.array([random.randint(0, dim-1) for dim in self.action_dims])
        else:
            state_tensor = torch.FloatTensor(state).unsqueeze(0)
            with torch.no_grad():
                q_values = q_network(state_tensor)
                actions = [torch.argmax(q).item() for q in q_values]
            return np.array(actions)
    
    def update_episode(self):
        """更新轮次"""
        self.episode += 1

class EnhancedQoTEnvironment(gym.Env):
    """
    增强版QoT感知光网络环境
    """
    
    def __init__(self, network_file: str, equipment_file: str, 
                 max_wavelengths: int = 80, use_graph_features: bool = True):
        super().__init__()
        
        # 初始化物理系统
        self.qot_calculator = AdvancedQoTCalculator()
        self.reward_calculator = AdaptiveRewardCalculator()
        
        # 网络参数
        self.max_wavelengths = max_wavelengths
        self.use_graph_features = use_graph_features
        
        # 加载网络拓扑
        self.network_topology = self._load_network_topology(network_file)
        self.num_nodes = len(self.network_topology['nodes'])
        
        # 加载设备配置
        self.equipment_config = self._load_equipment_config(equipment_file)
        
        # 状态空间设计
        base_state_dim = max_wavelengths * 3 + 8  # 基础物理状态
        if use_graph_features:
            graph_state_dim = 64  # 图特征维度
            total_state_dim = base_state_dim + graph_state_dim
        else:
            total_state_dim = base_state_dim
            
        self.observation_space = spaces.Box(
            low=-np.inf, high=np.inf, shape=(total_state_dim,), dtype=np.float32
        )
        
        # 动作空间
        self.action_space = spaces.MultiDiscrete([
            5,    # K=5条候选路径
            max_wavelengths,  # 波长选择
            20,   # 功率等级
            4     # 调制格式
        ])
        
        # 图神经网络（如果使用）
        if use_graph_features:
            self.gnn = GraphNeuralNetwork(
                node_features=6,  # 节点特征维度
                edge_features=4,  # 边特征维度
                hidden_dim=64
            )
        
        # 环境状态
        self.current_lightpaths = []
        self.current_request = None
        self.candidate_paths = []
        self.network_graph = None
        
        # 性能统计
        self.performance_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'blocked_requests': 0,
            'average_osnr': 0.0,
            'qot_violations': 0
        }
        
    def _load_network_topology(self, network_file: str) -> Dict:
        """加载网络拓扑"""
        try:
            with open(network_file, 'r') as f:
                topology = json.load(f)
            return topology
        except FileNotFoundError:
            # 创建默认拓扑
            return self._create_default_topology()
    
    def _create_default_topology(self) -> Dict:
        """创建默认网络拓扑"""
        # 简单的6节点网络
        nodes = [{'id': i, 'name': f'Node_{i}'} for i in range(6)]
        edges = [
            {'source': 0, 'target': 1, 'length': 100},
            {'source': 1, 'target': 2, 'length': 150},
            {'source': 2, 'target': 3, 'length': 200},
            {'source': 3, 'target': 4, 'length': 120},
            {'source': 4, 'target': 5, 'length': 80},
            {'source': 0, 'target': 5, 'length': 300}
        ]
        
        return {'nodes': nodes, 'edges': edges}
    
    def _load_equipment_config(self, equipment_file: str) -> Dict:
        """加载设备配置"""
        try:
            with open(equipment_file, 'r') as f:
                config = json.load(f)
            return config
        except FileNotFoundError:
            # 创建默认配置
            return self._create_default_equipment_config()
    
    def _create_default_equipment_config(self) -> Dict:
        """创建默认设备配置"""
        return {
            'amplifiers': {
                str(i): {
                    'noise_figure': 5.0,  # dB
                    'gain': 20.0,         # dB
                    'saturation_power': 20.0  # dBm
                } for i in range(self.num_nodes)
            },
            'fiber_spans': {
                f"{i}-{j}": {
                    'length': 100.0,      # km
                    'attenuation': 0.2,   # dB/km
                    'dispersion': 16.0    # ps/nm/km
                } for i in range(self.num_nodes) for j in range(self.num_nodes) if i != j
            }
        }
    
    def reset(self) -> np.ndarray:
        """重置环境"""
        self.current_lightpaths = []
        self.current_request = self._generate_request()
        self.candidate_paths = self._get_k_shortest_paths(
            self.current_request['source'], 
            self.current_request['destination']
        )
        
        # 重置性能统计
        self.performance_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'blocked_requests': 0,
            'average_osnr': 0.0,
            'qot_violations': 0
        }
        
        return self._get_state()
    
    def step(self, action: np.ndarray) -> Tuple[np.ndarray, float, bool, Dict]:
        """执行动作"""
        path_idx, wavelength, power_level, modulation_idx = action
        
        # 更新统计
        self.performance_stats['total_requests'] += 1
        
        # 动作有效性检查
        if path_idx >= len(self.candidate_paths):
            reward = -50.0
            info = {'success': False, 'error': 'Invalid path index'}
            self.performance_stats['blocked_requests'] += 1
        else:
            # 解析动作
            selected_path = self.candidate_paths[path_idx]
            power_dbm = -10.0 + power_level * 1.0
            modulation_formats = ['QPSK', '8QAM', '16QAM', '32QAM']
            modulation = modulation_formats[modulation_idx]
            
            # 计算QoT
            qot_result = self.qot_calculator.calculate_osnr(
                selected_path, wavelength, power_dbm, 
                self.current_lightpaths, self.equipment_config['amplifiers']
            )
            
            # 计算奖励
            network_state = {
                'required_osnr': self.current_request['required_osnr'],
                'predicted_degradation': self._estimate_network_degradation(selected_path, wavelength)
            }
            
            reward, reward_info = self.reward_calculator.calculate_adaptive_reward(
                self._get_state(), action, qot_result, network_state
            )
            
            # 更新环境状态
            if qot_result['success'] and qot_result['osnr_db'] >= self.current_request['required_osnr']:
                # 成功建立光路
                new_lightpath = {
                    'path': selected_path,
                    'wavelength': wavelength,
                    'power_dbm': power_dbm,
                    'modulation': modulation,
                    'osnr_db': qot_result['osnr_db'],
                    'request_id': self.current_request['id']
                }
                self.current_lightpaths.append(new_lightpath)
                self.performance_stats['successful_requests'] += 1
                
                # 更新平均OSNR
                total_osnr = sum(lp['osnr_db'] for lp in self.current_lightpaths)
                self.performance_stats['average_osnr'] = total_osnr / len(self.current_lightpaths)
                
                info = {
                    'success': True,
                    'osnr_db': qot_result['osnr_db'],
                    'modulation': modulation,
                    'reward_info': reward_info
                }
            else:
                self.performance_stats['blocked_requests'] += 1
                info = {
                    'success': False,
                    'error': 'QoT constraint violation',
                    'reward_info': reward_info
                }
        
        # 生成下一个请求
        self.current_request = self._generate_request()
        self.candidate_paths = self._get_k_shortest_paths(
            self.current_request['source'],
            self.current_request['destination']
        )
        
        # 获取下一个状态
        next_state = self._get_state()
        
        # 终止条件
        done = len(self.current_lightpaths) >= 100  # 最大100条光路
        
        return next_state, reward, done, info
    
    def _get_state(self) -> np.ndarray:
        """获取当前状态"""
        # 基础物理状态
        state = np.zeros(self.observation_space.shape[0])
        
        # 1. 波长功率分布 (0-79)
        for lp in self.current_lightpaths:
            if lp['wavelength'] < self.max_wavelengths:
                state[lp['wavelength']] += 10**(lp['power_dbm']/10)
        
        # 2. OSNR分布 (80-159)
        for lp in self.current_lightpaths:
            if lp['wavelength'] < self.max_wavelengths:
                state[80 + lp['wavelength']] = lp['osnr_db']
        
        # 3. 非线性噪声分布 (160-239)
        for wavelength in range(self.max_wavelengths):
            noise_level = 0.0
            for lp in self.current_lightpaths:
                if abs(lp['wavelength'] - wavelength) <= 3:
                    noise_level += 10**(lp['power_dbm']/10)
            state[160 + wavelength] = noise_level
        
        # 4. 当前请求信息 (240-247)
        if self.current_request:
            state[240] = self.current_request['source'] / self.num_nodes
            state[241] = self.current_request['destination'] / self.num_nodes
            state[242] = self.current_request['required_osnr'] / 30.0
            state[243] = len(self.candidate_paths) / 5.0
            state[244] = self.current_request.get('priority', 1) / 3.0
            state[245] = self.current_request.get('bandwidth', 1) / 10.0
            state[246] = len(self.current_lightpaths) / 100.0
            state[247] = self.performance_stats['successful_requests'] / max(1, self.performance_stats['total_requests'])
        
        # 5. 图特征（如果使用）
        if self.use_graph_features:
            graph_features = self._get_graph_features()
            state[248:312] = graph_features
        
        return state.astype(np.float32)
    
    def _get_graph_features(self) -> np.ndarray:
        """获取图特征"""
        if not hasattr(self, 'gnn'):
            return np.zeros(64)
        
        # 构建图数据
        node_features = []
        edge_index = []
        
        # 节点特征：[度数, 平均OSNR, 功率负载, 波长利用率, 位置x, 位置y]
        for i, node in enumerate(self.network_topology['nodes']):
            degree = len([e for e in self.network_topology['edges'] 
                         if e['source'] == i or e['target'] == i])
            
            # 计算该节点的平均OSNR
            node_osnr = np.mean([lp['osnr_db'] for lp in self.current_lightpaths 
                               if i in lp['path']]) if self.current_lightpaths else 0.0
            
            # 计算功率负载
            node_power = sum([10**(lp['power_dbm']/10) for lp in self.current_lightpaths 
                            if i in lp['path']])
            
            # 波长利用率
            used_wavelengths = set([lp['wavelength'] for lp in self.current_lightpaths 
                                   if i in lp['path']])
            wavelength_util = len(used_wavelengths) / self.max_wavelengths
            
            # 位置信息（如果有）
            pos_x = node.get('x', 0.0)
            pos_y = node.get('y', 0.0)
            
            node_features.append([degree, node_osnr, node_power, wavelength_util, pos_x, pos_y])
        
        # 边信息
        for edge in self.network_topology['edges']:
            edge_index.append([edge['source'], edge['target']])
            edge_index.append([edge['target'], edge['source']])  # 无向图
        
        # 转换为张量
        node_features = torch.FloatTensor(node_features)
        edge_index = torch.LongTensor(edge_index).t()
        
        # 通过GNN获取图嵌入
        with torch.no_grad():
            graph_embedding = self.gnn(node_features, edge_index)
        
        return graph_embedding.numpy().flatten()[:64]
    
    def _generate_request(self) -> Dict:
        """生成光路请求"""
        request_id = random.randint(1000, 9999)
        source = random.randint(0, self.num_nodes - 1)
        destination = random.randint(0, self.num_nodes - 1)
        
        while destination == source:
            destination = random.randint(0, self.num_nodes - 1)
        
        # 随机选择调制格式及其对应的OSNR需求
        modulation_requirements = {
            'QPSK': 12.0,
            '8QAM': 15.0,
            '16QAM': 18.0,
            '32QAM': 21.0
        }
        
        modulation = random.choice(list(modulation_requirements.keys()))
        base_osnr = modulation_requirements[modulation]
        
        # 添加随机裕度
        margin = random.uniform(0.0, 3.0)
        required_osnr = base_osnr + margin
        
        return {
            'id': request_id,
            'source': source,
            'destination': destination,
            'required_osnr': required_osnr,
            'modulation': modulation,
            'priority': random.randint(1, 3),
            'bandwidth': random.randint(1, 10)
        }
    
    def _get_k_shortest_paths(self, source: int, destination: int, k: int = 5) -> List[List[int]]:
        """获取K最短路径"""
        # 构建NetworkX图
        G = nx.Graph()
        
        # 添加节点
        for node in self.network_topology['nodes']:
            G.add_node(node['id'])
        
        # 添加边
        for edge in self.network_topology['edges']:
            G.add_edge(edge['source'], edge['target'], weight=edge['length'])
        
        # 计算K最短路径
        try:
            paths = list(nx.shortest_simple_paths(G, source, destination, weight='weight'))
            return paths[:k]
        except nx.NetworkXNoPath:
            return [[source, destination]]  # 直接连接作为后备
    
    def _estimate_network_degradation(self, path: List[int], wavelength: int) -> float:
        """估算网络降级"""
        # 简化的降级估算
        degradation = 0.0
        
        for lp in self.current_lightpaths:
            if self._paths_share_links(path, lp['path']):
                # 计算波长间隔影响
                wavelength_diff = abs(wavelength - lp['wavelength'])
                if wavelength_diff <= 3:
                    degradation += 0.5 / (wavelength_diff + 1)
        
        return degradation
    
    def _paths_share_links(self, path1: List[int], path2: List[int]) -> bool:
        """检查两条路径是否共享链路"""
        links1 = set([(path1[i], path1[i+1]) for i in range(len(path1)-1)])
        links2 = set([(path2[i], path2[i+1]) for i in range(len(path2)-1)])
        
        # 考虑双向链路
        links1_bidirectional = links1 | set([(b, a) for a, b in links1])
        links2_bidirectional = links2 | set([(b, a) for a, b in links2])
        
        return len(links1_bidirectional & links2_bidirectional) > 0
    
    def get_performance_stats(self) -> Dict:
        """获取性能统计"""
        if self.performance_stats['total_requests'] > 0:
            success_rate = self.performance_stats['successful_requests'] / self.performance_stats['total_requests']
            blocking_rate = self.performance_stats['blocked_requests'] / self.performance_stats['total_requests']
        else:
            success_rate = 0.0
            blocking_rate = 0.0
        
        return {
            'success_rate': success_rate,
            'blocking_rate': blocking_rate,
            'average_osnr': self.performance_stats['average_osnr'],
            'total_lightpaths': len(self.current_lightpaths),
            'network_utilization': len(self.current_lightpaths) / 100.0
        }


def main():
    """主函数 - 演示增强版DRL QoT优化系统"""
    
    # 配置文件路径
    network_file = "Data/CORONET_Global_Topology.json"
    equipment_file = "Data/default_edfa_config.json"
    
    try:
        # 创建增强版环境
        env = EnhancedQoTEnvironment(
            network_file=network_file,
            equipment_file=equipment_file,
            use_graph_features=True
        )
        
        print("🚀 增强版DRL QoT优化系统初始化完成")
        print(f"📊 状态空间维度: {env.observation_space.shape}")
        print(f"🎯 动作空间维度: {env.action_space.nvec}")
        print(f"🌐 网络节点数: {env.num_nodes}")
        
        # 测试环境
        state = env.reset()
        print(f"✅ 环境重置完成, 初始状态形状: {state.shape}")
        
        # 随机策略测试
        print("\n🧪 开始随机策略测试...")
        for episode in range(5):
            state = env.reset()
            total_reward = 0
            steps = 0
            
            while steps < 10:  # 每轮最多10步
                action = env.action_space.sample()
                next_state, reward, done, info = env.step(action)
                
                total_reward += reward
                steps += 1
                
                if done:
                    break
                    
                state = next_state
            
            stats = env.get_performance_stats()
            print(f"Episode {episode+1}: Reward={total_reward:.2f}, "
                  f"Success Rate={stats['success_rate']:.2%}, "
                  f"Avg OSNR={stats['average_osnr']:.2f}dB")
        
        print("\n📈 随机策略测试完成")
        
    except Exception as e:
        print(f"❌ 系统初始化错误: {e}")
        print("请检查配置文件和依赖项")


if __name__ == "__main__":
    main()