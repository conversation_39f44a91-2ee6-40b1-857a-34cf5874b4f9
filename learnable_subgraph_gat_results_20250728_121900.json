{"experiment_info": {"timestamp": "20250728_121900", "model_type": "LearnableSubgraphGAT", "task": "End-to-End Lightpath Impact Learning", "train_samples": 2250, "test_samples": 750, "model_parameters": 108260}, "results": {"final_test_accuracy": 0.5973333333333334, "best_accuracy": 0.5973333333333334, "precision": 0.35680711111111113, "recall": 0.5973333333333334, "f1_score": 0.44675347801892046}, "training_history": {"train_losses": [0.6750569981005456, 0.6746221792962817, 0.6744162551694446, 0.6739687557352914, 0.6739790174100134, 0.6747165999147627, 0.6746139983468585, 0.674559735722012, 0.6741225520239936, 0.6750109144581689, 0.6743774887190924, 0.6742821074989107, 0.6746826882362366, 0.674686452600691, 0.6747614623440636, 0.6750160691473219, 0.6748498292499119, 0.6748292710218164, 0.6748197339309586, 0.6749554438326094, 0.6742660561137729, 0.6746958207024468, 0.674974057747258, 0.6750432693296009, 0.674447866347101, 0.6744233642684089, 0.6743606572945913, 0.6752712599303987, 0.6744536988602744, 0.6743419291973114, 0.6744087479776806, 0.674416842619578, 0.6751117239660687, 0.6748175497055053, 0.6748127452598678, 0.6742942126459546, 0.6743129670090146, 0.6747348538637161, 0.6745104629993439, 0.6755150628752179, 0.6743532752460903, 0.674649263381958, 0.67461511654324, 0.6744239362478256, 0.6747750500175688, 0.6749478656848271, 0.6744546627865897, 0.6745588493347168, 0.674800813264317, 0.6747969672547446, 0.6743820101420085, 0.6743549374871783, 0.6747111379040612, 0.6746413834095001, 0.6750600713292758, 0.6746530230045319, 0.674425618396865, 0.6744173270199034, 0.6744184600114822, 0.6744202856620153, 0.6744939516252941, 0.674511009865337, 0.6746021010080974, 0.6744681459532844, 0.6744103428787656, 0.6745836298598183, 0.6747090763383442, 0.6743902316358354, 0.6742355486684375, 0.6745595552523931, 0.6745629907978905, 0.6742578621837828, 0.6743698711527718, 0.6744030632045533, 0.674434166762564, 0.6743568728897307, 0.6744598610136244, 0.6748329875469208, 0.674210582640436, 0.6745361649327808], "train_accuracies": [0.5977777777777777, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5955555555555555, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5946666666666667, 0.5973333333333334, 0.5973333333333334, 0.5955555555555555, 0.596, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5955555555555555, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5951111111111111, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5951111111111111, 0.5955555555555555, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5968888888888889, 0.5973333333333334, 0.5964444444444444, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334, 0.5973333333333334]}}