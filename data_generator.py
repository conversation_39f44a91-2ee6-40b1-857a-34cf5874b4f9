"""
基于物理模型的数据生成器
生成真实的动态光网络数据，用于训练动态子图GNN
"""

import numpy as np
import json
import random
from typing import Dict, List, Tuple
from physics_qot_system import PhysicsQoTSystem
import pickle
from pathlib import Path

class DynamicDataGenerator:
    """动态光网络数据生成器"""
    
    def __init__(self, physics_system: PhysicsQoTSystem):
        """
        初始化数据生成器
        
        Args:
            physics_system: 物理QoT计算系统
        """
        self.physics = physics_system
        self.active_lightpaths = {}  # 当前活跃的光路
        self.lightpath_counter = 0
        
        print("✅ Dynamic data generator initialized")
    
    def generate_dynamic_scenario(self, num_events: int = 200, 
                                arrival_rate: float = 0.1,
                                holding_time_mean: float = 100):
        """
        生成动态场景数据 (修改为生成器)
        
        Args:
            num_events: 事件总数
            arrival_rate: 到达率
            holding_time_mean: 平均持续时间
            
        Yields:
            单个训练样本
        """
        print(f"🚀 Generating dynamic scenario with {num_events} events")
        
        # 生成事件时间序列
        events = []
        current_time = 0
        
        # 生成建立事件
        for _ in range(num_events):
            # 光路到达时间
            arrival_time = current_time + np.random.exponential(1.0 / arrival_rate)
            
            # 生成光路请求
            request = self.physics.generate_realistic_request()
            
            # 持续时间
            holding_time = np.random.exponential(holding_time_mean)
            teardown_time = arrival_time + holding_time
            
            events.append({
                'time': arrival_time,
                'type': 'establish',
                'request': request,
                'teardown_time': teardown_time
            })
            
            current_time = arrival_time
        
        # 按时间排序
        events.sort(key=lambda x: x['time'])
        
        # 处理事件并生成训练数据
        num_generated = 0
        
        for event in events:
            if event['type'] == 'establish':
                sample = self._process_establishment_event(event)
                if sample:
                    yield sample
                    num_generated += 1
                
                # 安排拆除事件
                self._schedule_teardown(event['teardown_time'], 
                                      self.lightpath_counter - 1)
        
        print(f"✅ Generated {num_generated} training samples")
    
    def _process_establishment_event(self, event: Dict) -> Dict:
        """处理光路建立事件"""
        request = event['request']
        
        # 计算新光路的QoT
        qot_result = self.physics.calculate_lightpath_qot(
            request['source'], request['destination'],
            request['wavelength'], request['power_dbm']
        )
        
        if not qot_result['success']:
            return None  # 光路不可行，跳过
        
        # 捕获建立前的网络状态
        before_state = self._capture_network_state()
        
        # 识别受影响的光路
        new_lightpath = {
            'path': qot_result['path'],
            'wavelength': request['wavelength'],
            'power_dbm': request['power_dbm'],
            'osnr_db': qot_result['osnr_db']
        }
        
        affected_lightpaths = []
        qot_changes = {}
        
        # 计算对已有光路的影响
        existing_lps = []
        for lp_id, lp_info in self.active_lightpaths.items():
            existing_lps.append({
                'id': lp_id,
                'path': lp_info['path'],
                'wavelength': lp_info['wavelength'],
                'power_dbm': lp_info['power_dbm'],
                'osnr_db': lp_info['osnr_db']
            })
        
        if existing_lps:
            impacts = self.physics.calculate_crosstalk_impact(existing_lps, new_lightpath)
            
            for lp_id, impact in impacts.items():
                if impact['osnr_degradation_db'] > 0.01:  # 阈值：10mB
                    affected_lightpaths.append(lp_id)
                    qot_changes[lp_id] = {
                        'before_osnr': impact['original_osnr'],
                        'after_osnr': impact['degraded_osnr'],
                        'degradation': impact['osnr_degradation_db']
                    }
                    
                    # 更新已有光路的OSNR
                    self.active_lightpaths[lp_id]['osnr_db'] = impact['degraded_osnr']
        
        # 添加新光路到活跃列表
        self.active_lightpaths[self.lightpath_counter] = {
            'request': request,
            'path': qot_result['path'],
            'wavelength': request['wavelength'],
            'power_dbm': request['power_dbm'],
            'osnr_db': qot_result['osnr_db'],
            'establish_time': event['time']
        }
        
        # 捕获建立后的网络状态
        after_state = self._capture_network_state()
        
        # 创建训练样本
        training_sample = {
            'event_type': 'establish',
            'time': event['time'],
            'new_lightpath': {
                'id': self.lightpath_counter,
                'source': request['source'],
                'destination': request['destination'],
                'wavelength': request['wavelength'],
                'power_dbm': request['power_dbm'],
                'path': qot_result['path'],
                'osnr_db': qot_result['osnr_db']
            },
            'affected_lightpaths': affected_lightpaths,
            'qot_changes': qot_changes,
            'network_state_before': before_state,
            'network_state_after': after_state,
            'network_load_before': len(before_state['active_lightpaths']),
            'network_load_after': len(after_state['active_lightpaths'])
        }
        
        self.lightpath_counter += 1
        
        return training_sample
    
    def _schedule_teardown(self, teardown_time: float, lightpath_id: int):
        """安排光路拆除（简化处理）"""
        # 在实际实现中，这里应该加入事件队列
        # 为了简化，我们在生成数据时直接处理
        pass
    
    def _capture_network_state(self) -> Dict:
        """捕获当前网络状态"""
        # 波长使用矩阵
        wavelength_usage = np.zeros((self.physics.num_nodes, 
                                   self.physics.num_nodes, 
                                   self.physics.num_wavelengths))
        
        # 功率分布
        power_distribution = {}
        
        # 活跃光路信息
        active_lightpaths = {}
        
        for lp_id, lp_info in self.active_lightpaths.items():
            path = lp_info['path']
            wavelength = lp_info['wavelength']
            power = lp_info['power_dbm']
            
            # 更新波长使用
            for i in range(len(path) - 1):
                src, dst = path[i], path[i + 1]
                wavelength_usage[src, dst, wavelength] = 1
                wavelength_usage[dst, src, wavelength] = 1
            
            # 记录功率分布
            power_distribution[lp_id] = power
            
            # 记录活跃光路
            active_lightpaths[lp_id] = {
                'path': path,
                'wavelength': wavelength,
                'power_dbm': power,
                'osnr_db': lp_info['osnr_db']
            }
        
        return {
            'wavelength_usage': wavelength_usage.tolist(),
            'power_distribution': power_distribution,
            'active_lightpaths': active_lightpaths,
            'network_load': len(active_lightpaths)
        }
    
    def extract_subgraph_features(self, training_sample: Dict) -> Dict:
        """提取动态子图特征"""
        new_lp = training_sample['new_lightpath']
        affected_lps = training_sample['affected_lightpaths']
        
        # 构建影响子图
        subgraph_nodes = set(new_lp['path'])
        subgraph_lightpaths = [new_lp['id']] + affected_lps
        
        # 添加受影响光路的节点
        for lp_id in affected_lps:
            if lp_id in training_sample['network_state_before']['active_lightpaths']:
                lp_path = training_sample['network_state_before']['active_lightpaths'][lp_id]['path']
                subgraph_nodes.update(lp_path)
        
        subgraph_nodes = sorted(list(subgraph_nodes))
        
        # 提取子图特征
        node_features = []
        for node in subgraph_nodes:
            # 节点特征：度数、负载等
            degree = np.sum(self.physics.adjacency_matrix[node])
            
            # 计算节点上的波长使用情况
            wavelength_load = 0
            for lp_id, lp_info in training_sample['network_state_before']['active_lightpaths'].items():
                if node in lp_info['path']:
                    wavelength_load += 1
            
            node_features.append([degree, wavelength_load])
        
        # 边特征
        edge_features = []
        edge_indices = []
        
        for i, node1 in enumerate(subgraph_nodes):
            for j, node2 in enumerate(subgraph_nodes):
                if i < j and self.physics.adjacency_matrix[node1, node2] == 1:
                    # 边特征：长度、负载
                    length = self.physics.fiber_lengths[node1, node2]
                    
                    # 计算边上的波长使用情况
                    edge_load = 0
                    for lp_id, lp_info in training_sample['network_state_before']['active_lightpaths'].items():
                        path = lp_info['path']
                        for k in range(len(path) - 1):
                            if (path[k] == node1 and path[k+1] == node2) or \
                               (path[k] == node2 and path[k+1] == node1):
                                edge_load += 1
                                break
                    
                    edge_features.append([length, edge_load])
                    edge_indices.append([i, j])
        
        return {
            'subgraph_nodes': subgraph_nodes,
            'subgraph_lightpaths': subgraph_lightpaths,
            'node_features': node_features,
            'edge_features': edge_features,
            'edge_indices': edge_indices,
            'target_degradation': sum(change['degradation'] 
                                    for change in training_sample['qot_changes'].values())
        }
    
    def save_dataset(self, training_samples: List[Dict], filename: str):
        """保存数据集"""
        dataset = {
            'training_samples': training_samples,
            'network_info': self.physics.get_network_info(),
            'generation_params': {
                'num_samples': len(training_samples),
                'num_nodes': self.physics.num_nodes,
                'num_wavelengths': self.physics.num_wavelengths
            }
        }
        
        with open(filename, 'wb') as f:
            pickle.dump(dataset, f)
        
        print(f"✅ Dataset saved to {filename}")
        print(f"   Samples: {len(training_samples)}")
        print(f"   File size: {Path(filename).stat().st_size / 1024 / 1024:.2f} MB")

def generate_training_data():
    """生成训练数据的主函数"""
    print("🎯 Generating Physics-based Training Data")
    print("=" * 60)
    
    # 创建物理系统
    physics_system = PhysicsQoTSystem(num_nodes=14, num_wavelengths=80)
    
    # 创建数据生成器
    data_generator = DynamicDataGenerator(physics_system)
    
    # 生成动态场景数据并提取特征
    print(f"\n📊 Extracting subgraph features...")
    subgraph_samples = []
    
    # 使用生成器处理样本，避免一次性加载到内存
    for sample in data_generator.generate_dynamic_scenario(
        num_events=100,
        arrival_rate=0.05,
        holding_time_mean=150
    ):
        if sample['affected_lightpaths']:
            subgraph_features = data_generator.extract_subgraph_features(sample)
            
            # 清理样本中占用内存大的部分
            del sample['network_state_before']
            del sample['network_state_after']
            
            subgraph_samples.append({
                'original_sample': sample,
                'subgraph_features': subgraph_features
            })
    
    print(f"✅ Extracted {len(subgraph_samples)} subgraph samples")
    
    # 保存数据集
    data_generator.save_dataset(subgraph_samples, 'physics_based_dataset.pkl')
    
    # 显示数据统计
    print(f"\n📈 Dataset Statistics:")
    
    degradations = [s['subgraph_features']['target_degradation'] 
                   for s in subgraph_samples]
    
    print(f"   Total samples: {len(subgraph_samples)}")
    print(f"   Average degradation: {np.mean(degradations):.3f} dB")
    print(f"   Max degradation: {np.max(degradations):.3f} dB")
    print(f"   Non-zero degradations: {np.sum(np.array(degradations) > 0)}")
    
    subgraph_sizes = [len(s['subgraph_features']['subgraph_nodes']) 
                     for s in subgraph_samples]
    print(f"   Average subgraph size: {np.mean(subgraph_sizes):.1f} nodes")
    print(f"   Max subgraph size: {np.max(subgraph_sizes)} nodes")
    
    return subgraph_samples

def generate_large_scale_training_data():
    """生成大规模训练数据集"""
    print("🚀 Generating Large-Scale Physics-based Training Data")
    print("=" * 70)
    print("🎯 Target: 5,000+ training samples")
    print("=" * 70)
    
    # 创建物理系统
    physics_system = PhysicsQoTSystem(num_nodes=14, num_wavelengths=80)
    
    all_subgraph_samples = []
    
    # 多轮生成不同规模的场景
    scenarios = [
        {'name': 'Light Load', 'rounds': 20, 'events': 150, 'arrival_rate': 0.08},
        {'name': 'Medium Load', 'rounds': 15, 'events': 200, 'arrival_rate': 0.12},
        {'name': 'Heavy Load', 'rounds': 10, 'events': 300, 'arrival_rate': 0.15},
        {'name': 'Peak Load', 'rounds': 5, 'events': 400, 'arrival_rate': 0.20}
    ]
    
    for scenario in scenarios:
        print(f"\n📊 Generating {scenario['name']} scenarios...")
        print(f"   Rounds: {scenario['rounds']}")
        print(f"   Events per round: {scenario['events']}")
        print(f"   Arrival rate: {scenario['arrival_rate']}")
        
        for round_num in range(scenario['rounds']):
            print(f"   Round {round_num + 1}/{scenario['rounds']}...", end=' ', flush=True)
            
            # 重新初始化数据生成器（清空状态）
            data_generator = DynamicDataGenerator(physics_system)
            
            # 为当前轮次生成样本
            round_samples = []
            for sample in data_generator.generate_dynamic_scenario(
                num_events=scenario['events'],
                arrival_rate=scenario['arrival_rate'],
                holding_time_mean=np.random.uniform(30, 80)
            ):
                if sample['affected_lightpaths']:
                    subgraph_features = data_generator.extract_subgraph_features(sample)
                    if subgraph_features['target_degradation'] > 0:
                        # 清理大对象
                        del sample['network_state_before']
                        del sample['network_state_after']
                        
                        round_samples.append({
                            'original_sample': sample,
                            'subgraph_features': subgraph_features
                        })
            
            all_subgraph_samples.extend(round_samples)
            print(f"✅ {len(round_samples)} valid samples")
    
    print(f"\n🎉 Large-scale data generation completed!")
    print(f"   Total samples: {len(all_subgraph_samples)}")
    
    # 数据质量检查
    if all_subgraph_samples:
        degradations = [s['subgraph_features']['target_degradation'] 
                       for s in all_subgraph_samples]
        subgraph_sizes = [len(s['subgraph_features']['subgraph_nodes']) 
                         for s in all_subgraph_samples]
        
        print(f"\n📈 Large Dataset Statistics:")
        print(f"   Total samples: {len(all_subgraph_samples)}")
        print(f"   QoT degradation range: {min(degradations):.3f} - {max(degradations):.3f} dB")
        print(f"   Average degradation: {np.mean(degradations):.3f} ± {np.std(degradations):.3f} dB")
        print(f"   Subgraph size range: {min(subgraph_sizes)} - {max(subgraph_sizes)} nodes")
        print(f"   Average subgraph size: {np.mean(subgraph_sizes):.1f} ± {np.std(subgraph_sizes):.1f} nodes")
        
        # 保存大规模数据集
        filename = 'large_scale_physics_dataset.pkl'
        dataset = {
            'training_samples': all_subgraph_samples,
            'network_info': physics_system.get_network_info(),
            'generation_params': {
                'num_samples': len(all_subgraph_samples),
                'num_nodes': physics_system.num_nodes,
                'num_wavelengths': physics_system.num_wavelengths,
                'scenarios': scenarios
            }
        }
        
        with open(filename, 'wb') as f:
            pickle.dump(dataset, f)
        
        file_size_mb = Path(filename).stat().st_size / 1024 / 1024
        print(f"\n💾 Large dataset saved: {filename}")
        print(f"   File size: {file_size_mb:.2f} MB")
        print(f"   Improvement: {len(all_subgraph_samples)/96:.1f}x more samples than before")
    
    return len(all_subgraph_samples)

if __name__ == "__main__":
    # 生成大规模数据集
    generate_large_scale_training_data() 