"""
动态子图预测器
使用训练好的GNN模型进行QoT预测
"""

import torch
import numpy as np
import pickle
from typing import Dict, List, Tuple
from torch_geometric.data import Data
from physics_qot_system import PhysicsQoTSystem
from dynamic_subgraph_gnn import DynamicSubgraphGNN

class DynamicSubgraphPredictor:
    """动态子图预测器"""
    
    def __init__(self, model_path: str, physics_system: PhysicsQoTSystem):
        """
        初始化预测器
        
        Args:
            model_path: 训练好的模型路径
            physics_system: 物理QoT系统
        """
        self.physics = physics_system
        
        # 加载模型
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = DynamicSubgraphGNN(
            node_features=2,
            edge_features=2,
            hidden_dim=64,
            num_layers=3
        )
        
        try:
            self.model.load_state_dict(torch.load(model_path, map_location=self.device))
            self.model.to(self.device)
            self.model.eval()
            print(f"✅ Model loaded from {model_path}")
        except FileNotFoundError:
            print(f"⚠️  Model file {model_path} not found, using untrained model")
        
        self.active_lightpaths = {}  # 当前活跃的光路
        self.lightpath_counter = 0
        
    def identify_influence_subgraph(self, new_lightpath: Dict, 
                                  existing_lightpaths: List[Dict]) -> Dict:
        """
        识别影响子图
        
        Args:
            new_lightpath: 新光路信息
            existing_lightpaths: 已有光路列表
            
        Returns:
            影响子图信息
        """
        # 计算影响分数
        influence_scores = {}
        
        for lp in existing_lightpaths:
            # 路径重叠度
            overlap_ratio = self._calculate_path_overlap(
                new_lightpath['path'], lp['path']
            )
            
            # 波长邻近性
            wl_diff = abs(new_lightpath['wavelength'] - lp['wavelength'])
            wavelength_proximity = self._calculate_wavelength_proximity(wl_diff)
            
            # 功率相关性
            power_diff = abs(new_lightpath['power_dbm'] - lp['power_dbm'])
            power_factor = 1.0 + power_diff * 0.1
            
            # 综合影响分数
            influence_score = overlap_ratio * wavelength_proximity * power_factor
            
            if influence_score > 0.01:  # 阈值筛选
                influence_scores[lp['id']] = {
                    'score': influence_score,
                    'overlap_ratio': overlap_ratio,
                    'wavelength_proximity': wavelength_proximity,
                    'power_factor': power_factor,
                    'lightpath': lp
                }
        
        # 选择Top-K受影响的光路
        top_k = min(10, len(influence_scores))  # 最多选择10条
        affected_lightpaths = sorted(
            influence_scores.items(), 
            key=lambda x: x[1]['score'], 
            reverse=True
        )[:top_k]
        
        # 构建影响子图
        subgraph_nodes = set(new_lightpath['path'])
        subgraph_lightpaths = [new_lightpath]
        
        for lp_id, info in affected_lightpaths:
            lp = info['lightpath']
            subgraph_nodes.update(lp['path'])
            subgraph_lightpaths.append(lp)
        
        return {
            'subgraph_nodes': sorted(list(subgraph_nodes)),
            'subgraph_lightpaths': subgraph_lightpaths,
            'affected_lightpath_ids': [lp_id for lp_id, _ in affected_lightpaths],
            'influence_scores': dict(affected_lightpaths)
        }
    
    def extract_subgraph_features(self, subgraph_info: Dict) -> Data:
        """提取子图特征"""
        nodes = subgraph_info['subgraph_nodes']
        lightpaths = subgraph_info['subgraph_lightpaths']
        
        # 节点特征
        node_features = []
        for node in nodes:
            # 节点度数
            degree = np.sum(self.physics.adjacency_matrix[node])
            
            # 节点负载（经过该节点的光路数）
            node_load = 0
            for lp in lightpaths[1:]:  # 排除新光路
                if node in lp['path']:
                    node_load += 1
            
            node_features.append([degree, node_load])
        
        # 边特征和索引
        edge_features = []
        edge_indices = []
        
        for i, node1 in enumerate(nodes):
            for j, node2 in enumerate(nodes):
                if i < j and self.physics.adjacency_matrix[node1, node2] == 1:
                    # 边长度
                    length = self.physics.fiber_lengths[node1, node2]
                    
                    # 边负载（经过该边的光路数）
                    edge_load = 0
                    for lp in lightpaths[1:]:  # 排除新光路
                        path = lp['path']
                        for k in range(len(path) - 1):
                            if (path[k] == node1 and path[k+1] == node2) or \
                               (path[k] == node2 and path[k+1] == node1):
                                edge_load += 1
                                break
                    
                    edge_features.append([length, edge_load])
                    edge_indices.append([i, j])
        
        # 转换为PyTorch张量
        x = torch.tensor(node_features, dtype=torch.float)
        
        if edge_indices:
            edge_index = torch.tensor(edge_indices, dtype=torch.long).t().contiguous()
            edge_attr = torch.tensor(edge_features, dtype=torch.float)
        else:
            edge_index = torch.empty((2, 0), dtype=torch.long)
            edge_attr = torch.empty((0, 2), dtype=torch.float)
        
        return Data(x=x, edge_index=edge_index, edge_attr=edge_attr)
    
    def predict_qot_degradation(self, new_lightpath: Dict, 
                              existing_lightpaths: List[Dict]) -> Dict:
        """
        预测QoT降级
        
        Args:
            new_lightpath: 新光路信息
            existing_lightpaths: 已有光路列表
            
        Returns:
            预测结果
        """
        # 识别影响子图
        subgraph_info = self.identify_influence_subgraph(new_lightpath, existing_lightpaths)
        
        if not subgraph_info['affected_lightpath_ids']:
            return {
                'predicted_degradation': 0.0,
                'affected_lightpaths': [],
                'subgraph_size': len(subgraph_info['subgraph_nodes']),
                'confidence': 1.0
            }
        
        # 提取子图特征
        subgraph_data = self.extract_subgraph_features(subgraph_info)
        subgraph_data = subgraph_data.to(self.device)
        
        # GNN预测
        with torch.no_grad():
            prediction = self.model(
                subgraph_data.x, 
                subgraph_data.edge_index, 
                subgraph_data.edge_attr
            )
            predicted_degradation = prediction.item()
        
        # 计算置信度（基于子图大小和影响分数）
        avg_influence_score = np.mean([
            info['score'] for info in subgraph_info['influence_scores'].values()
        ])
        confidence = min(1.0, avg_influence_score * 2)  # 简单的置信度计算
        
        return {
            'predicted_degradation': max(0.0, predicted_degradation),
            'affected_lightpaths': subgraph_info['affected_lightpath_ids'],
            'subgraph_size': len(subgraph_info['subgraph_nodes']),
            'confidence': confidence,
            'influence_scores': subgraph_info['influence_scores']
        }
    
    def _calculate_path_overlap(self, path1: List[int], path2: List[int]) -> float:
        """计算路径重叠比例"""
        if not path1 or not path2:
            return 0.0
        
        edges1 = set()
        edges2 = set()
        
        for i in range(len(path1) - 1):
            edges1.add(tuple(sorted([path1[i], path1[i+1]])))
        
        for i in range(len(path2) - 1):
            edges2.add(tuple(sorted([path2[i], path2[i+1]])))
        
        if not edges2:
            return 0.0
        
        overlap_edges = len(edges1.intersection(edges2))
        return overlap_edges / len(edges2)
    
    def _calculate_wavelength_proximity(self, wavelength_diff: int) -> float:
        """计算波长邻近性"""
        if wavelength_diff == 0:
            return 1.0
        elif wavelength_diff == 1:
            return 0.8
        elif wavelength_diff == 2:
            return 0.6
        elif wavelength_diff <= 4:
            return 0.3
        else:
            return 0.1
    
    def add_lightpath(self, lightpath_request: Dict) -> Dict:
        """
        添加新光路并预测影响
        
        Args:
            lightpath_request: 光路请求
            
        Returns:
            预测结果和实际QoT
        """
        # 计算新光路的QoT
        qot_result = self.physics.calculate_lightpath_qot(
            lightpath_request['source'],
            lightpath_request['destination'],
            lightpath_request['wavelength'],
            lightpath_request['power_dbm']
        )
        
        if not qot_result['success']:
            return {
                'success': False,
                'error': 'Lightpath not feasible'
            }
        
        # 准备新光路信息
        new_lightpath = {
            'id': self.lightpath_counter,
            'path': qot_result['path'],
            'wavelength': lightpath_request['wavelength'],
            'power_dbm': lightpath_request['power_dbm'],
            'osnr_db': qot_result['osnr_db']
        }
        
        # 获取已有光路
        existing_lightpaths = []
        for lp_id, lp_info in self.active_lightpaths.items():
            existing_lightpaths.append({
                'id': lp_id,
                'path': lp_info['path'],
                'wavelength': lp_info['wavelength'],
                'power_dbm': lp_info['power_dbm'],
                'osnr_db': lp_info['osnr_db']
            })
        
        # GNN预测
        gnn_prediction = self.predict_qot_degradation(new_lightpath, existing_lightpaths)
        
        # 物理模型计算（作为真实值）
        if existing_lightpaths:
            physical_impacts = self.physics.calculate_crosstalk_impact(
                existing_lightpaths, new_lightpath
            )
            actual_degradation = sum(
                impact['osnr_degradation_db'] 
                for impact in physical_impacts.values()
            )
        else:
            actual_degradation = 0.0
        
        # 添加到活跃光路
        self.active_lightpaths[self.lightpath_counter] = {
            'path': qot_result['path'],
            'wavelength': lightpath_request['wavelength'],
            'power_dbm': lightpath_request['power_dbm'],
            'osnr_db': qot_result['osnr_db']
        }
        
        self.lightpath_counter += 1
        
        return {
            'success': True,
            'lightpath_id': self.lightpath_counter - 1,
            'new_lightpath_osnr': qot_result['osnr_db'],
            'gnn_prediction': gnn_prediction,
            'actual_degradation': actual_degradation,
            'prediction_error': abs(gnn_prediction['predicted_degradation'] - actual_degradation)
        }

def test_subgraph_predictor():
    """测试动态子图预测器"""
    print("🧪 Testing Dynamic Subgraph Predictor")
    print("=" * 50)
    
    # 创建物理系统
    physics_system = PhysicsQoTSystem(num_nodes=14, num_wavelengths=80)
    
    # 创建预测器
    predictor = DynamicSubgraphPredictor(
        'best_dynamic_subgraph_model.pth', 
        physics_system
    )
    
    # 测试预测
    print(f"\n🔮 Testing Predictions:")
    
    prediction_errors = []
    
    for i in range(10):
        # 生成随机光路请求
        request = physics_system.generate_realistic_request()
        
        # 添加光路并预测
        result = predictor.add_lightpath(request)
        
        if result['success']:
            gnn_pred = result['gnn_prediction']['predicted_degradation']
            actual = result['actual_degradation']
            error = result['prediction_error']
            
            prediction_errors.append(error)
            
            print(f"   LP {i}: {request['source']}→{request['destination']}")
            print(f"     GNN预测: {gnn_pred:.3f} dB")
            print(f"     实际降级: {actual:.3f} dB")
            print(f"     预测误差: {error:.3f} dB")
            print(f"     受影响光路: {len(result['gnn_prediction']['affected_lightpaths'])}")
        else:
            print(f"   LP {i}: Failed - {result.get('error', 'Unknown error')}")
    
    # 统计预测性能
    if prediction_errors:
        print(f"\n📊 Prediction Performance:")
        print(f"   Average error: {np.mean(prediction_errors):.3f} dB")
        print(f"   Max error: {np.max(prediction_errors):.3f} dB")
        print(f"   RMSE: {np.sqrt(np.mean(np.array(prediction_errors)**2)):.3f} dB")
    
    print(f"\n✅ Predictor test completed!")

if __name__ == "__main__":
    test_subgraph_predictor() 