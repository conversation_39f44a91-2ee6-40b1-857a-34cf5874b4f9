# II. Subgraph GAT Methodology

Our intelligent subgraph GAT approach consists of three key components: physics-aware relevance scoring, adaptive subgraph construction, and multi-scale attention processing, as illustrated in Figure 1.

## A. Data Preprocessing and Feature Engineering

We extract comprehensive lightpath parameters from network topology, where each lightpath is characterized by a 10-dimensional feature vector including: (i) Physical parameters such as source/destination nodes, wavelength ($\lambda$), optical power (P), and path length; (ii) Network parameters including span count, node degree, and betweenness centrality; (iii) Geographic features with normalized coordinates (x,y).

Interference labels are assigned based on physical thresholds: lightpaths with OSNR below 15 dB or BER above $1 \times 10^{-3}$ are marked as ``affected.'' Each node is characterized by a 10-dimensional physics-aware feature vector $\mathbf{x}_i = [P_{tx}, \rho_{load}, \lambda_{norm}, d_{cent}, x_{geo}, y_{geo}, \gamma_{NL}, N_{ASE}, D_{param}, \eta_{cap}]^T$, where $P_{tx}$, $\rho_{load}$, $\lambda_{norm}$, $d_{cent}$ represent power, load, wavelength, and centrality; $(x_{geo}, y_{geo})$ are geographic coordinates; and $\gamma_{NL}$, $N_{ASE}$, $D_{param}$, $\eta_{cap}$ capture nonlinearity, noise, dispersion, and capacity characteristics.

## B. Physics-Aware Relevance Scoring Mechanism

For each new lightpath $l_{new}$ and existing lightpath $l_i$, we compute relevance score $S(l_{new}, l_i)$ through multiple physical considerations. The spectral proximity evaluates wavelength-based interference potential as $R_{spectral}(l_{new}, l_i) = \exp(-\alpha \cdot |\lambda_{new} - \lambda_i|^2/2\sigma^2)$, while path overlap assesses physical route sharing through $R_{path}(l_{new}, l_i) = |Path(l_{new}) \cap Path(l_i)| / |Path(l_{new}) \cup Path(l_i)|$. The power correlation captures OSNR mutual influence via $R_{power}(l_{new}, l_i) = P_{signal}(l_i) / (P_{noise}(l_i) + P_{crosstalk}(l_{new}, l_i))$.

The composite relevance score is computed through a learnable neural scoring function:

$$\mathbf{r}_{input} = [\mathbf{h}_{new}, \mathbf{h}_i, |\mathbf{h}_{new} - \mathbf{h}_i|, R_{phy}(l_{new}, l_i)]$$

$$S(l_{new}, l_i) = \sigma(\mathbf{W}_3 \cdot \text{ReLU}(\mathbf{W}_2 \cdot \text{ReLU}(\mathbf{W}_1 \cdot \mathbf{r}_{input} + \mathbf{b}_1) + \mathbf{b}_2) + \mathbf{b}_3)$$

where $\alpha = 0.4$, $\beta = 0.35$, $\gamma = 0.25$ are learned weights, and $\sigma$ is the sigmoid activation function.

## C. Adaptive Subgraph Construction Strategy

Based on relevance scores, we dynamically construct task-specific subgraphs through the following process: First, compute relevance scores $S_i = S(l_{new}, l_i)$ for all lightpaths $l_i \in L$. Then select high-relevance lightpaths $L_{relevant} = \{l_i | S_i > \tau\}$ where $\tau$ is the relevance threshold. Extract involved nodes $V_{sub} = \bigcup\{\text{nodes}(l_i) | l_i \in L_{relevant}\} \cup \text{nodes}(l_{new})$ and add bridging nodes for connectivity if needed. Finally, construct the edge set $E_{sub} = \{(u,v) \in E | u,v \in V_{sub}\}$ to form subgraph $G_{sub} = (V_{sub}, E_{sub})$. 

The subgraph size is adaptively controlled by $k = \max(6, \min(10, \lceil 0.6 \cdot N \rceil))$ to ensure computational efficiency while maintaining connectivity and prediction accuracy.

## D. Multi-Scale GAT Architecture

We enhance standard GAT attention with physics-aware constraints through $\alpha_{ij}^{phy} = \alpha_{ij} \cdot \phi(d_{ij}, |\lambda_i - \lambda_j|)$, where $\phi$ incorporates physical distance and wavelength separation effects. The network captures features at different scales using local attention for 1-hop neighbors and global attention for k-hop neighbors:

$$\mathbf{h}_i^{local} = \sigma\left(\sum_{j \in \mathcal{N}_1(i)} \alpha_{ij}^{local} \mathbf{W}^{local} \mathbf{h}_j\right)$$

$$\mathbf{h}_i^{global} = \sigma\left(\sum_{j \in \mathcal{N}_k(i)} \alpha_{ij}^{global} \mathbf{W}^{global} \mathbf{h}_j\right)$$

$$\mathbf{h}_i^{final} = \mathbf{W}^{fusion}[\mathbf{h}_i^{local} \| \mathbf{h}_i^{global}] + \mathbf{b}^{fusion}$$

Node features are aggregated into graph-level representation through $\mathbf{h}_{graph} = \text{READOUT}(\{\mathbf{h}_i^{final} | i \in V_{sub}\})$. The final binary classifier consists of two fully connected layers (64→32→2) with ReLU activation, outputting lightpath interference predictions.

# III. Experimental Design and Performance Evaluation

## A. Experimental Configuration

Our experiments are conducted on a computing platform equipped with NVIDIA RTX 4050 GPU, 32GB RAM, and Intel i9-12900K processor. The software environment consists of Python 3.9, PyTorch 2.1.0, DGL 1.1.2, and CUDA 11.8. We adopt the 14-node Japanese NSFNET topology with 54 bidirectional fiber links as our testbed network.

We generate 2,500 lightpath configuration scenarios for comprehensive evaluation, with 2,000 scenarios (80\%) allocated for training and 500 scenarios (20\%) for testing. Each scenario involves lightpaths ranging from 2 to 6 hops across 80 C-band wavelength channels. The node features comprise a 10-dimensional physics-aware vector capturing optical power characteristics (transmission power, received power, OSNR value), spectral properties (center wavelength, bandwidth utilization, channel spacing), topological attributes (node degree, betweenness centrality, clustering coefficient), and geographic information (longitude and latitude coordinates). Additionally, we incorporate 6-dimensional edge features representing physical parameters such as fiber length, attenuation coefficient, and dispersion parameter, along with traffic-related metrics including link load, available wavelengths, and congestion level.

## B. Baseline Methods and Evaluation Metrics

We compare our proposed method against four representative approaches to demonstrate its effectiveness. The Full Graph GAT applies standard GAT on the complete 14-node network, while the Learnable Subgraph GAT employs end-to-end learnable subgraph selection. We also include a GraphSAGE-based Traditional GNN as a conventional baseline, alongside our proposed Intelligent Subgraph GAT with physics-aware relevance scoring.

Our evaluation framework encompasses both accuracy and efficiency metrics. For accuracy assessment, we measure classification accuracy, F1-score, precision and recall rates, AUC-ROC curve analysis, and regression $R^2$ coefficient. Efficiency evaluation focuses on single inference time (ms), relative speedup ratio, model size (MB), parameter count, and GPU memory consumption patterns.

## C. Comprehensive Performance Analysis

Table I presents comprehensive evaluation results across 500 test scenarios. Our Intelligent Subgraph GAT achieves 91.50\% test accuracy with an $R^2$ score of 0.7834, while requiring only 0.18 ms inference time and 64,127 parameters. Compared to the Full Graph GAT baseline (91.40\% accuracy, 2.74 ms inference, 312,548 parameters), our method demonstrates a 15.22× speedup with 79.5\% parameter reduction while maintaining competitive accuracy. The Learnable Subgraph GAT achieves 91.20\% accuracy with 10.96× speedup, and the Traditional GNN baseline reaches 89.80\% accuracy with modest 1.45× acceleration.

\begin{table}[htbp]
\centering
\caption{Performance Comparison}
\label{tab:performance}
\begin{tabular}{|l|c|c|c|}
\hline
Method & Acc.(\%) & Time(ms) & Speedup \\
\hline
\textbf{Ours} & \textbf{91.50} & \textbf{0.18} & \textbf{15.22×} \\
Learnable GAT & 91.20 & 0.25 & 10.96× \\
Full Graph GAT & 91.40 & 2.74 & 1.00× \\
Traditional GNN & 89.80 & 1.89 & 1.45× \\
\hline
\end{tabular}
\end{table}

The computational complexity analysis reveals significant advantages of our subgraph approach. While full graph methods exhibit $O(N^2)$ computational operations, our subgraph method achieves $O(k^2)$ complexity where $k \approx 0.6N$ on average. The scalability evaluation across different network sizes demonstrates increasing speedup ratios: $7.42 \times$ for 10 nodes, $15.22 \times$ for 14 nodes, $20.81 \times$ for 20 nodes, and $36.02 \times$ for 30 nodes, confirming the method's excellent scalability for larger networks.

Systematic ablation experiments validate the contribution of each component. Removing physics-aware scoring reduces accuracy by 1.05\% (from 91.50\% to 90.45\%), while eliminating adaptive subgraph construction increases inference time by 700\% without significant accuracy loss. Multi-scale attention contributes 1.27\% accuracy improvement, and dynamic relevance scoring provides 1.61\% performance boost. These results confirm that physics-aware scoring and multi-scale attention enhance prediction accuracy, while adaptive subgraph construction is critical for computational efficiency.

Training dynamics analysis shows that our method converges in 28 epochs with stable learning curves, compared to 58 epochs required by the full graph baseline with oscillation patterns. This represents a $2.1 \times$ improvement in training efficiency while maintaining consistent validation performance throughout the training process. The superior computational efficiency combined with competitive accuracy makes our approach highly suitable for real-time optical network QoT estimation applications.