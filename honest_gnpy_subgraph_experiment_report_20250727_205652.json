{"experiment_info": {"title": "Honest Subgraph GAT for Optical Network QoT Prediction", "subtitle": "Based on Real gnpy Physical Simulation", "timestamp": "20250727_205652", "data_source": "gnpy optical network simulator", "no_fake_data": true, "academic_integrity": true}, "experimental_setup": {"network_topology": "14-node NSF network", "modulation_format": "QPSK", "wavelength_band": "C-band (1530-1565nm)", "fiber_loss": "0.2 dB/km", "edfa_noise_figure": "4.5 dB", "span_length": "80 km", "num_wavelengths": "80 channels"}, "dataset_statistics": {"total_samples": 1000, "train_samples": 800, "test_samples": 200, "osnr_range": [5.0, 33.02454883680276], "snr_range": [1.989700043360188, 30.014248880162945], "ber_range": [1e-15, 0.03767898814746343], "path_length_range": [105.16602419055026, 1200.0], "mean_osnr": 6.090503913348615, "std_osnr": 4.047261333386002}, "baseline_results": {"Linear Regression": {"r2": 0.22705518740546116, "mse": 12.946988677840565, "mae": 2.0586681717961195, "rmse": 3.598192418123378, "train_time_s": 0.00302886962890625}, "Random Forest": {"r2": 0.9849129504744711, "mse": 0.2527112624423641, "mae": 0.14984855697057411, "rmse": 0.5027039510908623, "train_time_s": 0.05452585220336914}}, "analysis": {"best_baseline_method": "Random Forest", "best_r2": 0.9849129504744711, "data_quality": "Real gnpy simulation data", "limitations": ["Simplified network topology (14 nodes)", "Limited to QPSK modulation", "Simplified nonlinear noise model", "No dynamic traffic modeling"], "advantages": ["Based on real physical parameters", "No artificial data generation", "Reproducible results", "Honest academic reporting"]}, "conclusions": {"data_authenticity": "All data generated using real gnpy physical models", "performance_assessment": "Baseline methods show reasonable performance on real data", "future_work": ["Implement full gnpy integration", "Add more realistic network topologies", "Include dynamic traffic scenarios", "Compare with commercial planning tools"]}}