import numpy as np
import pandas as pd
import os
import sys
import random
import argparse
import time
import torch
import dgl
import matplotlib
matplotlib.use('Agg')
from dgl import DGLGraph
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm

plt.rcParams['font.sans-serif'] = ['Noto Sans CJK JP', 'Noto Sans CJK KR', 'Noto Sans CJK SC', 'Noto Sans CJK TC',
                                   'sans-serif']  # 优先使用Noto Sans CJK系列
plt.rcParams['axes.unicode_minus'] = False  # 正确显示负号

# 添加GNPy路径
# sys.path.append(os.path.join(os.getcwd(), 'oopt-gnpy-master'))

try:
    from gnpy.core.elements import Transceiver, Fiber, Edfa
    from gnpy.core.utils import lin2db, db2lin
    from gnpy.core.info import create_input_spectral_information
    from gnpy.core.network import build_network
except ImportError:
    print("错误: 无法导入GNPy库。请确保GNPy已正确安装。")
    sys.exit(1)

# 基本参数设置
channel_num = 80  # 波长通道数量
wavelength_start = 191.3e12  # 起始波长频率
wavelength_spacing = 50e9  # 波长间隔
random.seed(42)  # 设置随机种子，保证结果可复现


def create_jnet_topology():
    """
    创建日本网络(Jnet)拓扑
    
    Returns:
        g: DGL图对象
        link_length: 链路长度矩阵
        node_coords: 节点坐标（用于可视化）
    """
    print("创建日本网络拓扑...")

    # 定义边的源节点和目标节点 - 基于真实Jnet拓扑（0-13对应图中1-14）
    u, v = torch.tensor([0, 0, 1, 2, 2, 3, 4, 4, 5, 6, 6, 7, 8, 9, 9, 10, 10, 11, 11]), \
        torch.tensor([1, 2, 3, 4, 8, 5, 5, 6, 7, 7, 8, 9, 13, 10, 11, 11, 12, 12, 13])
    #u[3] = 2 和 v[3] = 4  节点 2 和节点 4 之间有一条边
    # 创建图
    g = dgl.graph((u, v))
    g = g.int()
    g = dgl.to_bidirected(g)  # 转换为双向图

    # 定义链路长度（单位：km） - 基于真实Jnet拓扑
    link_length = np.array([
        [0,   160, 240, 0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0  ],  # Node 1
        [160, 0,   0,   240, 0,   0,   0,   0,   0,   0,   0,   0,   0,   0  ],  # Node 2
        [240, 0,   0,   0,   240, 0,   0,   0,   240, 0,   0,   0,   0,   0  ],  # Node 3
        [0,   240, 0,   0,   0,   40,  0,   0,   0,   0,   0,   0,   0,   0  ],  # Node 4
        [0,   0,   240, 0,   0,   140, 240, 0,   0,   0,   0,   0,   0,   0  ],  # Node 5
        [0,   0,   0,   40,  140, 0,   0,   160, 0,   0,   0,   0,   0,   0  ],  # Node 6
        [0,   0,   0,   0,   240, 0,   0,   160, 240, 0,   0,   0,   0,   0  ],  # Node 7
        [0,   0,   0,   0,   0,   160, 160, 0,   0,   160, 0,   0,   0,   0  ],  # Node 8
        [0,   0,   240, 0,   0,   0,   240, 0,   0,   0,   0,   0,   0,   240],  # Node 9
        [0,   0,   0,   0,   0,   0,   0,   160, 0,   0,   400, 240, 0,   0  ],  # Node 10
        [0,   0,   0,   0,   0,   0,   0,   0,   0,   400, 0,   240, 240, 0  ],  # Node 11
        [0,   0,   0,   0,   0,   0,   0,   0,   0,   240, 240, 0,   160, 240],  # Node 12
        [0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   240, 160, 0,   0  ],  # Node 13
        [0,   0,   0,   0,   0,   0,   0,   0,   240, 0,   0,   240, 0,   0  ]   # Node 14
    ])

    # 定义节点坐标（近似日本地图位置，用于可视化）
    node_coords = [
        (35.6895, 139.6917),  # Tokyo
        (36.3219, 139.0032),  # Maebashi
        (36.6626, 138.3257),  # Nagano
        (37.9161, 139.0364),  # Niigata
        (38.2682, 140.8694),  # Sendai
        (39.7036, 141.1525),  # Morioka
        (40.8244, 140.7400),  # Aomori
        (43.0621, 141.3544),  # Sapporo
        (36.5626, 136.6564),  # Kanazawa
        (34.6932, 135.5021),  # Osaka
        (33.5904, 130.4017),  # Fukuoka
        (35.1709, 136.8816),  # Nagoya
        (32.7503, 129.8779),  # Nagasaki
        (34.3853, 132.4553)  # Hiroshima
    ]

    return g, link_length, node_coords


def create_physical_network(link_length):
    """
    创建物理网络模型，包括光纤、放大器和收发机
    
    Args:
        link_length: 链路长度矩阵
    
    Returns:
        network_elements: 包含网络元素的字典
        fiber_spans: 光纤跨段列表
        nodes: 节点列表
    """
    print("创建物理网络模型...")

    num_nodes = link_length.shape[0]
    network_elements = {}  # 初始化网络元素空字典
    fiber_spans = []
    nodes = []

    # 创建节点（收发机）
    for i in range(num_nodes):
        node_name = f"Node{i + 1}"
        tx = Transceiver(name=f"{node_name}_tx", uid=f"{node_name}_tx")  #GNPY的类，把电信号转光信号发到光纤中
        rx = Transceiver(name=f"{node_name}_rx", uid=f"{node_name}_rx")
        network_elements[tx.uid] = tx
        network_elements[rx.uid] = rx
        nodes.append((tx, rx))  #将RX和TX放在一个node列表里 相当于拼接

    # 创建链路（光纤和放大器）
    for i in range(num_nodes):
        for j in range(num_nodes):
            if link_length[i, j] > 0:
                # 创建光纤
                fiber_name = f"Fiber_{i + 1}_{j + 1}"
                length = link_length[i, j]

                # 光纤参数
                alpha_db = 0.2  # 衰减系数 (dB/km)
                dispersion = 16.7  # 色散 (ps/nm/km)
                gamma = 1.3  # 非线性系数 (1/W/km)

                fiber = Fiber(
                    name=fiber_name,
                    uid=fiber_name,
                    params={
                        'length': length,
                        'length_units': 'km',
                        'loss_coef': alpha_db,
                        'dispersion': dispersion,
                        'gamma': gamma,
                        'pmd_coef': 0.1e-13  # 添加 pmd_coef, 单位 s/sqrt(m)
                    }
                )

                # 在光纤末端添加EDFA放大器
                edfa_name = f"EDFA_{i + 1}_{j + 1}"
                edfa = Edfa(
                    name=edfa_name,
                    uid=edfa_name,
                    params={
                        'gain_target': alpha_db * length,  # 完全补偿光纤损耗
                        'tilt_target': 0,
                        'out_voa': 0,
                        'nf': 5.0  # 噪声系数 (dB)
                    }
                )

                network_elements[fiber.uid] = fiber
                network_elements[edfa.uid] = edfa
                fiber_spans.append((fiber, edfa))  # 将光纤和放大器放在一个fiber_spans列表里

    return network_elements, fiber_spans, nodes


def generate_traffic_matrix(num_nodes, num_requests):
    """
    生成随机业务矩阵
    
    Args:
        num_nodes: 节点数量
        num_requests: 业务请求数量
    
    Returns:
        traffic_matrix: 业务矩阵，每行包含源节点、目标节点和请求带宽
    """
    print(f"生成{num_requests}个随机业务请求...")

    traffic_matrix = []  #初始化业务矩阵

    for _ in range(num_requests):
        source = random.randint(0, num_nodes - 1)
        destination = random.randint(0, num_nodes - 1)
        while destination == source:  # 确保源和目标不同
            destination = random.randint(0, num_nodes - 1)

        # 随机生成带宽需求 (Gbps)
        bandwidth = random.choice([10, 40, 100, 200, 400])

        traffic_matrix.append((source, destination, bandwidth))

    return traffic_matrix  #业务矩阵中包括源节点、目标节点和请求带宽


def run_physical_layer_simulation(network_elements, fiber_spans, nodes, traffic_matrix, num_channels=80):
    """
    运行物理层仿真
    
    Args:
        network_elements: 网络元素字典
        fiber_spans: 光纤跨段列表
        nodes: 节点列表
        traffic_matrix: 业务矩阵
        num_channels: 波长通道数
    
    Returns:
        simulation_results: 仿真结果字典
    """
    print("运行物理层仿真...")

    simulation_results = {
        'power': [],
        'wavelength': [],
        'route': [],
        'snr': []
    }

    # 波长参数
    wavelength = np.zeros((1, num_channels))
    for i in range(num_channels):
        wavelength[0, i] = wavelength_start + i * wavelength_spacing

    # 为每个业务请求分配波长和路由

    # enenumerate 是遍历enumerate()列表同时获取每个元素的值和索引，这里每个元素值是一个包含三个值的元组
    for req_idx, (source, destination, bandwidth) in enumerate(traffic_matrix):
        if req_idx % 10 == 0:
            print(f"\r处理业务请求 {req_idx + 1}/{len(traffic_matrix)}", end="")
        #nodes列表的每个元素是一个包含两个 Transceiver 对象的元组（tx, rx）
        src_tx = nodes[source][0]  # 源节点nodes[source]发射机
        dst_rx = nodes[destination][1]  # 目标节点nodes[destination]的第二个元素接收机

        # 简单路由：直接使用源和目标之间的链路
        route = [source, destination]

        # 检查链路是否存在
        if not any(span[0].uid == f"Fiber_{source + 1}_{destination + 1}" for span in fiber_spans):  #
            # span[0] 表示该元组中的 第一个元素 (fiber, edfa)
            # 如果直接链路不存在，尝试找到一个中间节点
            intermediate_nodes = []
            for i in range(len(nodes)):
                if i != source and i != destination:
                    # 检查 source -> i -> destination 是否都存在链路
                    if any(span[0].uid == f"Fiber_{source + 1}_{i + 1}" for span in fiber_spans) and \
                            any(span[0].uid == f"Fiber_{i + 1}_{destination + 1}" for span in fiber_spans):
                        intermediate_nodes.append(i)

            if intermediate_nodes:
                # 选择第一个可行的中间节点
                inter_node = intermediate_nodes[0]
                route = [source, inter_node, destination]
            else:
                # 无法找到路由，跳过此请求(无法传输)
                continue
                # 段代码的目的：
                # 当两个节点之间没有直接链路时，尝试寻找一个中间节点 i，使得：
                # source -> i 有链路
                # i -> destination 也有链路
        # 随机选择一个波长
        channel = random.randint(0, num_channels - 1)

        # 设置输入功率 (dBm)
        input_power_dbm = random.uniform(-3, 0)
        input_power_lin = db2lin(input_power_dbm) / 1000  # 转换为线性功率 (W)

        # 创建输入信号 返回了频谱信息
        signal = create_input_spectral_information(
            f_min=wavelength_start - wavelength_spacing / 2,
            f_max=wavelength_start + (num_channels - 0.5) * wavelength_spacing,
            roll_off=0.15,
            baud_rate=32e9,  # 32 GBaud
            power=input_power_lin,
            spacing=wavelength_spacing,
            # tx_osnr=40,  # 高发射OSNR
            # nch=num_channels,
            # ch_id=channel
        )

        # 存储仿真结果
        simulation_results['power'].append(input_power_lin)
        simulation_results['wavelength'].append(channel)
        simulation_results['route'].append(route)

        # 模拟信号传输并计算SNR
        try:
            # 简化模拟：使用GN模型估算SNR
            #获取链路总长度，遍历路由路径上的每一段光纤，累加它们的长度
            fiber_length = 0
            for i in range(len(route) - 1):
                src, dst = route[i], route[i + 1]
                fiber_uid = f"Fiber_{src + 1}_{dst + 1}"
                if fiber_uid in network_elements:
                    fiber = network_elements[fiber_uid]
                    fiber_length += fiber.params.length / 1000

            # 使用GN模型估算SNR
            alpha = 0.2 * np.log(10) / 10  # 转换为线性单位
            gamma = 1.3  # 1/W/km
            beta2 = -21.3e-27  # s^2/km (D = 16.7 ps/nm/km)

            # 信道功率 (W)
            p_ch = input_power_lin

            # GN模型计算非线性噪声
            gn_term = (8 / 27) * (gamma ** 2) * (p_ch ** 3) * \
                      (1 - np.exp(-2 * alpha * fiber_length)) / (2 * alpha)

            # ASE噪声（每个EDFA）
            num_edfa = len(route) - 1
            h = 6.62607015e-34  # 普朗克常数
            f = wavelength_start + channel * wavelength_spacing  # 信道频率
            nf_lin = db2lin(5.0)  # 噪声系数（线性单位）
            bandwidth = 32e9  # 带宽 (Hz)

            ase_noise = num_edfa * h * f * bandwidth * nf_lin * (db2lin(alpha * fiber_length) - 1)

            # 计算总SNR
            snr = p_ch / (gn_term + ase_noise)
            snr_db = lin2db(snr)

            simulation_results['snr'].append(snr_db)
        except Exception as e:
            print(f"\n仿真请求 {req_idx + 1} 时出错: {e}")
            simulation_results['snr'].append(10.0)  # 默认值

    print("\n物理层仿真完成")
    return simulation_results


def prepare_training_data(g, simulation_results, link_length, num_samples=2000):
    """
    准备用于训练镜像模型的数据
    
    Args:
        g: DGL图对象
        simulation_results: 仿真结果
        link_length: 链路长度矩阵
        num_samples: 样本数量
    
    Returns:
        training_data: 训练数据字典
    """
    print("准备训练数据...")

    num_nodes = g.num_nodes()

    # 初始化训练数据结构
    training_data = []

    # 为每个样本创建网络状态快照
    for sample_idx in range(num_samples):
        if sample_idx % 100 == 0:
            print(f"\r生成样本 {sample_idx + 1}/{num_samples}", end="")

        # 随机选择部署的光路数量（模拟不同负载）
        max_simulated_routes = len(simulation_results['route'])
        if max_simulated_routes == 0:
            # 如果没有成功的路由，则此样本不包含任何光路
            # 或者可以考虑跳过此样本的生成，或打印警告
            num_lightpaths = 0
            # print(f"警告: 样本 {sample_idx + 1} 没有可用的仿真路由。") # 可选的警告
        else:
            # 确保随机选择的上限不小于1，且不超过实际可用路由数
            # 同时，原来的代码意图是至少有10条，但不超过100条，这里我们调整为更灵活
            # 如果希望保留最小10条的意图，但可用路由不足10，则取所有可用路由
            # current_max = min(100, max_simulated_routes)
            # num_lightpaths = random.randint(min(10, current_max), current_max) if current_max > 0 else 0

            # 更简单的逻辑：在1到实际可用路由数之间随机选择，如果可用路由少，选择范围也小
            # 如果希望在负载较高时模拟更多光路，可以在有足够多路由时调整下限
            if max_simulated_routes < 10 : # 如果可用路由少于10
                 num_lightpaths = random.randint(1, max_simulated_routes) if max_simulated_routes > 0 else 0
            else: # 如果可用路由大于等于10
                 num_lightpaths = random.randint(10, min(100, max_simulated_routes))

        # 初始化网络状态矩阵
        link_waveState = np.zeros((num_nodes, num_nodes, 1, channel_num))
        edge_feature_P = np.zeros((num_nodes, num_nodes, 1, channel_num))

        # 随机选择要部署的光路
        deployed_indices = random.sample(range(len(simulation_results['route'])), num_lightpaths)

        # 部署选定的光路
        for idx in deployed_indices:
            route = simulation_results['route'][idx]
            wavelength = simulation_results['wavelength'][idx]
            power = simulation_results['power'][idx]

            # 更新链路状态
            for i in range(len(route) - 1):
                src, dst = route[i], route[i + 1]
                link_waveState[src, dst, 0, wavelength] = 1
                edge_feature_P[src, dst, 0, wavelength] = power

                # 计算每条光路的SNR（这里使用仿真结果），并包含完整路径信息
                snr_values = {}
                for idx in deployed_indices:
                    route_nodes = simulation_results['route'][idx]  # 路径节点列表
                    wavelength = simulation_results['wavelength'][idx]
                    snr = simulation_results['snr'][idx]
                    power = simulation_results['power'][idx]  # 获取对应的功率

                    if len(route_nodes) >= 2:
                        src, dst = route_nodes[0], route_nodes[-1]
                        # 键包含源、目标、波长和完整路径，值为 (snr, power)
                        snr_values[(src, dst, wavelength, tuple(route_nodes))] = (snr, power)

        # 创建样本数据
        sample = {
            'node_power': np.zeros((num_nodes, 1)),  # 节点功率（输入特征）
            'link_wave': link_waveState,  # 链路波长状态
            'link_power': edge_feature_P,  # 链路功率
            'snr_values': snr_values  # SNR值（标签）
        }

        training_data.append(sample)

    print("\n训练数据准备完成")
    return training_data


def save_data(training_data, simulation_results, output_dir="./data"):
    """
    保存生成的数据
    
    Args:
        training_data: 训练数据
        simulation_results: 仿真结果
        output_dir: 输出目录
    """
    print(f"保存数据到 {output_dir}...")

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 保存训练数据
    for i, data in enumerate(training_data):
        if i % 500 == 0:
            print(f"\r保存样本 {i + 1}/{len(training_data)}", end="")

        # 转换为适合保存的格式
        sample_data = [
            data['node_power'],
            data['link_wave'],
            data['link_power']
        ]

        # 保存为numpy文件
        # np.save(f"{output_dir}/sample_{i}.npy\", np.array(sample_data, dtype=object), allow_pickle=True) # 旧方法
        # 创建一个空的1D对象数组，然后填充，以强制NumPy按预期处理
        sample_data_to_save = np.empty(len(sample_data), dtype=object)
        for k_idx, item_arr in enumerate(sample_data):
            sample_data_to_save[k_idx] = item_arr
        np.save(f"{output_dir}/sample_{i}.npy", sample_data_to_save, allow_pickle=True)

    # 保存SNR值（用于验证）
    snr_df = pd.DataFrame({
        'source': [route[0] for route in simulation_results['route']],
        'destination': [route[-1] for route in simulation_results['route']],
        'wavelength': simulation_results['wavelength'],
        'power': simulation_results['power'],
        'snr': simulation_results['snr']
    })

    snr_df.to_csv(f"{output_dir}/snr_values.csv", index=False)

    # 保存合并的训练数据文件
    combined_data = np.array(training_data, dtype=object)
    np.save(f"{output_dir}/combined_data.npy", combined_data, allow_pickle=True)

    print(f"\n数据已保存到 {output_dir}")


def visualize_network(g, node_coords, link_length, output_file="network_topology.png"):
    """
    可视化网络拓扑
    
    Args:
        g: DGL图对象
        node_coords: 节点坐标
        link_length: 链路长度矩阵
        output_file: 输出文件名
    """
    print(f"可视化网络拓扑并保存到 {output_file}...")
    #  加载中文字体
    font_path = '/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc'  # 直接指定 Noto Sans CJK SC
    if os.path.exists(font_path):
        try:
            my_font = fm.FontProperties(fname=font_path)
            plt.rcParams['font.family'] = my_font.get_name()
            plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
            print(f"已尝试加载字体: {my_font.get_name()} 从 {font_path}")
        except Exception as e:
            print(f"加载字体 {font_path} 失败: {e}. 图形中的中文可能无法正常显示。")
    else:
        print(f"字体文件 {font_path} 不存在。请检查路径或安装中文字体。图形中的中文可能无法正常显示。")
    # 提取坐标
    x = [coord[1] for coord in node_coords]  # 经度作为x坐标
    y = [coord[0] for coord in node_coords]  # 纬度作为y坐标

    plt.figure(figsize=(10, 8))

    # 绘制链路
    for i in range(len(node_coords)):
        for j in range(len(node_coords)):
            if link_length[i, j] > 0:
                plt.plot([x[i], x[j]], [y[i], y[j]], 'k-', alpha=0.6,
                         linewidth=1 + link_length[i, j] / 100)

    # 绘制节点
    plt.scatter(x, y, s=100, c='blue', alpha=0.8)

    # 添加节点标签
    for i, (x_i, y_i) in enumerate(zip(x, y)):
        plt.text(x_i, y_i, f"{i + 1}", fontsize=12, ha='center', va='center')

    plt.title("日本网络(Jnet)拓扑")
    plt.xlabel("经度")
    plt.ylabel("纬度")
    plt.grid(True, linestyle='--', alpha=0.7)

    # 调整坐标轴以匹配日本地图
    plt.xlim(129, 142)
    plt.ylim(31, 44)

    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"网络拓扑已保存到 {output_file}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="生成M-RWMA实验所需的模拟数据")
    parser.add_argument("--num_samples", type=int, default=2000, help="要生成的样本数量")
    parser.add_argument("--num_requests", type=int, default=500, help="要生成的业务请求数量")
    parser.add_argument("--output_dir", type=str, default="./data", help="输出目录")
    args = parser.parse_args()

    print("=== 数据生成工具 ===")
    print(f"样本数量: {args.num_samples}")
    print(f"业务请求数量: {args.num_requests}")
    print(f"输出目录: {args.output_dir}")
    os.makedirs(args.output_dir, exist_ok=True)

    # 创建拓扑
    g, link_length, node_coords = create_jnet_topology()

    # 可视化网络
    visualize_network(g, node_coords, link_length, f"{args.output_dir}/network_topology.png")

    # 创建物理网络
    network_elements, fiber_spans, nodes = create_physical_network(link_length)

    # 生成流量矩阵
    traffic_matrix = generate_traffic_matrix(len(nodes), args.num_requests)

    # 运行物理层仿真
    simulation_results = run_physical_layer_simulation(
        network_elements, fiber_spans, nodes, traffic_matrix, channel_num
    )

    # 准备训练数据
    training_data = prepare_training_data(g, simulation_results, link_length, args.num_samples)

    # 保存数据
    save_data(training_data, simulation_results, args.output_dir)

    print("数据生成完成！")


if __name__ == "__main__":
    main()
