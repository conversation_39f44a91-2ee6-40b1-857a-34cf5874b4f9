#!/usr/bin/env python3
"""
可学习子图GAT - 让模型自己学习相关性评分和子图选择策略
端到端训练，无需人工设计影响计算函数
"""

import time
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import dgl
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_recall_fscore_support
from dgl.nn.pytorch.conv import GATConv
from dgl.nn.pytorch import GraphConv as GCNConv
import dgl.function as fn
import copy
import json
from datetime import datetime
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

class LearnableSubgraphSelector(nn.Module):
    """可学习的子图选择器 - 学习节点相关性评分"""
    def __init__(self, node_feature_dim, hidden_dim):
        super(LearnableSubgraphSelector, self).__init__()
        
        # 节点相关性评分网络
        self.relevance_scorer = nn.Sequential(
            nn.Linear(node_feature_dim * 3, hidden_dim),  # [新光路节点, 目标光路节点, 当前节点]
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(), 
            nn.Linear(hidden_dim // 2, 1),
            nn.Sigmoid()  # 输出0-1的相关性分数
        )
        
        # 距离编码器
        self.distance_encoder = nn.Embedding(20, hidden_dim // 4)  # 最大跳数20
        
    def forward(self, full_graph, node_features, new_lightpath_nodes, target_lightpath_nodes, k=8):
        """
        选择最相关的k个节点构成子图
        
        Args:
            full_graph: 完整图
            node_features: 全图节点特征 [N, feature_dim]
            new_lightpath_nodes: 新光路涉及的节点 [src, dst]
            target_lightpath_nodes: 目标光路涉及的节点 [src, dst]  
            k: 子图节点数量
        
        Returns:
            selected_nodes: 选中的节点索引
            relevance_scores: 相关性分数
            subgraph: 提取的子图
        """
        num_nodes = full_graph.num_nodes()
        device = node_features.device
        
        # 1. 计算所有节点的相关性分数
        new_src_feat = node_features[new_lightpath_nodes[0]]  # 新光路源节点特征
        new_dst_feat = node_features[new_lightpath_nodes[1]]  # 新光路目标节点特征
        target_src_feat = node_features[target_lightpath_nodes[0]]  # 目标光路源节点特征
        target_dst_feat = node_features[target_lightpath_nodes[1]]  # 目标光路目标节点特征
        
        # 关键节点特征的平均
        key_nodes_feat = (new_src_feat + new_dst_feat + target_src_feat + target_dst_feat) / 4
        
        relevance_scores = []
        
        for i in range(num_nodes):
            current_feat = node_features[i]
            
            # 构建输入特征：[关键节点平均特征, 当前节点特征, 相似度特征]
            similarity_feat = torch.abs(current_feat - key_nodes_feat)  # 特征差异
            
            scorer_input = torch.cat([
                key_nodes_feat,    # 关键节点信息
                current_feat,      # 当前节点特征
                similarity_feat    # 相似度信息
            ], dim=0)
            
            score = self.relevance_scorer(scorer_input.unsqueeze(0))
            relevance_scores.append(score)
        
        relevance_scores = torch.cat(relevance_scores, dim=0)  # [N, 1]
        
        # 2. 确保关键节点被选中（强制选择）
        key_nodes = torch.tensor([
            new_lightpath_nodes[0], new_lightpath_nodes[1],
            target_lightpath_nodes[0], target_lightpath_nodes[1]
        ], device=device)
        
        # 给关键节点设置高分数
        relevance_scores[key_nodes] = 1.0
        
        # 3. 选择top-k个节点
        _, top_indices = torch.topk(relevance_scores.squeeze(), k=min(k, num_nodes))
        
        # 4. 提取子图
        selected_nodes = top_indices.cpu().tolist()
        subgraph = dgl.node_subgraph(full_graph, selected_nodes)
        
        return selected_nodes, relevance_scores, subgraph

class AttentiveSubgraphGAT(nn.Module):
    """注意力子图GAT - 结合可学习子图选择"""
    def __init__(self, node_feature_dim, hidden_dim, num_heads, num_layers, num_classes, 
                 subgraph_size=8, dropout=0.1):
        super(AttentiveSubgraphGAT, self).__init__()
        
        self.subgraph_size = subgraph_size
        
        # 可学习子图选择器
        self.subgraph_selector = LearnableSubgraphSelector(node_feature_dim, hidden_dim)
        
        # GAT层
        self.gat_layers = nn.ModuleList()
        
        # 第一层
        self.gat_layers.append(GATConv(
            node_feature_dim, hidden_dim, num_heads,
            feat_drop=dropout, attn_drop=dropout, allow_zero_in_degree=True
        ))
        
        # 中间层
        for _ in range(num_layers - 1):
            self.gat_layers.append(GATConv(
                hidden_dim * num_heads, hidden_dim, num_heads,
                feat_drop=dropout, attn_drop=dropout, allow_zero_in_degree=True
            ))
        
        # 全局注意力池化
        self.global_attention = nn.Sequential(
            nn.Linear(hidden_dim * num_heads, hidden_dim),
            nn.Tanh(),
            nn.Linear(hidden_dim, 1)
        )
        
        # 分类器
        self.classifier = nn.Sequential(
            nn.Linear(hidden_dim * num_heads, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, num_classes)
        )
        
    def forward(self, full_graph, full_node_features, new_lightpath_nodes, target_lightpath_nodes):
        """
        前向传播
        
        Args:
            full_graph: 完整图
            full_node_features: 全图节点特征
            new_lightpath_nodes: 新光路节点 [src, dst]
            target_lightpath_nodes: 目标光路节点 [src, dst]
        """
        # 1. 学习子图选择
        selected_nodes, relevance_scores, subgraph = self.subgraph_selector(
            full_graph, full_node_features, new_lightpath_nodes, target_lightpath_nodes, 
            k=self.subgraph_size
        )
        
        # 2. 获取子图节点特征
        subgraph_features = full_node_features[selected_nodes]
        
        # 3. GAT处理
        h = subgraph_features
        
        for gat_layer in self.gat_layers:
            h = gat_layer(subgraph, h).flatten(1)
            h = F.relu(h)
        
        # 4. 注意力池化
        attention_weights = self.global_attention(h)  # [subgraph_nodes, 1]
        attention_weights = F.softmax(attention_weights, dim=0)
        
        # 加权聚合
        graph_repr = torch.sum(h * attention_weights, dim=0)  # [hidden_dim * num_heads]
        
        # 5. 分类
        logits = self.classifier(graph_repr.unsqueeze(0))
        
        return logits, {
            'selected_nodes': selected_nodes,
            'relevance_scores': relevance_scores,
            'attention_weights': attention_weights,
            'subgraph_size': len(selected_nodes)
        }

class AdaptiveLightpathDataGenerator:
    """自适应光路数据生成器 - 为端到端学习设计"""
    
    def __init__(self):
        self.create_japan_network()
        
    def create_japan_network(self):
        """创建真实的日本网络拓扑"""
        u = torch.tensor([0,0,1,2,3,3,4,4,4,5,6,6,7,8,11,9,8,13,11,11,13,10])
        v = torch.tensor([1,2,3,4,4,5,5,8,6,7,7,11,9,11,9,10,13,11,10,12,12,12])
        
        self.link_length = np.array([
            [0,160,240,0,0,0,0,0,0,0,0,0,0,0],
            [160,0,0,240,0,0,0,0,0,0,0,0,0,0],
            [240,0,0,0,240,0,0,0,0,0,0,0,0,0],
            [0,240,0,0,80,40,0,0,0,0,0,0,0,0],
            [0,0,240,80,0,40,80,0,240,0,0,0,0,0],
            [0,0,0,40,40,0,0,160,0,0,0,0,0,0],
            [0,0,0,0,80,0,0,80,0,0,0,240,0,0],
            [0,0,0,0,0,160,80,0,0,160,0,0,0,0],
            [0,0,0,0,240,0,0,0,0,0,0,240,0,240],
            [0,0,0,0,0,0,0,160,0,0,40,40,0,0],
            [0,0,0,0,0,0,0,0,0,40,0,40,320,0],
            [0,0,0,0,0,0,240,0,240,40,40,0,320,240],
            [0,0,0,0,0,0,0,0,0,0,320,320,0,160],
            [0,0,0,0,0,0,0,0,240,0,0,240,160,0]
        ])
        
        self.full_graph = dgl.graph((u, v))
        self.full_graph = dgl.to_bidirected(self.full_graph)
        
        print(f"🌐 创建日本网络: {self.full_graph.num_nodes()}节点, {self.full_graph.num_edges()}边")
        
    def generate_learning_scenarios(self, num_scenarios: int = 2000):
        """生成用于端到端学习的场景数据"""
        print(f"🔄 生成 {num_scenarios} 个端到端学习场景...")
        
        scenarios = []
        
        for i in range(num_scenarios):
            if i % 400 == 0:
                print(f"   进度: {i}/{num_scenarios}")
            
            # 随机选择光路
            nodes = list(range(self.full_graph.num_nodes()))
            
            new_src = np.random.choice(nodes)
            new_dst = np.random.choice([n for n in nodes if n != new_src])
            target_src = np.random.choice(nodes) 
            target_dst = np.random.choice([n for n in nodes if n != target_src])
            
            # 生成丰富的节点特征
            node_features = self._generate_rich_node_features()
            
            # 使用更复杂的ground truth生成（基于真实的网络物理特性）
            is_affected = self._generate_ground_truth_label(
                new_src, new_dst, target_src, target_dst, node_features
            )
            
            scenario = {
                'full_graph': self.full_graph,
                'node_features': node_features,
                'new_lightpath_nodes': [new_src, new_dst],
                'target_lightpath_nodes': [target_src, target_dst],
                'label': is_affected
            }
            
            scenarios.append(scenario)
        
        print(f"✅ 场景生成完成! 总数: {len(scenarios)}")
        
        # 统计标签分布
        affected_count = sum(1 for s in scenarios if s['label'] == 1)
        print(f"   受影响: {affected_count} ({affected_count/len(scenarios)*100:.1f}%)")
        print(f"   未受影响: {len(scenarios)-affected_count} ({(len(scenarios)-affected_count)/len(scenarios)*100:.1f}%)")
        
        return scenarios
    
    def _generate_rich_node_features(self):
        """生成丰富的节点特征 - 让模型有足够信息学习"""
        num_nodes = self.full_graph.num_nodes()
        features = []
        
        for i in range(num_nodes):
            # 8维特征：[功率, 负载, 度数, 中介性, 波长利用率, 地理位置x, 地理位置y, 容量]
            power = np.random.uniform(-3, 3)  # 发射功率
            load = np.random.uniform(0, 1)    # 节点负载
            degree = self.full_graph.in_degrees()[i].item() / max(self.full_graph.in_degrees())
            betweenness = np.random.uniform(0, 1)  # 中介中心性
            wavelength_util = np.random.uniform(0, 0.8)  # 波长利用率
            geo_x = np.random.uniform(0, 1)  # 地理坐标x (归一化)
            geo_y = np.random.uniform(0, 1)  # 地理坐标y (归一化)
            capacity = np.random.uniform(0.5, 1.0)  # 节点容量
            
            features.append([power, load, degree, betweenness, wavelength_util, geo_x, geo_y, capacity])
        
        return torch.tensor(features, dtype=torch.float32)
    
    def _generate_ground_truth_label(self, new_src, new_dst, target_src, target_dst, node_features):
        """生成ground truth标签 - 基于复杂的物理关系"""
        
        # 1. 计算最短路径
        try:
            import networkx as nx
            nx_graph = dgl.to_networkx(self.full_graph).to_undirected()
            
            # 添加权重
            for edge in nx_graph.edges():
                i, j = edge
                nx_graph[i][j]['weight'] = self.link_length[i][j] if self.link_length[i][j] > 0 else 1000
            
            new_path = nx.shortest_path(nx_graph, new_src, new_dst, weight='weight')
            target_path = nx.shortest_path(nx_graph, target_src, target_dst, weight='weight')
        except:
            new_path = [new_src, new_dst]
            target_path = [target_src, target_dst]
        
        # 2. 复杂的影响计算（这是模型需要学习的ground truth）
        
        # 路径重叠
        new_edges = set((min(new_path[i], new_path[i+1]), max(new_path[i], new_path[i+1])) 
                       for i in range(len(new_path)-1))
        target_edges = set((min(target_path[i], target_path[i+1]), max(target_path[i], target_path[i+1])) 
                          for i in range(len(target_path)-1))
        path_overlap = len(new_edges.intersection(target_edges)) / max(len(new_edges.union(target_edges)), 1)
        
        # 节点特征相似性
        new_src_feat = node_features[new_src]
        new_dst_feat = node_features[new_dst]
        target_src_feat = node_features[target_src]
        target_dst_feat = node_features[target_dst]
        
        # 功率相似性
        power_similarity = 1 - abs(new_src_feat[0] - target_src_feat[0]) / 6.0
        
        # 负载相关性
        load_impact = (new_src_feat[1] + target_src_feat[1]) / 2  # 负载越高影响越大
        
        # 地理距离
        geo_distance = torch.sqrt((new_src_feat[5] - target_src_feat[5])**2 + 
                                 (new_src_feat[6] - target_src_feat[6])**2)
        geo_impact = 1 / (1 + geo_distance)  # 距离越近影响越大
        
        # 容量冲突
        capacity_conflict = max(0, (new_src_feat[7] + target_src_feat[7]) - 1.0)  # 容量超载
        
        # 综合影响评分
        impact_score = (
            path_overlap * 0.3 +           # 路径重叠
            power_similarity * 0.2 +       # 功率相似性
            load_impact * 0.2 +            # 负载影响
            geo_impact * 0.2 +             # 地理影响
            capacity_conflict * 0.1        # 容量冲突
        )
        
        # 使用阈值判断 + 随机噪声（模拟现实的不确定性）
        noise = np.random.normal(0, 0.1)
        final_score = impact_score + noise
        
        return 1 if final_score > 0.4 else 0

def train_learnable_subgraph_gat():
    """训练可学习子图GAT"""
    print("🧠 可学习子图GAT实验 - 端到端学习相关性评分")
    print("=" * 60)
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🔧 使用设备: {device}")
    
    # 生成数据
    data_generator = AdaptiveLightpathDataGenerator()
    scenarios = data_generator.generate_learning_scenarios(num_scenarios=3000)
    
    # 数据划分
    train_scenarios, test_scenarios = train_test_split(scenarios, test_size=0.25, random_state=42,
                                                     stratify=[s['label'] for s in scenarios])
    
    print(f"📊 数据划分: 训练{len(train_scenarios)}, 测试{len(test_scenarios)}")
    
    # 创建模型
    model = AttentiveSubgraphGAT(
        node_feature_dim=8,  # 丰富的8维节点特征
        hidden_dim=64,
        num_heads=4,
        num_layers=2,
        num_classes=2,
        subgraph_size=10,  # 子图大小
        dropout=0.1
    ).to(device)
    
    print(f"🧠 模型参数: {sum(p.numel() for p in model.parameters())}")
    
    # 训练设置
    optimizer = torch.optim.Adam(model.parameters(), lr=0.002, weight_decay=1e-4)
    criterion = nn.CrossEntropyLoss()
    
    # 训练循环
    epochs = 80
    best_accuracy = 0
    train_losses = []
    train_accuracies = []
    
    print(f"\n🚀 开始端到端训练...")
    
    for epoch in range(epochs):
        model.train()
        total_loss = 0
        correct_predictions = 0
        
        start_time = time.time()
        
        # 训练批次
        for scenario in train_scenarios:
            full_graph = scenario['full_graph'].to(device)
            node_features = scenario['node_features'].to(device)
            new_lightpath_nodes = scenario['new_lightpath_nodes']
            target_lightpath_nodes = scenario['target_lightpath_nodes']
            label = torch.tensor([scenario['label']], dtype=torch.long).to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            logits, debug_info = model(full_graph, node_features, new_lightpath_nodes, target_lightpath_nodes)
            loss = criterion(logits, label)
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
            
            # 统计准确率
            pred = torch.argmax(logits, dim=1)
            correct_predictions += (pred == label).sum().item()
        
        # 计算平均损失和准确率
        avg_loss = total_loss / len(train_scenarios)
        train_accuracy = correct_predictions / len(train_scenarios)
        
        train_losses.append(avg_loss)
        train_accuracies.append(train_accuracy)
        
        epoch_time = time.time() - start_time
        
        # 测试评估
        if epoch % 10 == 0:
            test_accuracy = evaluate_model(model, test_scenarios, device)
            
            print(f"Epoch {epoch:3d}: Train Loss={avg_loss:.4f}, Train Acc={train_accuracy:.4f}, "
                  f"Test Acc={test_accuracy:.4f}, Time={epoch_time:.2f}s")
            
            if test_accuracy > best_accuracy:
                best_accuracy = test_accuracy
                torch.save(model.state_dict(), 'best_learnable_subgraph_gat.pth')
    
    # 最终测试
    print(f"\n🧪 最终测试评估...")
    model.load_state_dict(torch.load('best_learnable_subgraph_gat.pth'))
    
    final_accuracy, detailed_results = evaluate_model_detailed(model, test_scenarios, device)
    
    print(f"✅ 最终测试准确率: {final_accuracy:.4f}")
    print(f"   精确率: {detailed_results['precision']:.4f}")
    print(f"   召回率: {detailed_results['recall']:.4f}")
    print(f"   F1分数: {detailed_results['f1_score']:.4f}")
    
    # 分析学习到的子图选择策略
    analyze_learned_subgraph_strategy(model, test_scenarios[:10], device)
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results = {
        'experiment_info': {
            'timestamp': timestamp,
            'model_type': 'LearnableSubgraphGAT',
            'task': 'End-to-End Lightpath Impact Learning',
            'train_samples': len(train_scenarios),
            'test_samples': len(test_scenarios),
            'model_parameters': sum(p.numel() for p in model.parameters())
        },
        'results': {
            'final_test_accuracy': final_accuracy,
            'best_accuracy': best_accuracy,
            'precision': detailed_results['precision'],
            'recall': detailed_results['recall'],
            'f1_score': detailed_results['f1_score']
        },
        'training_history': {
            'train_losses': train_losses,
            'train_accuracies': train_accuracies
        }
    }
    
    results_file = f'learnable_subgraph_gat_results_{timestamp}.json'
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📁 结果保存至: {results_file}")
    
    return results

def evaluate_model(model, test_scenarios, device):
    """简单模型评估"""
    model.eval()
    correct = 0
    
    with torch.no_grad():
        for scenario in test_scenarios:
            full_graph = scenario['full_graph'].to(device)
            node_features = scenario['node_features'].to(device)
            new_lightpath_nodes = scenario['new_lightpath_nodes']
            target_lightpath_nodes = scenario['target_lightpath_nodes']
            label = scenario['label']
            
            logits, _ = model(full_graph, node_features, new_lightpath_nodes, target_lightpath_nodes)
            pred = torch.argmax(logits, dim=1).cpu().item()
            
            if pred == label:
                correct += 1
    
    return correct / len(test_scenarios)

def evaluate_model_detailed(model, test_scenarios, device):
    """详细模型评估"""
    model.eval()
    predictions = []
    true_labels = []
    
    with torch.no_grad():
        for scenario in test_scenarios:
            full_graph = scenario['full_graph'].to(device)
            node_features = scenario['node_features'].to(device)
            new_lightpath_nodes = scenario['new_lightpath_nodes']
            target_lightpath_nodes = scenario['target_lightpath_nodes']
            label = scenario['label']
            
            logits, _ = model(full_graph, node_features, new_lightpath_nodes, target_lightpath_nodes)
            pred = torch.argmax(logits, dim=1).cpu().item()
            
            predictions.append(pred)
            true_labels.append(label)
    
    accuracy = accuracy_score(true_labels, predictions)
    precision, recall, f1, _ = precision_recall_fscore_support(true_labels, predictions, average='weighted')
    
    return accuracy, {
        'precision': precision,
        'recall': recall, 
        'f1_score': f1,
        'predictions': predictions,
        'true_labels': true_labels
    }

def analyze_learned_subgraph_strategy(model, sample_scenarios, device):
    """分析模型学习到的子图选择策略"""
    print(f"\n🔍 分析学习到的子图选择策略:")
    print("-" * 50)
    
    model.eval()
    
    with torch.no_grad():
        for i, scenario in enumerate(sample_scenarios):
            full_graph = scenario['full_graph'].to(device)
            node_features = scenario['node_features'].to(device)
            new_lightpath_nodes = scenario['new_lightpath_nodes']
            target_lightpath_nodes = scenario['target_lightpath_nodes']
            true_label = scenario['label']
            
            logits, debug_info = model(full_graph, node_features, new_lightpath_nodes, target_lightpath_nodes)
            pred_label = torch.argmax(logits, dim=1).cpu().item()
            
            selected_nodes = debug_info['selected_nodes']
            relevance_scores = debug_info['relevance_scores']
            attention_weights = debug_info['attention_weights']
            
            print(f"场景 {i+1}: 真实标签={true_label}, 预测标签={pred_label}")
            print(f"  新光路: {new_lightpath_nodes[0]}→{new_lightpath_nodes[1]}")
            print(f"  目标光路: {target_lightpath_nodes[0]}→{target_lightpath_nodes[1]}")
            print(f"  选中子图节点: {selected_nodes}")
            print(f"  平均相关性分数: {relevance_scores.mean().item():.3f}")
            print(f"  最大注意力权重: {attention_weights.max().item():.3f}")
            print()

if __name__ == "__main__":
    results = train_learnable_subgraph_gat() 