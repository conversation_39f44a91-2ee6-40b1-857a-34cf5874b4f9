#!/usr/bin/env python3
"""
增强版可学习子图GAT实验 - 优化效果
包含：
1. 更复杂的物理层建模
2. 更丰富的节点和边特征
3. 分阶段训练策略
4. 对比学习和注意力机制优化
5. 多任务学习
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import dgl
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score
from tqdm import tqdm
import json
import time
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置随机种子
torch.manual_seed(42)
np.random.seed(42)

class EnhancedPhysicsCalculator:
    """增强的物理层计算器 - 更复杂的OSNR和非线性效应建模"""
    
    def __init__(self):
        self.fiber_loss_db_per_km = 0.2
        self.edfa_gain_db = 20
        self.edfa_nf_db = 5
        self.span_length_km = 80
        self.wavelength_channels = 80
        self.channel_spacing_ghz = 50
        self.reference_power_dbm = 0
        
    def calculate_advanced_osnr(self, path_length_km, num_spans, existing_channels, 
                              wavelength_conflicts, power_imbalance, nonlinear_penalty):
        """计算高级OSNR，考虑多种物理效应"""
        
        # ASE噪声计算 (每个EDFA)
        span_loss_db = path_length_km / num_spans * self.fiber_loss_db_per_km
        ase_power_linear = 10**(((self.edfa_nf_db + span_loss_db) / 10) - 3)  # mW
        total_ase_linear = ase_power_linear * num_spans
        
        # 非线性噪声 (Kerr效应, XPM, FWM)
        gamma = 1.3e-3  # 非线性系数 W^-1/km
        effective_area = 80e-12  # m^2
        beta2 = -2.168e-26  # s^2/m
        
        # FWM效率计算
        fwm_efficiency = self._calculate_fwm_efficiency(existing_channels, beta2)
        
        # XPM噪声
        xpm_noise = self._calculate_xpm_noise(existing_channels, power_imbalance, gamma, path_length_km)
        
        # SPM噪声
        spm_noise = self._calculate_spm_noise(power_imbalance, gamma, path_length_km)
        
        # 总非线性噪声
        total_nonlinear_noise = fwm_efficiency + xpm_noise + spm_noise + nonlinear_penalty
        
        # 信号功率
        signal_power_linear = 10**(self.reference_power_dbm / 10)  # mW
        
        # 波长相关损耗
        wavelength_dependent_loss = wavelength_conflicts * 0.1  # dB
        signal_power_linear *= 10**(-wavelength_dependent_loss / 10)
        
        # 总噪声功率
        total_noise_linear = total_ase_linear + total_nonlinear_noise
        
        # OSNR计算
        osnr_linear = max(signal_power_linear / total_noise_linear, 1e-10)
        osnr_db = 10 * np.log10(osnr_linear)
        
        # 限制OSNR范围到合理值
        osnr_db = max(8, min(40, osnr_db))
        
        return osnr_db
    
    def _calculate_fwm_efficiency(self, num_channels, beta2):
        """计算四波混频效率"""
        if num_channels < 3:
            return 0
        
        # 简化的FWM效率计算
        delta_f = self.channel_spacing_ghz * 1e9  # Hz
        fwm_factor = (2 * np.pi * delta_f)**2 * abs(beta2)
        efficiency = num_channels * (num_channels - 1) * (num_channels - 2) / 6 * fwm_factor * 1e-6
        return min(efficiency, 0.1)  # 限制最大FWM噪声
    
    def _calculate_xpm_noise(self, num_channels, power_imbalance, gamma, length_km):
        """计算交叉相位调制噪声"""
        if num_channels <= 1:
            return 0
        
        xpm_coefficient = 2 * gamma * length_km * 1000  # 转换为米
        xpm_noise = xpm_coefficient * (num_channels - 1) * power_imbalance**2
        return min(xpm_noise * 1e-6, 0.05)  # mW
    
    def _calculate_spm_noise(self, power_imbalance, gamma, length_km):
        """计算自相位调制噪声"""
        spm_coefficient = gamma * length_km * 1000  # 转换为米
        spm_noise = spm_coefficient * power_imbalance**2
        return min(spm_noise * 1e-6, 0.02)  # mW

class EnhancedNetworkTopology:
    """增强的网络拓扑 - 基于真实日本网络"""
    
    def __init__(self):
        self.num_nodes = 14
        self.node_positions = {
            0: (139.6917, 35.6895),   # Tokyo
            1: (135.5023, 34.6937),   # Osaka  
            2: (136.9066, 35.1815),   # Nagoya
            3: (130.4017, 33.5904),   # Fukuoka
            4: (131.4202, 31.9077),   # Kagoshima
            5: (140.1230, 36.3909),   # Utsunomiya
            6: (141.3625, 43.0642),   # Sapporo
            7: (140.7400, 40.8244),   # Morioka
            8: (132.4553, 34.3853),   # Hiroshima
            9: (133.9250, 34.6851),   # Takamatsu
            10: (135.8681, 35.2661),  # Kyoto
            11: (136.6256, 36.5951),  # Kanazawa
            12: (138.3831, 34.9756),  # Shizuoka
            13: (139.0238, 36.0014)   # Maebashi
        }
        
        # 真实光纤链路 (基于日本光网络拓扑)
        self.edges = [
            (0, 1), (0, 2), (0, 5), (0, 6), (0, 7), (0, 12), (0, 13),
            (1, 2), (1, 3), (1, 8), (1, 9), (1, 10),
            (2, 10), (2, 11), (2, 12), (2, 13),
            (3, 4), (3, 8),
            (5, 7), (5, 13),
            (6, 7),
            (7, 11),
            (8, 9),
            (9, 10),
            (10, 11),
            (11, 13),
            (12, 13)
        ]
        
    def create_graph(self):
        """创建DGL图"""
        g = dgl.graph(self.edges, num_nodes=self.num_nodes)
        g = dgl.to_bidirected(g)
        return g
    
    def calculate_distance(self, node1, node2):
        """计算两节点间的地理距离"""
        lat1, lon1 = self.node_positions[node1]
        lat2, lon2 = self.node_positions[node2]
        
        # 简化的距离计算 (公里)
        lat_diff = lat1 - lat2
        lon_diff = lon1 - lon2
        distance = np.sqrt(lat_diff**2 + lon_diff**2) * 111  # 1度约111公里
        return distance

class EnhancedFeatureGenerator:
    """增强的特征生成器 - 更丰富的节点和边特征"""
    
    def __init__(self, topology, physics_calc):
        self.topology = topology
        self.physics_calc = physics_calc
        
    def generate_rich_node_features(self, graph, scenario_id):
        """生成15维丰富节点特征"""
        num_nodes = graph.num_nodes()
        features = np.zeros((num_nodes, 15))
        
        # 基于场景ID生成一致但多样的特征
        np.random.seed(scenario_id)
        
        for i in range(num_nodes):
            # 基于节点位置和度数的特征生成
            degree = graph.in_degrees(i)
            lat, lon = self.topology.node_positions[i]
            
            # 基础特征 (8维) - 更现实的分布
            features[i, 0] = np.random.lognormal(0, 0.3) + 0.5  # 节点功率 (W) - 对数正态分布
            features[i, 1] = np.random.beta(2, 3)  # 负载利用率 - Beta分布偏向中等负载
            features[i, 2] = degree / 10.0   # 归一化节点度
            features[i, 3] = self._calculate_betweenness(graph, i)  # 介数中心性
            features[i, 4] = np.random.beta(3, 2)  # 波长利用率 - Beta分布
            features[i, 5] = (lon - 130.0) / 11.0  # 归一化经度 (130-141度)
            features[i, 6] = (lat - 31.0) / 13.0   # 归一化纬度 (31-44度)
            features[i, 7] = np.random.gamma(2, 25) + 20   # 节点容量 (Gbps) - Gamma分布
            
            # 增强特征 (7维) - 更符合网络特征的分布
            features[i, 8] = max(1, int(degree * np.random.uniform(1.5, 3.0)))  # 与度数相关的光路数量
            features[i, 9] = np.random.normal(15, 8)  # 平均OSNR (dB) - 正态分布
            features[i, 10] = np.random.exponential(0.2)  # 噪声水平 - 指数分布
            features[i, 11] = np.random.gamma(2, 0.3)  # 功率谱密度
            features[i, 12] = max(0, int(80 - features[i, 4] * 80 + np.random.normal(0, 10)))  # 可用波长与利用率相关
            features[i, 13] = min(1.0, features[i, 1] + np.random.normal(0, 0.1))  # 拥塞程度与负载相关
            features[i, 14] = np.random.uniform(0.3, 1.0)  # 服务质量权重
        
        # 恢复随机种子
        np.random.seed(None)
            
        return features
    
    def generate_rich_edge_features(self, graph):
        """生成10维丰富边特征"""
        edges = graph.edges()
        num_edges = len(edges[0])
        features = np.zeros((num_edges, 10))
        
        for i in range(num_edges):
            src, dst = edges[0][i].item(), edges[1][i].item()
            distance = self.topology.calculate_distance(src, dst)
            
            # 基础特征 (6维)
            features[i, 0] = distance / 1000.0  # 归一化距离
            features[i, 1] = distance * self.physics_calc.fiber_loss_db_per_km  # 光纤损耗
            features[i, 2] = np.random.uniform(0.3, 0.8)  # 利用率
            features[i, 3] = np.random.randint(2, 15)     # 光路数量
            features[i, 4] = np.random.randint(0, 5)      # 波长冲突数
            features[i, 5] = np.random.uniform(0.5, 3.0)  # 边上总功率
            
            # 增强特征 (4维)
            features[i, 6] = np.random.uniform(0.1, 0.5)  # 非线性系数
            features[i, 7] = np.random.uniform(0.2, 0.7)  # 色散补偿
            features[i, 8] = np.random.randint(2, 8)      # EDFA数量
            features[i, 9] = np.random.uniform(0.1, 0.3)  # 链路质量因子
            
        return features
    
    def _calculate_betweenness(self, graph, node):
        """简化的介数中心性计算"""
        # 使用节点度作为简化的中心性度量
        degree = graph.in_degrees(node)
        max_degree = max(graph.in_degrees())
        return degree / max_degree if max_degree > 0 else 0

class EnhancedSubgraphSelector(nn.Module):
    """增强的子图选择器 - 多头注意力机制"""
    
    def __init__(self, node_feature_dim=15, hidden_dim=128, num_heads=4):
        super().__init__()
        self.node_feature_dim = node_feature_dim
        self.hidden_dim = hidden_dim
        self.num_heads = num_heads
        
        # 多头注意力相关性评分器
        self.relevance_scorer = nn.ModuleList([
            nn.Sequential(
                nn.Linear(node_feature_dim * 3, hidden_dim),  # 新光路、目标光路、当前节点特征
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(hidden_dim, hidden_dim // 2),
                nn.ReLU(),
                nn.Linear(hidden_dim // 2, 1),
                nn.Sigmoid()
            ) for _ in range(num_heads)
        ])
        
        # 注意力权重融合
        self.attention_fusion = nn.Linear(num_heads, 1)
        
        # 子图大小预测器
        self.size_predictor = nn.Sequential(
            nn.Linear(node_feature_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1),
            nn.Sigmoid()
        )
        
    def forward(self, node_features, new_lightpath_nodes, target_lightpath_nodes):
        batch_size, num_nodes, feature_dim = node_features.shape
        
        # 获取关键节点特征
        new_src_feat = node_features[torch.arange(batch_size), new_lightpath_nodes[:, 0]]
        new_dst_feat = node_features[torch.arange(batch_size), new_lightpath_nodes[:, 1]]
        target_src_feat = node_features[torch.arange(batch_size), target_lightpath_nodes[:, 0]]
        target_dst_feat = node_features[torch.arange(batch_size), target_lightpath_nodes[:, 1]]
        
        # 预测最优子图大小
        key_features = torch.cat([new_src_feat + new_dst_feat, target_src_feat + target_dst_feat], dim=1)
        optimal_ratio = self.size_predictor(key_features)
        optimal_k = torch.clamp((optimal_ratio * num_nodes).int(), min=6, max=num_nodes-2)
        
        # 多头相关性评分
        all_head_scores = []
        for head in self.relevance_scorer:
            head_scores = []
            for i in range(num_nodes):
                current_node_feat = node_features[:, i, :]
                combined_feat = torch.cat([
                    new_src_feat + new_dst_feat,  # 新光路特征
                    target_src_feat + target_dst_feat,  # 目标光路特征
                    current_node_feat  # 当前节点特征
                ], dim=1)
                score = head(combined_feat)
                head_scores.append(score)
            head_scores = torch.stack(head_scores, dim=1)  # [batch, num_nodes, 1]
            all_head_scores.append(head_scores)
        
        # 融合多头注意力
        all_head_scores = torch.stack(all_head_scores, dim=-1)  # [batch, num_nodes, 1, num_heads]
        attention_weights = F.softmax(all_head_scores, dim=-1)
        fused_scores = torch.sum(all_head_scores * attention_weights, dim=-1)  # [batch, num_nodes, 1]
        fused_scores = fused_scores.squeeze(-1)  # [batch, num_nodes]
        
        # 选择Top-K节点
        subgraph_nodes = []
        relevance_scores = []
        
        for i in range(batch_size):
            k = optimal_k[i].item()
            scores = fused_scores[i]
            _, top_indices = torch.topk(scores, k)
            subgraph_nodes.append(top_indices)
            relevance_scores.append(scores[top_indices].mean())
        
        return subgraph_nodes, torch.stack(relevance_scores)

class EnhancedSubgraphGAT(nn.Module):
    """增强的子图GAT - 多层GAT + 残差连接"""
    
    def __init__(self, node_feature_dim=15, edge_feature_dim=10, hidden_dim=128, 
                 num_layers=3, num_heads=4, num_classes=2):
        super().__init__()
        
        self.subgraph_selector = EnhancedSubgraphSelector(node_feature_dim, hidden_dim, num_heads)
        
        # 多层GAT with 残差连接
        self.gat_layers = nn.ModuleList()
        self.layer_norms = nn.ModuleList()
        
        # 输入层
        self.gat_layers.append(
            dgl.nn.GATConv(node_feature_dim, hidden_dim // num_heads, num_heads, 
                          feat_drop=0.2, attn_drop=0.2, activation=F.elu, allow_zero_in_degree=True)
        )
        self.layer_norms.append(nn.LayerNorm(hidden_dim))
        
        # 隐藏层
        for _ in range(num_layers - 2):
            self.gat_layers.append(
                dgl.nn.GATConv(hidden_dim, hidden_dim // num_heads, num_heads,
                              feat_drop=0.2, attn_drop=0.2, activation=F.elu, allow_zero_in_degree=True)
            )
            self.layer_norms.append(nn.LayerNorm(hidden_dim))
        
        # 输出层
        self.gat_layers.append(
            dgl.nn.GATConv(hidden_dim, hidden_dim // num_heads, num_heads,
                          feat_drop=0.1, attn_drop=0.1, activation=None, allow_zero_in_degree=True)
        )
        self.layer_norms.append(nn.LayerNorm(hidden_dim))
        
        # 边特征处理
        self.edge_encoder = nn.Sequential(
            nn.Linear(edge_feature_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim)
        )
        
        # 全局注意力池化
        self.global_attention = nn.MultiheadAttention(hidden_dim, num_heads, dropout=0.1)
        
        # 多任务分类器
        self.impact_classifier = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, num_classes)
        )
        
        # 回归器 (预测OSNR变化)
        self.osnr_regressor = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim, 1)
        )
        
    def forward(self, graph, node_features, edge_features, new_lightpath_nodes, target_lightpath_nodes):
        batch_size = node_features.shape[0]
        
        # 选择子图
        subgraph_nodes_list, relevance_scores = self.subgraph_selector(
            node_features, new_lightpath_nodes, target_lightpath_nodes
        )
        
        # 批量处理子图
        subgraph_outputs = []
        attention_weights_list = []
        
        for i in range(batch_size):
            subgraph_nodes = subgraph_nodes_list[i]
            
            # 提取子图并添加自环
            subgraph = dgl.node_subgraph(graph, subgraph_nodes)
            subgraph = dgl.add_self_loop(subgraph)
            sub_node_features = node_features[i][subgraph_nodes]
            
            # 处理边特征
            sub_edge_features = self.edge_encoder(edge_features[i][:subgraph.num_edges()])
            
            # 多层GAT处理
            h = sub_node_features
            for j, (gat_layer, layer_norm) in enumerate(zip(self.gat_layers, self.layer_norms)):
                h_new = gat_layer(subgraph, h).flatten(1)
                h_new = layer_norm(h_new)
                
                # 残差连接 (除了第一层)
                if j > 0 and h.shape == h_new.shape:
                    h = h + h_new
                else:
                    h = h_new
                    
                h = F.dropout(h, training=self.training)
            
            # 全局注意力池化
            h_pooled, attention_weights = self.global_attention(
                h.unsqueeze(0), h.unsqueeze(0), h.unsqueeze(0)
            )
            h_pooled = h_pooled.squeeze(0)
            
            # 聚合表示
            graph_repr = torch.cat([
                torch.mean(h_pooled, dim=0),  # 平均池化
                torch.max(h_pooled, dim=0)[0]  # 最大池化
            ])
            
            subgraph_outputs.append(graph_repr)
            attention_weights_list.append(attention_weights.max().item())
        
        # 批量输出
        batch_repr = torch.stack(subgraph_outputs)
        
        # 多任务预测
        impact_logits = self.impact_classifier(batch_repr)
        osnr_pred = self.osnr_regressor(batch_repr)
        
        return {
            'impact_logits': impact_logits,
            'osnr_pred': osnr_pred,
            'subgraph_nodes': subgraph_nodes_list,
            'relevance_scores': relevance_scores,
            'attention_weights': attention_weights_list
        }

class EnhancedDataGenerator:
    """增强的数据生成器 - 更复杂的物理层建模"""
    
    def __init__(self, topology, physics_calc, feature_gen):
        self.topology = topology
        self.physics_calc = physics_calc
        self.feature_gen = feature_gen
        self.graph = topology.create_graph()
        
    def generate_enhanced_scenarios(self, num_scenarios=5000):
        """生成增强的训练场景"""
        scenarios = []
        labels = []
        osnr_changes = []
        
        print(f"🔄 生成 {num_scenarios} 个增强场景...")
        
        for i in tqdm(range(num_scenarios)):
            # 生成特征
            node_features = self.feature_gen.generate_rich_node_features(self.graph, i)
            edge_features = self.feature_gen.generate_rich_edge_features(self.graph)
            
            # 随机选择光路
            num_nodes = self.topology.num_nodes
            new_src, new_dst = np.random.choice(num_nodes, 2, replace=False)
            target_src, target_dst = np.random.choice(num_nodes, 2, replace=False)
            
            # 复杂的物理层影响计算
            impact_result = self._calculate_complex_impact(
                (new_src, new_dst), (target_src, target_dst), 
                node_features, edge_features
            )
            
            scenario = {
                'node_features': node_features.astype(np.float32),
                'edge_features': edge_features.astype(np.float32),
                'new_lightpath': [new_src, new_dst],
                'target_lightpath': [target_src, target_dst]
            }
            
            scenarios.append(scenario)
            labels.append(impact_result['is_affected'])
            osnr_changes.append(impact_result['osnr_change'])
            
            if (i + 1) % 1000 == 0:
                affected_count = sum(labels)
                affected_ratio = affected_count / (i + 1)
                print(f"   进度: {i+1}/{num_scenarios}, 受影响: {affected_count} ({affected_ratio*100:.1f}%)")
                
                # 数据质量检查
                if i > 2000 and (affected_ratio < 0.2 or affected_ratio > 0.8):
                    print(f"   ⚠️  数据不平衡警告: 受影响比例 {affected_ratio:.1%}")
                    if affected_ratio < 0.1 or affected_ratio > 0.95:
                        print(f"   🛑 数据质量过差，建议调整参数!")
                        break
        
        affected_count = sum(labels)
        print(f"✅ 场景生成完成! 总数: {len(scenarios)}")
        print(f"   受影响: {affected_count} ({affected_count/len(scenarios)*100:.1f}%)")
        print(f"   未受影响: {len(scenarios)-affected_count} ({(len(scenarios)-affected_count)/len(scenarios)*100:.1f}%)")
        
        return scenarios, labels, osnr_changes
    
    def _calculate_complex_impact(self, new_lightpath, target_lightpath, node_features, edge_features):
        """计算复杂的物理层影响"""
        new_src, new_dst = new_lightpath
        target_src, target_dst = target_lightpath
        
        # 路径重叠分析
        path_overlap = self._analyze_path_overlap(new_lightpath, target_lightpath)
        
        # 地理距离影响
        geo_distance = self._calculate_geographic_impact(new_lightpath, target_lightpath)
        
        # 功率和负载影响
        power_impact = self._calculate_power_impact(new_lightpath, target_lightpath, node_features)
        
        # 波长相关影响
        wavelength_impact = self._calculate_wavelength_impact(new_lightpath, target_lightpath, node_features)
        
        # 网络拥塞影响
        congestion_impact = self._calculate_congestion_impact(new_lightpath, target_lightpath, node_features)
        
        # 非线性效应
        nonlinear_impact = self._calculate_nonlinear_effects(new_lightpath, target_lightpath, node_features)
        
        # 重新设计影响计算 - 目标是40-60%受影响比例
        
        # 1. 严格的路径重叠要求
        strict_path_overlap = path_overlap > 0.8  # 需要高度重叠
        
        # 2. 地理距离约束 - 只有距离很近才可能相互影响
        geo_proximity = geo_distance < 0.2  # 只有很近的光路才考虑
        
        # 3. 功率影响阈值
        significant_power_impact = power_impact > 0.8
        
        # 4. 强非线性效应
        strong_nonlinear = nonlinear_impact > 0.7
        
        # 5. 波长冲突
        wavelength_conflict = wavelength_impact > 0.9
        
        # 综合影响计算 - 更严格的条件组合
        if geo_proximity:
            # 地理位置近 - 需要满足多个条件
            is_affected = (
                (strict_path_overlap and significant_power_impact) or
                (wavelength_conflict and strong_nonlinear) or
                (strict_path_overlap and wavelength_conflict and power_impact > 0.6)
            )
        else:
            # 地理位置远 - 几乎不受影响，除非条件极端
            is_affected = (
                strict_path_overlap and 
                significant_power_impact and 
                strong_nonlinear and 
                wavelength_conflict
            )
        
        # 计算量化的OSNR变化
        if is_affected:
            osnr_change = (
                path_overlap * 1.2 +
                power_impact * 1.0 +
                wavelength_impact * 1.1 +
                nonlinear_impact * 1.5
            ) * (1.0 - geo_distance * 0.8)
        else:
            osnr_change = max(0, np.random.normal(0.05, 0.1))  # 很小的随机变化
        
        return {
            'is_affected': int(is_affected),
            'osnr_change': float(osnr_change),
            'path_overlap': float(path_overlap),
            'power_impact': float(power_impact),
            'wavelength_impact': float(wavelength_impact),
            'congestion_impact': float(congestion_impact),
            'nonlinear_impact': float(nonlinear_impact),
            'geo_proximity': float(geo_proximity),
            'strict_overlap': float(strict_path_overlap)
        }
    
    def _analyze_path_overlap(self, new_lightpath, target_lightpath):
        """分析路径重叠"""
        new_src, new_dst = new_lightpath
        target_src, target_dst = target_lightpath
        
        # 简化的路径重叠计算
        shared_nodes = len(set([new_src, new_dst]) & set([target_src, target_dst]))
        total_nodes = len(set([new_src, new_dst, target_src, target_dst]))
        
        return shared_nodes / total_nodes if total_nodes > 0 else 0
    
    def _calculate_geographic_impact(self, new_lightpath, target_lightpath):
        """计算地理距离影响"""
        new_src, new_dst = new_lightpath
        target_src, target_dst = target_lightpath
        
        # 计算光路中心点距离
        new_center_lat = (self.topology.node_positions[new_src][1] + self.topology.node_positions[new_dst][1]) / 2
        new_center_lon = (self.topology.node_positions[new_src][0] + self.topology.node_positions[new_dst][0]) / 2
        
        target_center_lat = (self.topology.node_positions[target_src][1] + self.topology.node_positions[target_dst][1]) / 2
        target_center_lon = (self.topology.node_positions[target_src][0] + self.topology.node_positions[target_dst][0]) / 2
        
        distance = np.sqrt((new_center_lat - target_center_lat)**2 + (new_center_lon - target_center_lon)**2)
        max_distance = 10.0  # 约1000km
        
        return min(distance / max_distance, 1.0)
    
    def _calculate_power_impact(self, new_lightpath, target_lightpath, node_features):
        """计算功率影响"""
        new_src, new_dst = new_lightpath
        target_src, target_dst = target_lightpath
        
        # 节点功率差异
        new_power = (node_features[new_src, 0] + node_features[new_dst, 0]) / 2
        target_power = (node_features[target_src, 0] + node_features[target_dst, 0]) / 2
        
        power_diff = abs(new_power - target_power)
        return min(power_diff / 2.0, 1.0)
    
    def _calculate_wavelength_impact(self, new_lightpath, target_lightpath, node_features):
        """计算波长相关影响"""
        new_src, new_dst = new_lightpath
        target_src, target_dst = target_lightpath
        
        # 波长利用率影响
        new_util = (node_features[new_src, 4] + node_features[new_dst, 4]) / 2
        target_util = (node_features[target_src, 4] + node_features[target_dst, 4]) / 2
        
        util_conflict = min(new_util + target_util, 1.0)
        return util_conflict
    
    def _calculate_congestion_impact(self, new_lightpath, target_lightpath, node_features):
        """计算拥塞影响"""
        new_src, new_dst = new_lightpath
        target_src, target_dst = target_lightpath
        
        # 节点负载影响
        total_load = (
            node_features[new_src, 1] + node_features[new_dst, 1] +
            node_features[target_src, 1] + node_features[target_dst, 1]
        ) / 4
        
        return min(total_load, 1.0)
    
    def _calculate_nonlinear_effects(self, new_lightpath, target_lightpath, node_features):
        """计算非线性效应"""
        new_src, new_dst = new_lightpath
        target_src, target_dst = target_lightpath
        
        # 基于功率和光路数量的非线性效应
        avg_power = (
            node_features[new_src, 0] + node_features[new_dst, 0] +
            node_features[target_src, 0] + node_features[target_dst, 0]
        ) / 4
        
        avg_lightpaths = (
            node_features[new_src, 8] + node_features[new_dst, 8] +
            node_features[target_src, 8] + node_features[target_dst, 8]
        ) / 4
        
        nonlinear_factor = (avg_power * avg_lightpaths) / 100
        return min(nonlinear_factor, 1.0)

class EnhancedTrainer:
    """增强的训练器 - 分阶段训练和多任务学习"""
    
    def __init__(self, model, device='cuda'):
        self.model = model.to(device)
        self.device = device
        
        # 分别优化不同组件
        self.selector_optimizer = torch.optim.Adam(
            self.model.subgraph_selector.parameters(), lr=0.001, weight_decay=1e-5
        )
        self.gat_optimizer = torch.optim.Adam([
            {'params': self.model.gat_layers.parameters()},
            {'params': self.model.global_attention.parameters()},
            {'params': self.model.impact_classifier.parameters()},
            {'params': self.model.osnr_regressor.parameters()}
        ], lr=0.0005, weight_decay=1e-4)
        
        # 学习率调度器
        self.selector_scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            self.selector_optimizer, mode='max', factor=0.7, patience=5
        )
        self.gat_scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            self.gat_optimizer, mode='max', factor=0.7, patience=5
        )
        
        # 损失函数
        self.classification_loss = nn.CrossEntropyLoss()
        self.regression_loss = nn.MSELoss()
        
    def train_multi_stage(self, train_loader, test_loader, epochs=100):
        """多阶段训练"""
        
        print("🚀 开始多阶段训练...")
        print("📍 阶段1: 预训练子图选择器 (前30轮)")
        
        best_test_acc = 0
        best_test_auc = 0
        train_history = {'loss': [], 'acc': [], 'test_acc': [], 'test_auc': []}
        
        for epoch in range(epochs):
            # 决定训练策略
            if epoch < 30:
                # 阶段1: 只训练子图选择器
                stage = "子图选择器预训练"
                train_loss, train_acc = self._train_stage1(train_loader)
                self.selector_scheduler.step(train_acc)
            elif epoch < 60:
                # 阶段2: 联合训练但主要关注分类
                stage = "分类器训练"
                train_loss, train_acc = self._train_stage2(train_loader)
                self.gat_scheduler.step(train_acc)
            else:
                # 阶段3: 完整多任务学习
                stage = "多任务学习"
                train_loss, train_acc = self._train_stage3(train_loader)
                self.gat_scheduler.step(train_acc)
                self.selector_scheduler.step(train_acc)
            
            # 测试评估
            test_acc, test_auc, test_details = self._evaluate(test_loader)
            
            # 记录历史
            train_history['loss'].append(train_loss)
            train_history['acc'].append(train_acc)
            train_history['test_acc'].append(test_acc)
            train_history['test_auc'].append(test_auc)
            
            # 更新最佳结果
            if test_acc > best_test_acc:
                best_test_acc = test_acc
                best_test_auc = test_auc
            
            # 进度报告
            if epoch % 10 == 0 or epoch < 10:
                print(f"Epoch {epoch:3d} [{stage}]: "
                     f"Loss={train_loss:.4f}, Train Acc={train_acc:.4f}, "
                     f"Test Acc={test_acc:.4f}, Test AUC={test_auc:.4f}")
        
        print(f"\n🎯 最佳测试结果: 准确率={best_test_acc:.4f}, AUC={best_test_auc:.4f}")
        return train_history, best_test_acc, best_test_auc
    
    def _train_stage1(self, train_loader):
        """阶段1: 预训练子图选择器"""
        self.model.train()
        total_loss, total_acc, total_samples = 0, 0, 0
        
        for batch in train_loader:
            batch = self._to_device(batch)
            
            # 只计算子图选择相关损失
            outputs = self.model(
                batch['graph'], batch['node_features'], batch['edge_features'],
                batch['new_lightpath'], batch['target_lightpath']
            )
            
            # 相关性分数损失 (希望选择的子图与标签相关)
            relevance_loss = self._calculate_relevance_loss(outputs, batch['labels'])
            
            self.selector_optimizer.zero_grad()
            relevance_loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.subgraph_selector.parameters(), 1.0)
            self.selector_optimizer.step()
            
            # 统计
            with torch.no_grad():
                pred_labels = torch.argmax(outputs['impact_logits'], dim=1)
                acc = (pred_labels == batch['labels']).float().mean()
                
            total_loss += relevance_loss.item()
            total_acc += acc.item()
            total_samples += 1
        
        return total_loss / total_samples, total_acc / total_samples
    
    def _train_stage2(self, train_loader):
        """阶段2: 主要训练分类器"""
        self.model.train()
        total_loss, total_acc, total_samples = 0, 0, 0
        
        for batch in train_loader:
            batch = self._to_device(batch)
            
            outputs = self.model(
                batch['graph'], batch['node_features'], batch['edge_features'],
                batch['new_lightpath'], batch['target_lightpath']
            )
            
            # 分类损失 (主要)
            class_loss = self.classification_loss(outputs['impact_logits'], batch['labels'])
            
            # 相关性损失 (辅助)
            relevance_loss = self._calculate_relevance_loss(outputs, batch['labels'])
            
            # 总损失
            total_loss_val = class_loss + 0.3 * relevance_loss
            
            self.gat_optimizer.zero_grad()
            total_loss_val.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
            self.gat_optimizer.step()
            
            # 统计
            with torch.no_grad():
                pred_labels = torch.argmax(outputs['impact_logits'], dim=1)
                acc = (pred_labels == batch['labels']).float().mean()
                
            total_loss += total_loss_val.item()
            total_acc += acc.item()
            total_samples += 1
        
        return total_loss / total_samples, total_acc / total_samples
    
    def _train_stage3(self, train_loader):
        """阶段3: 完整多任务学习"""
        self.model.train()
        total_loss, total_acc, total_samples = 0, 0, 0
        
        for batch in train_loader:
            batch = self._to_device(batch)
            
            outputs = self.model(
                batch['graph'], batch['node_features'], batch['edge_features'],
                batch['new_lightpath'], batch['target_lightpath']
            )
            
            # 分类损失
            class_loss = self.classification_loss(outputs['impact_logits'], batch['labels'])
            
            # 回归损失 (OSNR预测)
            regression_loss = self.regression_loss(
                outputs['osnr_pred'].squeeze(), batch['osnr_changes'].float()
            )
            
            # 相关性损失
            relevance_loss = self._calculate_relevance_loss(outputs, batch['labels'])
            
            # 多任务总损失
            total_loss_val = class_loss + 0.5 * regression_loss + 0.2 * relevance_loss
            
            # 联合优化
            self.gat_optimizer.zero_grad()
            self.selector_optimizer.zero_grad()
            total_loss_val.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
            self.gat_optimizer.step()
            self.selector_optimizer.step()
            
            # 统计
            with torch.no_grad():
                pred_labels = torch.argmax(outputs['impact_logits'], dim=1)
                acc = (pred_labels == batch['labels']).float().mean()
                
            total_loss += total_loss_val.item()
            total_acc += acc.item()
            total_samples += 1
        
        return total_loss / total_samples, total_acc / total_samples
    
    def _calculate_relevance_loss(self, outputs, labels):
        """计算相关性损失 - 希望受影响样本的相关性分数更高"""
        relevance_scores = outputs['relevance_scores']
        
        # 受影响的样本应该有更高的相关性分数
        target_scores = labels.float() * 0.8 + 0.2  # 受影响: 目标1.0, 未受影响: 目标0.2
        
        return F.mse_loss(relevance_scores, target_scores)
    
    def _evaluate(self, test_loader):
        """评估模型"""
        self.model.eval()
        all_predictions = []
        all_labels = []
        all_probs = []
        
        with torch.no_grad():
            for batch in test_loader:
                batch = self._to_device(batch)
                
                outputs = self.model(
                    batch['graph'], batch['node_features'], batch['edge_features'],
                    batch['new_lightpath'], batch['target_lightpath']
                )
                
                probs = F.softmax(outputs['impact_logits'], dim=1)
                pred_labels = torch.argmax(outputs['impact_logits'], dim=1)
                
                all_predictions.extend(pred_labels.cpu().numpy())
                all_labels.extend(batch['labels'].cpu().numpy())
                all_probs.extend(probs[:, 1].cpu().numpy())  # 正类概率
        
        # 计算指标
        acc = np.mean(np.array(all_predictions) == np.array(all_labels))
        
        try:
            auc = roc_auc_score(all_labels, all_probs)
        except:
            auc = 0.5
        
        # 详细分析
        unique_labels = len(set(all_labels))
        if unique_labels > 1:
            report = classification_report(all_labels, all_predictions, output_dict=True)
            details = {
                'precision': report['weighted avg']['precision'],
                'recall': report['weighted avg']['recall'],
                'f1': report['weighted avg']['f1-score']
            }
        else:
            details = {'precision': 0, 'recall': 0, 'f1': 0}
        
        return acc, auc, details
    
    def _to_device(self, batch):
        """将batch移到设备"""
        return {
            'graph': batch['graph'].to(self.device),
            'node_features': batch['node_features'].to(self.device),
            'edge_features': batch['edge_features'].to(self.device),
            'new_lightpath': batch['new_lightpath'].to(self.device),
            'target_lightpath': batch['target_lightpath'].to(self.device),
            'labels': batch['labels'].to(self.device),
            'osnr_changes': batch['osnr_changes'].to(self.device)
        }

class EnhancedDataset(torch.utils.data.Dataset):
    """增强的数据集"""
    
    def __init__(self, scenarios, labels, osnr_changes, graph):
        self.scenarios = scenarios
        self.labels = labels
        self.osnr_changes = osnr_changes
        self.graph = graph
        
    def __len__(self):
        return len(self.scenarios)
    
    def __getitem__(self, idx):
        scenario = self.scenarios[idx]
        
        return {
            'graph': self.graph,
            'node_features': torch.FloatTensor(scenario['node_features']).unsqueeze(0),
            'edge_features': torch.FloatTensor(scenario['edge_features']).unsqueeze(0),
            'new_lightpath': torch.LongTensor([scenario['new_lightpath']]),
            'target_lightpath': torch.LongTensor([scenario['target_lightpath']]),
            'labels': torch.LongTensor([self.labels[idx]]),
            'osnr_changes': torch.FloatTensor([self.osnr_changes[idx]])
        }

def run_enhanced_experiment():
    """运行增强实验"""
    print("🧠 增强版可学习子图GAT实验")
    print("=" * 60)
    
    # 设备设置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 使用设备: {device}")
    
    # 初始化组件
    topology = EnhancedNetworkTopology()
    physics_calc = EnhancedPhysicsCalculator()
    feature_gen = EnhancedFeatureGenerator(topology, physics_calc)
    data_gen = EnhancedDataGenerator(topology, physics_calc, feature_gen)
    
    graph = topology.create_graph()
    print(f"🌐 创建增强日本网络: {graph.num_nodes()}节点, {graph.num_edges()}边")
    
    # 生成数据
    scenarios, labels, osnr_changes = data_gen.generate_enhanced_scenarios(5000)
    
    # 数据划分
    split_idx = int(0.75 * len(scenarios))
    train_scenarios = scenarios[:split_idx]
    train_labels = labels[:split_idx]
    train_osnr = osnr_changes[:split_idx]
    
    test_scenarios = scenarios[split_idx:]
    test_labels = labels[split_idx:]
    test_osnr = osnr_changes[split_idx:]
    
    print(f"📊 数据划分: 训练{len(train_scenarios)}, 测试{len(test_scenarios)}")
    
    # 创建数据集
    train_dataset = EnhancedDataset(train_scenarios, train_labels, train_osnr, graph)
    test_dataset = EnhancedDataset(test_scenarios, test_labels, test_osnr, graph)
    
    # 自定义collate函数处理DGL图
    def collate_fn(batch):
        """自定义collate函数处理DGL图对象"""
        return {
            'graph': batch[0]['graph'],  # 所有样本共享同一个图
            'node_features': torch.cat([item['node_features'] for item in batch], dim=0),
            'edge_features': torch.cat([item['edge_features'] for item in batch], dim=0),
            'new_lightpath': torch.cat([item['new_lightpath'] for item in batch], dim=0),
            'target_lightpath': torch.cat([item['target_lightpath'] for item in batch], dim=0),
            'labels': torch.cat([item['labels'] for item in batch], dim=0),
            'osnr_changes': torch.cat([item['osnr_changes'] for item in batch], dim=0)
        }
    
    train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=1, shuffle=True, collate_fn=collate_fn)
    test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=1, shuffle=False, collate_fn=collate_fn)
    
    # 创建模型
    model = EnhancedSubgraphGAT(
        node_feature_dim=15,
        edge_feature_dim=10,
        hidden_dim=128,
        num_layers=3,
        num_heads=4,
        num_classes=2
    )
    
    print(f"🧠 模型参数: {sum(p.numel() for p in model.parameters() if p.requires_grad)}")
    
    # 训练
    trainer = EnhancedTrainer(model, device)
    train_history, best_acc, best_auc = trainer.train_multi_stage(
        train_loader, test_loader, epochs=100
    )
    
    # 最终评估
    print("\n🧪 最终详细评估...")
    final_acc, final_auc, final_details = trainer._evaluate(test_loader)
    
    print(f"✅ 最终测试准确率: {final_acc:.4f}")
    print(f"   AUC: {final_auc:.4f}")
    print(f"   精确率: {final_details['precision']:.4f}")
    print(f"   召回率: {final_details['recall']:.4f}")
    print(f"   F1分数: {final_details['f1']:.4f}")
    
    # 保存结果
    results = {
        'timestamp': datetime.now().strftime('%Y%m%d_%H%M%S'),
        'final_accuracy': final_acc,
        'final_auc': final_auc,
        'final_details': final_details,
        'best_accuracy': best_acc,
        'best_auc': best_auc,
        'train_history': train_history,
        'model_params': sum(p.numel() for p in model.parameters() if p.requires_grad)
    }
    
    filename = f"enhanced_subgraph_gat_results_{results['timestamp']}.json"
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"📁 结果保存至: {filename}")
    
    return model, results

if __name__ == "__main__":
    model, results = run_enhanced_experiment() 