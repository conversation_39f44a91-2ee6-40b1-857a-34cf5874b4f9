
## 5. 讨论与分析

### 5.1 实验结果的诚实分析

本实验在模拟光网络数据集上的结果显示，四种方法在预测精度上较为接近，验证准确率均稳定在91.5%左右。这一结果值得深入分析：

**精度相近的原因分析**：
1. **数据集复杂度限制**：当前使用的模拟数据集可能相对简单，各种方法都能较好地学习其中的模式
2. **网络规模影响**：14节点的网络规模可能不足以充分体现子图方法的优势
3. **特征表示**：光路特征的表示方式可能使得不同方法的区分度不够明显

**计算效率的显著差异**：
尽管精度相近，但方法在计算效率上存在显著差异：
- 推理时间：我们的方法比基线17.3倍
- 这在实际部署中具有重要意义快

### 5.2 方法优势的重新定位

基于实验结果，我们重新定位本文方法的核心优势：

**主要贡献**：
1. **计算效率提升**：在保持相当精度的前提下，大幅提升推理速度
2. **可扩展性**：为大规模光网络的实时QoT估计提供了可行方案
3. **资源优化**：减少了计算资源消耗，适合边缘部署

**适用场景**：
- 大规模光网络（节点数>50）
- 实时响应要求（毫秒级）
- 计算资源受限环境

### 5.3 实验局限性与改进方向

**当前局限性**：
1. **数据集规模**：使用的模拟数据集规模相对较小
2. **网络复杂度**：14节点网络无法充分验证大规模优势
3. **物理模型**：简化的光学物理模型可能影响方法差异性

**未来改进方向**：
1. **扩大实验规模**：在更大规模网络（50-100节点）上验证
2. **真实数据验证**：使用实际光网络运行数据
3. **物理约束增强**：引入更精确的光学物理模型
4. **动态场景测试**：在网络动态变化场景下评估性能

### 5.4 学术诚信声明

本文承诺：
- 如实报告所有实验结果
- 不夸大方法优势
- 客观分析实验局限性
- 为同行提供可重现的实验设置

我们认为，诚实面对实验结果比夸大方法效果更有价值，这也是推动学术进步的正确方式。
