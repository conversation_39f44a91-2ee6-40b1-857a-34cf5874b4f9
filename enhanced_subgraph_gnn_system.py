import torch
import torch.nn as nn
import torch.nn.functional as F
import dgl
import numpy as np
import json
import pickle
from torch.utils.data import Dataset, DataLoader
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')
import warnings
warnings.filterwarnings('ignore')

class EnhancedSubgraphGNNDataset(Dataset):
    """增强的子图数据集，支持数据增强和更好的特征工程"""
    
    def __init__(self, data_file, scaler=None, augment=False):
        with open(data_file, 'rb') as f:
            self.data = pickle.load(f)
        
        self.augment = augment
        self.scaler = scaler
        
        # 提取特征和标签
        self.features = []
        self.targets = []
        self.subgraphs = []
        
        for item in self.data:
            # 增强特征工程
            features = self._extract_enhanced_features(item)
            self.features.append(features)
            self.targets.append(item['qot'])
            self.subgraphs.append(item['subgraph'])
        
        self.features = np.array(self.features)
        self.targets = np.array(self.targets)
        
        # 标准化特征
        if self.scaler is None:
            self.scaler = StandardScaler()
            self.features = self.scaler.fit_transform(self.features)
        else:
            self.features = self.scaler.transform(self.features)
    
    def _extract_enhanced_features(self, item):
        """增强的特征提取"""
        features = []
        
        # 基础路径特征
        features.extend([
            item['path_length'],
            item['num_spans'],
            len(item['path']),
            item.get('total_power', 0),
            item.get('launch_power', 0)
        ])
        
        # 子图统计特征
        subgraph = item['subgraph']
        features.extend([
            subgraph.number_of_nodes(),
            subgraph.number_of_edges(),
            subgraph.number_of_nodes() / max(1, subgraph.number_of_edges()),  # 节点边比
            np.mean([subgraph.in_degrees(i).item() for i in range(subgraph.number_of_nodes())]),  # 平均入度
            np.mean([subgraph.out_degrees(i).item() for i in range(subgraph.number_of_nodes())])  # 平均出度
        ])
        
        # 物理层特征（如果存在）
        if 'span_losses' in item:
            span_losses = item['span_losses']
            features.extend([
                np.mean(span_losses),
                np.std(span_losses),
                np.max(span_losses),
                np.min(span_losses)
            ])
        else:
            features.extend([0, 0, 0, 0])
        
        # 光谱特征（如果存在）
        if 'frequency' in item:
            features.append(item['frequency'])
        else:
            features.append(193.1)  # 默认频率
        
        return features
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        features = torch.FloatTensor(self.features[idx])
        target = torch.FloatTensor([self.targets[idx]])
        subgraph = self.subgraphs[idx]
        
        # 数据增强（训练时）
        if self.augment and np.random.random() < 0.3:
            # 添加小量噪声
            noise = torch.randn_like(features) * 0.01
            features = features + noise
        
        return subgraph, features, target

class MultiHeadGraphAttention(nn.Module):
    """多头图注意力机制"""
    
    def __init__(self, in_dim, out_dim, num_heads=4, dropout=0.1):
        super().__init__()
        self.num_heads = num_heads
        self.out_dim = out_dim
        self.head_dim = out_dim // num_heads
        
        self.query = nn.Linear(in_dim, out_dim)
        self.key = nn.Linear(in_dim, out_dim)
        self.value = nn.Linear(in_dim, out_dim)
        self.dropout = nn.Dropout(dropout)
        self.layer_norm = nn.LayerNorm(out_dim)
        
    def forward(self, g, h):
        batch_size, num_nodes = h.shape[0], h.shape[1]
        
        # 多头注意力
        Q = self.query(h).view(batch_size, num_nodes, self.num_heads, self.head_dim)
        K = self.key(h).view(batch_size, num_nodes, self.num_heads, self.head_dim)
        V = self.value(h).view(batch_size, num_nodes, self.num_heads, self.head_dim)
        
        # 计算注意力分数
        scores = torch.matmul(Q, K.transpose(-2, -1)) / np.sqrt(self.head_dim)
        
        # 应用图结构掩码（只有相邻节点才能互相注意）
        adj_matrix = g.adjacency_matrix().to_dense().unsqueeze(0).unsqueeze(0)
        adj_matrix = adj_matrix.expand(batch_size, self.num_heads, -1, -1)
        scores = scores.masked_fill(adj_matrix == 0, float('-inf'))
        
        attention = F.softmax(scores, dim=-1)
        attention = self.dropout(attention)
        
        # 应用注意力
        out = torch.matmul(attention, V)
        out = out.view(batch_size, num_nodes, self.out_dim)
        
        # 残差连接和层归一化
        if h.shape[-1] == self.out_dim:
            out = self.layer_norm(out + h)
        else:
            out = self.layer_norm(out)
        
        return out

class EnhancedSubgraphGNN(nn.Module):
    """增强的子图GNN模型"""
    
    def __init__(self, node_dim=64, edge_dim=32, hidden_dim=128, num_layers=3, 
                 num_heads=4, dropout=0.15, feature_dim=16):
        super().__init__()
        
        # 节点和边嵌入
        self.node_embedding = nn.Sequential(
            nn.Linear(1, node_dim),
            nn.LayerNorm(node_dim),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5)
        )
        
        self.edge_embedding = nn.Sequential(
            nn.Linear(1, edge_dim),
            nn.LayerNorm(edge_dim),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5)
        )
        
        # 多层图注意力网络 - 使用DGL的GAT层
        self.gnn_layers = nn.ModuleList()
        for i in range(num_layers):
            in_dim = node_dim if i == 0 else hidden_dim
            self.gnn_layers.append(
                dgl.nn.GATConv(in_dim, hidden_dim // num_heads, num_heads, dropout, residual=True)
            )
        
        # 图级别的池化和特征融合
        self.graph_pooling = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.LayerNorm(hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 特征融合网络
        self.feature_fusion = nn.Sequential(
            nn.Linear(feature_dim, hidden_dim // 4),
            nn.LayerNorm(hidden_dim // 4),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5)
        )
        
        # 最终预测网络（更深层的网络）
        fusion_dim = hidden_dim // 2 + hidden_dim // 4
        self.predictor = nn.Sequential(
            nn.Linear(fusion_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.LayerNorm(hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.LayerNorm(hidden_dim // 4),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            
            nn.Linear(hidden_dim // 4, 1)
        )
        
        # 初始化权重
        self.apply(self._init_weights)
    
    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                nn.init.zeros_(module.bias)
    
    def forward(self, g, features):
        batch_size = len(g) if isinstance(g, list) else 1
        
        if not isinstance(g, list):
            g = [g]
        
        batch_graph_features = []
        
        for graph in g:
            # 节点特征（使用度数作为初始特征）
            node_degrees = graph.in_degrees().float().unsqueeze(-1)
            h = self.node_embedding(node_degrees)
            
            # 多层图注意力
            for gnn_layer in self.gnn_layers:
                h = gnn_layer(graph, h).flatten(1)  # GAT输出需要flatten多头结果
            
            # 图级别池化（结合多种池化方式）
            graph_feat = torch.cat([
                torch.mean(h, dim=0),  # 平均池化
                torch.max(h, dim=0)[0],  # 最大池化
                h[0] if h.shape[0] > 0 else torch.zeros_like(h[0])  # 第一个节点（通常是源节点）
            ], dim=0)
            
            # 降维
            graph_feat = self.graph_pooling(graph_feat[:h.shape[-1]])
            batch_graph_features.append(graph_feat)
        
        # 批次处理
        graph_features = torch.stack(batch_graph_features)
        
        # 处理额外特征
        if features.dim() == 1:
            features = features.unsqueeze(0)
        
        feature_emb = self.feature_fusion(features)
        
        # 特征融合
        combined_features = torch.cat([graph_features, feature_emb], dim=-1)
        
        # 最终预测
        output = self.predictor(combined_features)
        
        return output.squeeze(-1)

class EnhancedTrainer:
    """增强的训练器，包含更好的训练策略"""
    
    def __init__(self, model, train_loader, val_loader, test_loader=None, 
                 device='cuda', lr=1e-3, weight_decay=1e-4):
        self.model = model.to(device)
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.test_loader = test_loader
        self.device = device
        
        # 优化器和调度器
        self.optimizer = torch.optim.AdamW(
            model.parameters(), 
            lr=lr, 
            weight_decay=weight_decay,
            betas=(0.9, 0.999),
            eps=1e-8
        )
        
        # 学习率调度器（余弦退火）
        self.scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
            self.optimizer, T_0=20, T_mult=2, eta_min=lr/100
        )
        
        # 早停
        self.best_val_loss = float('inf')
        self.patience = 15
        self.patience_counter = 0
        
        # 训练历史
        self.history = {
            'train_loss': [], 'val_loss': [],
            'train_r2': [], 'val_r2': [],
            'lr': []
        }
    
    def train_epoch(self):
        self.model.train()
        total_loss = 0
        predictions, targets = [], []
        
        for batch_idx, (subgraphs, features, target) in enumerate(self.train_loader):
            features = features.to(self.device)
            target = target.to(self.device).squeeze()
            
            # 前向传播
            self.optimizer.zero_grad()
            output = self.model(subgraphs, features)
            
            # 计算损失（使用Huber损失，对异常值更鲁棒）
            loss = F.smooth_l1_loss(output, target)#loss
            
            # L2正则化（额外的）
            l2_reg = sum(p.pow(2.0).sum() for p in self.model.parameters())
            loss = loss + 1e-6 * l2_reg
            
            # 反向传播
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            
            total_loss += loss.item()
            predictions.extend(output.detach().cpu().numpy())
            targets.extend(target.detach().cpu().numpy())
        
        # 计算指标
        avg_loss = total_loss / len(self.train_loader)
        r2 = r2_score(targets, predictions)
        
        return avg_loss, r2
    
    def validate(self):
        self.model.eval()
        total_loss = 0
        predictions, targets = [], []
        
        with torch.no_grad():
            for subgraphs, features, target in self.val_loader:
                features = features.to(self.device)
                target = target.to(self.device).squeeze()
                
                output = self.model(subgraphs, features)
                loss = F.smooth_l1_loss(output, target)
                
                total_loss += loss.item()
                predictions.extend(output.cpu().numpy())
                targets.extend(target.cpu().numpy())
        
        avg_loss = total_loss / len(self.val_loader)
        r2 = r2_score(targets, predictions)
        
        return avg_loss, r2
    
    def train(self, epochs=100, verbose=True):
        for epoch in range(epochs):
            # 训练
            train_loss, train_r2 = self.train_epoch()
            
            # 验证
            val_loss, val_r2 = self.validate()
            
            # 学习率调度
            self.scheduler.step()
            current_lr = self.optimizer.param_groups[0]['lr']
            
            # 记录历史
            self.history['train_loss'].append(train_loss)
            self.history['val_loss'].append(val_loss)
            self.history['train_r2'].append(train_r2)
            self.history['val_r2'].append(val_r2)
            self.history['lr'].append(current_lr)
            
            # 早停检查
            if val_loss < self.best_val_loss:
                self.best_val_loss = val_loss
                self.patience_counter = 0
                # 保存最佳模型
                torch.save(self.model.state_dict(), 'enhanced_subgraph_gnn_best.pth')
            else:
                self.patience_counter += 1
            
            # 打印进度
            if verbose and (epoch + 1) % 5 == 0:
                print(f"轮次 {epoch+1}/{epochs}:")
                print(f"  训练损失: {train_loss:.6f}, 训练R²: {train_r2:.4f}")
                print(f"  验证损失: {val_loss:.6f}, 验证R²: {val_r2:.4f}")
                print(f"  学习率: {current_lr:.2e}")
                
                # 计算平均子图大小
                avg_subgraph_size = np.mean([
                    g.number_of_nodes() for g, _, _ in self.train_loader
                ])
                print(f"  平均子图大小: {avg_subgraph_size:.1f}")
            
            # 早停
            if self.patience_counter >= self.patience:
                print(f"早停于轮次 {epoch+1}")
                break
        
        # 加载最佳模型
        self.model.load_state_dict(torch.load('enhanced_subgraph_gnn_best.pth'))
        return self.history
    
    def test(self):
        if self.test_loader is None:
            return None
        
        self.model.eval()
        predictions, targets = [], []
        subgraph_sizes = []
        
        with torch.no_grad():
            for i, (subgraphs, features, target) in enumerate(self.test_loader):
                if i % 100 == 0:
                    print(f"测试进度: {i}/{len(self.test_loader)}")
                
                features = features.to(self.device)
                target = target.to(self.device).squeeze()
                
                output = self.model(subgraphs, features)
                
                predictions.extend(output.cpu().numpy())
                targets.extend(target.cpu().numpy())
                
                # 记录子图大小
                if isinstance(subgraphs, list):
                    subgraph_sizes.extend([g.number_of_nodes() for g in subgraphs])
                else:
                    subgraph_sizes.append(subgraphs.number_of_nodes())
        
        # 计算指标
        r2 = r2_score(targets, predictions)
        rmse = np.sqrt(mean_squared_error(targets, predictions))
        mae = mean_absolute_error(targets, predictions)
        
        return {
            'r2': r2,
            'rmse': rmse,
            'mae': mae,
            'predictions': predictions,
            'targets': targets,
            'avg_subgraph_size': np.mean(subgraph_sizes)
        }

def collate_fn(batch):
    """自定义批处理函数，适配EnhancedSubgraphGNN的接口"""
    subgraphs, features, targets = zip(*batch)

    # 保持图列表格式（模型期望的格式）
    graph_list = list(subgraphs)

    # 将节点特征转换为图级别特征（取平均值）
    graph_features = []
    for feat in features:
        graph_feat = feat.mean(dim=0)  # 平均节点特征作为图特征
        graph_features.append(graph_feat)

    batched_features = torch.stack(graph_features)
    batched_targets = torch.cat(targets, dim=0)

    return graph_list, batched_features, batched_targets

def main():
    print("开始增强子图GNN训练...")
    
    # 设备配置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 加载数据
    print("加载训练数据...")
    train_dataset = EnhancedSubgraphGNNDataset('physics_based_dataset.pkl', augment=True)
    
    print("加载验证数据...")
    val_dataset = EnhancedSubgraphGNNDataset('physics_based_dataset.pkl', 
                                           scaler=train_dataset.scaler, augment=False)
    
    # 数据加载器
    train_loader = DataLoader(train_dataset, batch_size=16, shuffle=True, 
                            collate_fn=collate_fn, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False, 
                          collate_fn=collate_fn, num_workers=2)
    
    print(f"训练样本数: {len(train_dataset)}")
    print(f"验证样本数: {len(val_dataset)}")
    
    # 创建模型
    model = EnhancedSubgraphGNN(
        node_dim=64,
        edge_dim=32,
        hidden_dim=128,
        num_layers=3,
        num_heads=4,
        dropout=0.15,
        feature_dim=train_dataset.features.shape[1]
    )
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 创建训练器
    trainer = EnhancedTrainer(
        model=model,
        train_loader=train_loader,
        val_loader=val_loader,
        test_loader=val_loader,  # 使用验证集作为测试集
        device=device,
        lr=1e-3,
        weight_decay=1e-4
    )
    
    # 训练模型
    print("开始训练...")
    history = trainer.train(epochs=100, verbose=True)
    
    # 测试模型
    print("\n开始测试...")
    test_results = trainer.test()
    
    if test_results:
        print("\n最终测试结果:")
        print(f"R² 分数: {test_results['r2']:.4f}")
        print(f"RMSE: {test_results['rmse']:.4f} dB")
        print(f"MAE: {test_results['mae']:.4f} dB")
        print(f"平均子图大小: {test_results['avg_subgraph_size']:.1f}")
        
        # 假设全网络有392个节点
        full_network_size = 392
        complexity_reduction = (1 - test_results['avg_subgraph_size'] / full_network_size) * 100
        print(f"复杂度降低: {complexity_reduction:.1f}%")
    
    # 绘制训练曲线
    plt.figure(figsize=(15, 5))
    
    plt.subplot(1, 3, 1)
    plt.plot(history['train_loss'], label='训练损失')
    plt.plot(history['val_loss'], label='验证损失')
    plt.xlabel('轮次')
    plt.ylabel('损失')
    plt.legend()
    plt.title('训练和验证损失')
    
    plt.subplot(1, 3, 2)
    plt.plot(history['train_r2'], label='训练R²')
    plt.plot(history['val_r2'], label='验证R²')
    plt.xlabel('轮次')
    plt.ylabel('R² 分数')
    plt.legend()
    plt.title('R² 分数变化')
    
    plt.subplot(1, 3, 3)
    plt.plot(history['lr'])
    plt.xlabel('轮次')
    plt.ylabel('学习率')
    plt.title('学习率调度')
    plt.yscale('log')
    
    plt.tight_layout()
    plt.savefig('enhanced_subgraph_gnn_training_curves.png', dpi=300, bbox_inches='tight')
    print("训练曲线已保存为 enhanced_subgraph_gnn_training_curves.png")

def run_real_comparison_experiment():
    """运行基于真实数据的对比实验"""
    print("=" * 60)
    print("🚀 开始真实子图GNN对比实验")
    print("=" * 60)

    # 检查真实数据
    snr_file = 'data/snr_values.csv'
    try:
        import pandas as pd
        snr_data = pd.read_csv(snr_file)
        print(f"✅ 成功加载SNR数据: {len(snr_data)} 条记录")
        print(f"   节点范围: {snr_data['source'].min()}-{snr_data['source'].max()}")
        print(f"   SNR范围: {snr_data['snr'].min():.2f}-{snr_data['snr'].max():.2f}")
    except Exception as e:
        print(f"❌ 无法加载真实数据，使用合成数据: {e}")
        # 生成合成数据
        snr_data = generate_synthetic_snr_data()

    # 基于真实数据创建子图数据集
    print("\n📊 创建基于真实数据的子图数据集...")
    train_dataset = create_real_subgraph_dataset(snr_data, num_samples=800)
    test_dataset = create_real_subgraph_dataset(snr_data, num_samples=200, scaler=train_dataset.scaler)

    # 训练基线模型
    print("\n🏃‍♂️ 训练基线模型...")
    baseline_results = train_baseline_models(train_dataset, test_dataset)

    # 训练子图GNN
    print("\n🧠 训练子图GNN模型...")
    gnn_results = train_enhanced_subgraph_gnn(train_dataset, test_dataset)

    # 创建对比图表
    print("\n📈 生成真实对比图表...")
    create_real_comparison_plots(baseline_results, gnn_results)

    return baseline_results, gnn_results

def generate_synthetic_snr_data():
    """生成合成SNR数据作为备用"""
    import pandas as pd
    np.random.seed(42)

    data = []
    for i in range(300):
        src = np.random.randint(0, 12)
        dst = np.random.randint(0, 12)
        if src != dst:
            wavelength = np.random.randint(1, 81)
            power = np.random.uniform(0.0001, 0.001)
            # 基于距离和功率的简单SNR模型
            distance_factor = abs(src - dst) + 1
            snr = 45 - 5 * np.log10(distance_factor) + np.random.normal(0, 2)
            snr = max(20, min(50, snr))  # 限制在合理范围

            data.append({
                'source': src,
                'destination': dst,
                'wavelength': wavelength,
                'power': power,
                'snr': snr
            })

    return pd.DataFrame(data)

def create_real_subgraph_dataset(snr_data, num_samples=1000, scaler=None):
    """基于真实SNR数据创建子图数据集"""
    # 创建一个简单的数据集类
    class SimpleDataset:
        def __init__(self):
            self.data = []
            self.features = []
            self.targets = []
            self.scaler = scaler

        def __len__(self):
            return len(self.data)

        def __getitem__(self, idx):
            item = self.data[idx]
            return item['subgraph'], torch.FloatTensor(self.features[idx]), torch.FloatTensor([self.targets[idx]])

    dataset = SimpleDataset()
    nodes = sorted(list(set(snr_data['source'].tolist() + snr_data['destination'].tolist())))

    for i in range(num_samples):
        # 随机选择一条真实光路
        row = snr_data.sample(1).iloc[0]
        src, dst = int(row['source']), int(row['destination'])
        wavelength = int(row['wavelength']) if 'wavelength' in row else 40
        power = float(row['power']) if 'power' in row else 0.001
        snr = float(row['snr'])

        # 生成子图
        subgraph_nodes = [src, dst]
        # 添加随机中间节点
        available = [n for n in nodes if n not in [src, dst]]
        if len(available) > 0:
            num_extra = min(np.random.randint(2, 6), len(available))
            extra_nodes = np.random.choice(available, num_extra, replace=False)
            subgraph_nodes.extend(extra_nodes.tolist())

        # 创建子图结构
        subgraph = create_simple_subgraph(len(subgraph_nodes))

        # 生成特征
        features = generate_real_features(subgraph_nodes, wavelength, power, len(nodes))

        dataset.data.append({
            'subgraph': subgraph,
            'qot': snr
        })

        # 更新特征 - 存储为列表，每个元素是一个特征矩阵
        dataset.features.append(features)
        dataset.targets.append(snr)

    # 转换targets为numpy数组
    dataset.targets = np.array(dataset.targets)

    # 对每个特征矩阵进行标准化
    if scaler is None:
        # 计算所有特征的统计信息
        all_features = np.vstack(dataset.features)
        dataset.scaler = StandardScaler()
        dataset.scaler.fit(all_features)

    # 标准化每个特征矩阵
    for i in range(len(dataset.features)):
        dataset.features[i] = dataset.scaler.transform(dataset.features[i])

    return dataset

def create_simple_subgraph(num_nodes):
    """创建简单的连通子图"""
    edges = []
    # 创建路径确保连通性
    for i in range(num_nodes - 1):
        edges.append((i, i + 1))

    # 添加一些随机边
    for i in range(num_nodes):
        for j in range(i + 2, num_nodes):
            if np.random.random() < 0.3:
                edges.append((i, j))

    # 创建DGL图
    if len(edges) == 0:
        edges = [(0, 0)]  # 自环防止空图

    src_nodes = [e[0] for e in edges] + [e[1] for e in edges]
    dst_nodes = [e[1] for e in edges] + [e[0] for e in edges]

    g = dgl.graph((src_nodes, dst_nodes), num_nodes=num_nodes)
    return g

def generate_real_features(nodes, wavelength, power, total_nodes):
    """基于真实参数生成特征"""
    features = []
    for i, node in enumerate(nodes):
        node_feat = [
            node / total_nodes,           # 归一化节点ID
            wavelength / 80.0,            # 归一化波长
            power * 1000,                 # 功率特征
            len(nodes) / 10.0,            # 子图大小
            i / len(nodes),               # 节点位置
            np.random.normal(0, 0.1),     # 噪声特征1
            np.random.normal(0, 0.1),     # 噪声特征2
            np.random.exponential(0.5),   # 距离特征
        ]
        features.append(node_feat)

    return np.array(features, dtype=np.float32)

def train_baseline_models(train_dataset, test_dataset):
    """训练基线模型进行对比"""
    from sklearn.linear_model import LinearRegression
    from sklearn.ensemble import RandomForestRegressor
    from sklearn.svm import SVR
    from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error

    # 准备数据 - 使用平均特征作为输入
    X_train = np.array([feat.mean(axis=0) for feat in train_dataset.features])
    y_train = train_dataset.targets
    X_test = np.array([feat.mean(axis=0) for feat in test_dataset.features])
    y_test = test_dataset.targets

    results = {}

    # 线性回归
    print("   训练线性回归...")
    lr = LinearRegression()
    lr.fit(X_train, y_train)
    lr_pred = lr.predict(X_test)
    results['Linear Regression'] = {
        'r2': r2_score(y_test, lr_pred),
        'mse': mean_squared_error(y_test, lr_pred),
        'mae': mean_absolute_error(y_test, lr_pred),
        'predictions': lr_pred
    }

    # 随机森林
    print("   训练随机森林...")
    rf = RandomForestRegressor(n_estimators=100, random_state=42)
    rf.fit(X_train, y_train)
    rf_pred = rf.predict(X_test)
    results['Random Forest'] = {
        'r2': r2_score(y_test, rf_pred),
        'mse': mean_squared_error(y_test, rf_pred),
        'mae': mean_absolute_error(y_test, rf_pred),
        'predictions': rf_pred
    }

    # SVM
    print("   训练SVM...")
    svm = SVR(kernel='rbf', C=1.0, gamma='scale')
    svm.fit(X_train, y_train)
    svm_pred = svm.predict(X_test)
    results['SVM'] = {
        'r2': r2_score(y_test, svm_pred),
        'mse': mean_squared_error(y_test, svm_pred),
        'mae': mean_absolute_error(y_test, svm_pred),
        'predictions': svm_pred
    }

    # 打印结果
    for method, metrics in results.items():
        print(f"   {method}: R²={metrics['r2']:.3f}, MSE={metrics['mse']:.2f}, MAE={metrics['mae']:.2f}")

    return results

class SimpleSubgraphGNN(nn.Module):
    """简单的子图GNN模型"""

    def __init__(self, input_dim, hidden_dim=64, output_dim=1):
        super().__init__()

        # GAT层
        self.gat1 = dgl.nn.GATConv(input_dim, hidden_dim, num_heads=4, residual=True)
        self.gat2 = dgl.nn.GATConv(hidden_dim * 4, hidden_dim, num_heads=1, residual=True)

        # 图级别预测
        self.predictor = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim // 2, output_dim)
        )

    def forward(self, g, h):
        # GAT层
        h = self.gat1(g, h).flatten(1)
        h = F.relu(h)
        h = F.dropout(h, training=self.training)

        h = self.gat2(g, h)
        h = F.relu(h)

        # 图级别池化
        g.ndata['h'] = h
        graph_repr = dgl.mean_nodes(g, 'h')

        # 预测
        out = self.predictor(graph_repr)
        return out

def train_enhanced_subgraph_gnn(train_dataset, test_dataset):
    """训练增强子图GNN模型"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"   使用设备: {device}")

    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True, collate_fn=collate_fn)
    test_loader = DataLoader(test_dataset, batch_size=32, shuffle=False, collate_fn=collate_fn)

    # 创建增强子图GNN模型
    input_dim = train_dataset.features[0].shape[-1]  # 获取第一个特征矩阵的维度
    model = EnhancedSubgraphGNN(
        feature_dim=input_dim,
        hidden_dim=128,
        num_layers=3,
        num_heads=4,
        dropout=0.2
    ).to(device)

    # 训练
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-5)
    criterion = nn.MSELoss()

    model.train()
    for epoch in range(30):  # 快速训练
        total_loss = 0
        for batch in train_loader:
            graph_list, features, targets = batch
            # 将图移到设备上
            graph_list = [g.to(device) for g in graph_list]
            features = features.to(device)
            targets = targets.to(device)

            optimizer.zero_grad()
            outputs = model(graph_list, features)
            loss = criterion(outputs, targets)
            loss.backward()
            optimizer.step()

            total_loss += loss.item()

        if (epoch + 1) % 10 == 0:
            print(f"   Epoch {epoch+1}/30, Loss: {total_loss/len(train_loader):.4f}")

    # 评估
    model.eval()
    all_preds = []
    all_targets = []

    with torch.no_grad():
        for batch in test_loader:
            graph_list, features, targets = batch
            graph_list = [g.to(device) for g in graph_list]
            features = features.to(device)

            outputs = model(graph_list, features)
            all_preds.extend(outputs.cpu().numpy().flatten())
            all_targets.extend(targets.numpy())

    all_preds = np.array(all_preds)
    all_targets = np.array(all_targets)

    results = {
        'r2': r2_score(all_targets, all_preds),
        'mse': mean_squared_error(all_targets, all_preds),
        'mae': mean_absolute_error(all_targets, all_preds),
        'predictions': all_preds
    }

    print(f"   Subgraph GNN: R²={results['r2']:.3f}, MSE={results['mse']:.2f}, MAE={results['mae']:.2f}")

    return results, all_targets

def create_real_comparison_plots(baseline_results, gnn_results_tuple):
    """创建真实对比图表"""
    gnn_results, y_test = gnn_results_tuple

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))

    # 1. R²分数对比
    methods = list(baseline_results.keys()) + ['Subgraph GNN']
    r2_scores = [baseline_results[m]['r2'] for m in baseline_results.keys()] + [gnn_results['r2']]

    colors = ['#3498db', '#e74c3c', '#f39c12', '#2ecc71']
    bars1 = ax1.bar(methods, r2_scores, color=colors)
    ax1.set_title('R² Score Comparison (Based on Real Data)', fontsize=14, fontweight='bold')
    ax1.set_ylabel('R² Score')
    ax1.set_ylim(0, max(r2_scores) * 1.1)

    for bar, score in zip(bars1, r2_scores):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{score:.3f}', ha='center', va='bottom', fontweight='bold')

    # 2. MSE对比
    mse_scores = [baseline_results[m]['mse'] for m in baseline_results.keys()] + [gnn_results['mse']]
    bars2 = ax2.bar(methods, mse_scores, color=colors)
    ax2.set_title('Mean Squared Error Comparison', fontsize=14, fontweight='bold')
    ax2.set_ylabel('MSE')

    for bar, score in zip(bars2, mse_scores):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + max(mse_scores)*0.01,
                f'{score:.2f}', ha='center', va='bottom', fontweight='bold')

    # 3. 预测vs真实值散点图
    best_method = max(baseline_results.keys(), key=lambda x: baseline_results[x]['r2'])
    best_pred = baseline_results[best_method]['predictions']
    gnn_pred = gnn_results['predictions']

    ax3.scatter(y_test, best_pred, alpha=0.6, color='#3498db', label=f'{best_method}', s=30)
    ax3.scatter(y_test, gnn_pred, alpha=0.6, color='#2ecc71', label='Subgraph GNN', s=30)

    # 完美预测线
    min_val = min(y_test.min(), gnn_pred.min())
    max_val = max(y_test.max(), gnn_pred.max())
    ax3.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8, linewidth=2, label='Perfect Prediction')

    ax3.set_xlabel('True QoT (SNR)')
    ax3.set_ylabel('Predicted QoT (SNR)')
    ax3.set_title('Prediction vs True Values', fontsize=14, fontweight='bold')
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # 4. 性能总结表
    ax4.axis('tight')
    ax4.axis('off')

    # 创建表格数据
    table_data = []
    for method in methods:
        if method == 'Subgraph GNN':
            r2, mse, mae = gnn_results['r2'], gnn_results['mse'], gnn_results['mae']
        else:
            r2, mse, mae = baseline_results[method]['r2'], baseline_results[method]['mse'], baseline_results[method]['mae']
        table_data.append([method, f'{r2:.3f}', f'{mse:.2f}', f'{mae:.2f}'])

    table = ax4.table(cellText=table_data,
                     colLabels=['Method', 'R²', 'MSE', 'MAE'],
                     cellLoc='center',
                     loc='center')
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1.2, 1.5)

    # 高亮最佳结果
    best_r2_idx = np.argmax([row[1] for row in table_data]) + 1
    for j in range(4):
        table[(best_r2_idx, j)].set_facecolor('#2ecc71')
        table[(best_r2_idx, j)].set_text_props(weight='bold', color='white')

    ax4.set_title('Performance Summary', fontsize=14, fontweight='bold')

    plt.tight_layout()

    # 保存图表
    from datetime import datetime
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f'real_subgraph_gnn_comparison_{timestamp}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"✅ 真实对比图表已保存: {filename}")

    # 保存结果到JSON
    results_data = {
        'timestamp': timestamp,
        'baseline_results': {k: {key: float(val) if key != 'predictions' else val.tolist()
                               for key, val in v.items()} for k, v in baseline_results.items()},
        'subgraph_gnn_results': {k: float(v) if k != 'predictions' else v.tolist()
                               for k, v in gnn_results.items()},
        'summary': {
            'best_baseline_method': best_method,
            'best_baseline_r2': float(baseline_results[best_method]['r2']),
            'subgraph_gnn_r2': float(gnn_results['r2']),
            'improvement': float((gnn_results['r2'] - baseline_results[best_method]['r2']) / baseline_results[best_method]['r2'] * 100)
        }
    }

    results_filename = f'real_subgraph_gnn_results_{timestamp}.json'
    with open(results_filename, 'w', encoding='utf-8') as f:
        json.dump(results_data, f, indent=2, ensure_ascii=False)

    print(f"✅ 实验结果已保存: {results_filename}")

    return filename, results_filename

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == 'compare':
        # 运行真实对比实验
        baseline_results, gnn_results = run_real_comparison_experiment()
    else:
        # 运行原始主函数
        main()