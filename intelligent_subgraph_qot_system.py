#!/usr/bin/env python3
"""
智能子图QoT估计系统
结合可学习相关性评分、动态影响识别和增强GAT架构
目标：训练后精度高、预测快，与全图GNN基线对比

关键创新：
1. 物理感知的相关性评分网络
2. 自适应子图构建策略
3. 多尺度注意力机制
4. 端到端优化的动态更新
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import dgl
import numpy as np
import json
import pickle
import time
from typing import Dict, List, Tuple, Optional
from datetime import datetime
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, r2_score, mean_squared_error
from dgl.nn.pytorch.conv import GATConv
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

class PhysicsAwareRelevanceScorer(nn.Module):
    """物理感知的相关性评分网络 - 核心创新模块"""
    
    def __init__(self, node_feature_dim, hidden_dim=128):
        super(PhysicsAwareRelevanceScorer, self).__init__()
        
        # 物理特征编码器
        self.physics_encoder = nn.Sequential(
            nn.Linear(node_feature_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # 光路交互建模网络
        self.interaction_network = nn.Sequential(
            nn.Linear(hidden_dim * 4, hidden_dim * 2),  # [新光路特征, 目标光路特征, 交互特征, 距离特征]
            nn.LayerNorm(hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.15),
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, 1),
            nn.Sigmoid()  # 输出0-1的相关性分数
        )
        
        # 可学习的物理参数
        self.crosstalk_weight = nn.Parameter(torch.tensor(0.3))
        self.power_weight = nn.Parameter(torch.tensor(0.25))
        self.distance_weight = nn.Parameter(torch.tensor(0.2))
        self.wavelength_weight = nn.Parameter(torch.tensor(0.25))
        
        # 距离编码器（学习不同跳数的影响）
        self.distance_embedding = nn.Embedding(20, hidden_dim // 4)
        
    def compute_physical_interaction(self, new_lightpath_features, target_lightpath_features, 
                                   distance_hops):
        """计算物理层交互特征"""
        # 功率相关性
        power_diff = torch.abs(new_lightpath_features[:, 0] - target_lightpath_features[:, 0])  # 功率差异
        power_interaction = torch.exp(-power_diff * self.power_weight)
        
        # 波长相关性（临近波长干扰更强）
        wavelength_diff = torch.abs(new_lightpath_features[:, 2] - target_lightpath_features[:, 2])
        wavelength_interaction = torch.exp(-wavelength_diff * self.wavelength_weight)
        
        # 地理距离影响
        geo_distance = torch.sqrt(
            (new_lightpath_features[:, 5] - target_lightpath_features[:, 5])**2 + 
            (new_lightpath_features[:, 6] - target_lightpath_features[:, 6])**2
        )
        distance_interaction = torch.exp(-geo_distance * self.distance_weight)
        
        # 串扰建模
        load_product = new_lightpath_features[:, 1] * target_lightpath_features[:, 1]  # 负载交互
        crosstalk_factor = torch.tanh(load_product * self.crosstalk_weight)
        
        # 距离编码
        distance_emb = self.distance_embedding(distance_hops.clamp(0, 19))
        
        # 组合交互特征
        interaction_features = torch.stack([
            power_interaction, wavelength_interaction, 
            distance_interaction, crosstalk_factor
        ], dim=1)
        
        return interaction_features, distance_emb
    
    def forward(self, full_graph, node_features, new_lightpath_nodes, 
                target_lightpath_nodes, candidate_nodes):
        """
        计算候选节点的相关性分数
        
        Args:
            full_graph: 完整网络图
            node_features: 全网节点特征 [N, feature_dim]
            new_lightpath_nodes: 新光路节点对 [src, dst]
            target_lightpath_nodes: 目标光路节点对 [src, dst]
            candidate_nodes: 候选节点列表
        
        Returns:
            relevance_scores: 候选节点的相关性分数
        """
        device = node_features.device
        
        # 获取关键光路特征
        new_src_feat = node_features[new_lightpath_nodes[0]]
        new_dst_feat = node_features[new_lightpath_nodes[1]]
        target_src_feat = node_features[target_lightpath_nodes[0]]
        target_dst_feat = node_features[target_lightpath_nodes[1]]
        
        # 光路级别特征（平均）
        new_lightpath_feat = (new_src_feat + new_dst_feat) / 2
        target_lightpath_feat = (target_src_feat + target_dst_feat) / 2
        
        relevance_scores = []
        
        for node_idx in candidate_nodes:
            current_node_feat = node_features[node_idx]
            
            # 计算到关键节点的最短距离
            key_nodes = torch.tensor([new_lightpath_nodes[0], new_lightpath_nodes[1], 
                                    target_lightpath_nodes[0], target_lightpath_nodes[1]], 
                                   device=device)
            
            # 简化距离计算（可以用更复杂的最短路径算法）
            distances = torch.abs(key_nodes.float() - node_idx)
            min_distance = torch.min(distances).long()
            
            # 编码特征
            encoded_new = self.physics_encoder(new_lightpath_feat)
            encoded_target = self.physics_encoder(target_lightpath_feat)
            encoded_current = self.physics_encoder(current_node_feat)
            
            # 计算物理交互
            interaction_feat, distance_emb = self.compute_physical_interaction(
                new_lightpath_feat.unsqueeze(0), 
                target_lightpath_feat.unsqueeze(0),
                min_distance.unsqueeze(0)
            )
            
            # 构建输入特征
            interaction_input = torch.cat([
                encoded_new, encoded_target, encoded_current, 
                distance_emb.squeeze(0)
            ], dim=0)
            
            # 相关性评分
            score = self.interaction_network(interaction_input.unsqueeze(0))
            relevance_scores.append(score)
        
        return torch.cat(relevance_scores, dim=0)

class AdaptiveSubgraphBuilder(nn.Module):
    """自适应子图构建器"""
    
    def __init__(self, relevance_scorer, min_subgraph_size=6, max_subgraph_size=15):
        super(AdaptiveSubgraphBuilder, self).__init__()
        self.relevance_scorer = relevance_scorer
        self.min_size = min_subgraph_size
        self.max_size = max_subgraph_size
        
        # 可学习的阈值参数
        self.relevance_threshold = nn.Parameter(torch.tensor(0.3))
        
    def forward(self, full_graph, node_features, new_lightpath_nodes, 
                target_lightpath_nodes, adaptive_size=True):
        """
        自适应构建子图
        
        Args:
            adaptive_size: 是否使用自适应大小（根据相关性分数分布决定）
        """
        num_nodes = full_graph.num_nodes()
        all_nodes = list(range(num_nodes))
        
        # 必须包含的关键节点
        key_nodes = set([new_lightpath_nodes[0], new_lightpath_nodes[1],
                        target_lightpath_nodes[0], target_lightpath_nodes[1]])
        
        # 候选节点（排除关键节点）
        candidate_nodes = [n for n in all_nodes if n not in key_nodes]
        
        if not candidate_nodes:
            selected_nodes = list(key_nodes)
        else:
            # 计算相关性分数
            relevance_scores = self.relevance_scorer(
                full_graph, node_features, new_lightpath_nodes,
                target_lightpath_nodes, candidate_nodes
            )
            
            if adaptive_size:
                # 自适应确定子图大小
                high_relevance_count = (relevance_scores > self.relevance_threshold).sum().item()
                target_size = min(max(high_relevance_count + len(key_nodes), self.min_size), 
                                self.max_size)
            else:
                target_size = self.max_size
            
            # 选择额外节点数量
            num_additional = max(0, target_size - len(key_nodes))
            
            if num_additional > 0:
                # 选择top-k相关节点
                _, top_indices = torch.topk(relevance_scores.squeeze(), 
                                          k=min(num_additional, len(candidate_nodes)))
                additional_nodes = [candidate_nodes[i] for i in top_indices.cpu().tolist()]
            else:
                additional_nodes = []
            
            selected_nodes = list(key_nodes) + additional_nodes
        
        # 构建子图
        subgraph = dgl.node_subgraph(full_graph, selected_nodes)
        
        return {
            'selected_nodes': selected_nodes,
            'subgraph': subgraph,
            'relevance_scores': relevance_scores if 'relevance_scores' in locals() else None,
            'subgraph_size': len(selected_nodes),
            'adaptive_threshold': self.relevance_threshold.item()
        }

class MultiScaleGATLayer(nn.Module):
    """多尺度注意力GAT层"""
    
    def __init__(self, in_dim, out_dim, num_heads=8, dropout=0.1):
        super(MultiScaleGATLayer, self).__init__()
        
        # 多个不同尺度的注意力头
        self.local_attention = GATConv(in_dim, out_dim // 2, num_heads // 2, 
                                     dropout, dropout, allow_zero_in_degree=True)
        self.global_attention = GATConv(in_dim, out_dim // 2, num_heads // 2,
                                      dropout, dropout, allow_zero_in_degree=True)
        
        # 尺度融合网络
        self.scale_fusion = nn.Sequential(
            nn.Linear(out_dim, out_dim),
            nn.LayerNorm(out_dim),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5)
        )
        
        # 残差连接的投影层
        self.residual_proj = nn.Linear(in_dim, out_dim) if in_dim != out_dim else nn.Identity()
        
    def forward(self, g, h):
        # 局部注意力（关注直接邻居）
        h_local = self.local_attention(g, h).flatten(1)
        
        # 全局注意力（考虑更远的节点，通过添加自环实现）
        g_global = dgl.add_self_loop(g)
        h_global = self.global_attention(g_global, h).flatten(1)
        
        # 多尺度融合
        h_combined = torch.cat([h_local, h_global], dim=1)
        h_fused = self.scale_fusion(h_combined)
        
        # 残差连接
        h_residual = self.residual_proj(h)
        
        return h_fused + h_residual

class IntelligentSubgraphGAT(nn.Module):
    """智能子图GAT - 整体架构"""
    
    def __init__(self, node_feature_dim, hidden_dim=128, num_layers=3, 
                 num_heads=8, num_classes=2, dropout=0.15):
        super(IntelligentSubgraphGAT, self).__init__()
        
        # 相关性评分器
        self.relevance_scorer = PhysicsAwareRelevanceScorer(node_feature_dim, hidden_dim)
        
        # 自适应子图构建器
        self.subgraph_builder = AdaptiveSubgraphBuilder(self.relevance_scorer)
        
        # 多尺度GAT层
        self.gat_layers = nn.ModuleList()
        
        # 输入层
        self.gat_layers.append(MultiScaleGATLayer(node_feature_dim, hidden_dim, num_heads, dropout))
        
        # 隐藏层
        for _ in range(num_layers - 1):
            self.gat_layers.append(MultiScaleGATLayer(hidden_dim, hidden_dim, num_heads, dropout))
        
        # 图级别表示学习
        self.graph_pooling = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.LayerNorm(hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
        )
        
        # 多任务预测头
        self.impact_classifier = nn.Sequential(
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(hidden_dim // 4, num_classes)  # 是否受影响分类
        )
        
        self.qot_regressor = nn.Sequential(
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(hidden_dim // 4, 1)  # QoT降级值回归
        )
        
    def forward(self, full_graph, full_node_features, new_lightpath_nodes, 
                target_lightpath_nodes, return_subgraph_info=False):
        """
        前向传播
        
        Returns:
            impact_logits: 影响分类结果
            qot_prediction: QoT降级预测
            subgraph_info: 子图构建信息（可选）
        """
        # 1. 自适应子图构建
        subgraph_result = self.subgraph_builder(
            full_graph, full_node_features, new_lightpath_nodes, target_lightpath_nodes
        )
        
        subgraph = subgraph_result['subgraph']
        selected_nodes = subgraph_result['selected_nodes']
        
        # 2. 获取子图特征
        subgraph_features = full_node_features[selected_nodes]
        
        # 3. 多尺度GAT处理
        h = subgraph_features
        for gat_layer in self.gat_layers:
            h = gat_layer(subgraph, h)
        
        # 4. 图级别池化（结合多种策略）
        # 平均池化
        h_mean = torch.mean(h, dim=0)
        # 最大池化
        h_max, _ = torch.max(h, dim=0)
        # 注意力加权池化
        attention_weights = F.softmax(torch.sum(h, dim=1), dim=0)
        h_att = torch.sum(h * attention_weights.unsqueeze(1), dim=0)
        
        # 融合不同池化结果
        graph_repr = (h_mean + h_max + h_att) / 3
        graph_repr = self.graph_pooling(graph_repr.unsqueeze(0))
        
        # 5. 多任务预测
        impact_logits = self.impact_classifier(graph_repr)
        qot_prediction = self.qot_regressor(graph_repr)
        
        if return_subgraph_info:
            return impact_logits, qot_prediction, subgraph_result
        else:
            return impact_logits, qot_prediction

class IntelligentQoTDataGenerator:
    """智能QoT数据生成器 - 生成更真实的物理数据"""
    
    def __init__(self, network_size=14):
        self.network_size = network_size
        self.create_realistic_network()
        
    def create_realistic_network(self):
        """创建更真实的网络拓扑"""
        # 基于真实日本网络的改进版本
        u = torch.tensor([0,0,1,2,3,3,4,4,4,5,6,6,7,8,11,9,8,13,11,11,13,10])
        v = torch.tensor([1,2,3,4,4,5,5,8,6,7,7,11,9,11,9,10,13,11,10,12,12,12])
        
        self.full_graph = dgl.graph((u, v))
        self.full_graph = dgl.to_bidirected(self.full_graph)
        
        # 添加更多连接以提高连通性
        additional_edges = [(1, 4), (2, 5), (6, 9), (7, 10), (8, 12)]
        for src, dst in additional_edges:
            if src < self.network_size and dst < self.network_size:
                self.full_graph.add_edges([src, dst], [dst, src])
        
        print(f"🌐 创建真实网络: {self.full_graph.num_nodes()}节点, {self.full_graph.num_edges()}边")
        
    def generate_enhanced_scenarios(self, num_scenarios=5000):
        """生成增强的训练场景"""
        print(f"🔄 生成 {num_scenarios} 个增强学习场景...")
        
        scenarios = []
        
        for i in range(num_scenarios):
            if i % 500 == 0:
                print(f"   进度: {i}/{num_scenarios}")
            
            # 生成多样化的光路对
            scenario = self._generate_single_scenario()
            scenarios.append(scenario)
        
        print(f"✅ 场景生成完成! 总数: {len(scenarios)}")
        
        # 统计标签分布
        impact_count = sum(1 for s in scenarios if s['impact_label'] == 1)
        print(f"   有影响: {impact_count} ({impact_count/len(scenarios)*100:.1f}%)")
        print(f"   无影响: {len(scenarios)-impact_count} ({(len(scenarios)-impact_count)/len(scenarios)*100:.1f}%)")
        
        # 统计QoT分布
        qot_values = [s['qot_degradation'] for s in scenarios]
        print(f"   QoT降级 - 均值: {np.mean(qot_values):.3f}, 标准差: {np.std(qot_values):.3f}")
        
        return scenarios
    
    def _generate_single_scenario(self):
        """生成单个训练场景"""
        nodes = list(range(self.network_size))
        
        # 选择光路节点
        new_src = np.random.choice(nodes)
        new_dst = np.random.choice([n for n in nodes if n != new_src])
        target_src = np.random.choice(nodes)
        target_dst = np.random.choice([n for n in nodes if n != target_src])
        
        # 生成丰富的节点特征
        node_features = self._generate_physics_based_features()
        
        # 生成ground truth（基于复杂的物理模型）
        impact_label, qot_degradation = self._generate_physics_based_labels(
            new_src, new_dst, target_src, target_dst, node_features
        )
        
        return {
            'full_graph': self.full_graph,
            'node_features': node_features,
            'new_lightpath_nodes': [new_src, new_dst],
            'target_lightpath_nodes': [target_src, target_dst],
            'impact_label': impact_label,
            'qot_degradation': qot_degradation
        }
    
    def _generate_physics_based_features(self):
        """基于物理原理生成节点特征"""
        features = []
        
        for i in range(self.network_size):
            # 10维丰富特征
            power_dbm = np.random.uniform(-5, 5)  # 发射功率
            load_ratio = np.random.beta(2, 5)     # 节点负载（beta分布更真实）
            wavelength_idx = np.random.uniform(0, 1)  # 归一化波长索引
            degree_centrality = self.full_graph.in_degrees()[i].item() / max(self.full_graph.in_degrees())
            
            # 地理坐标（模拟真实地理分布）
            geo_x = 0.1 * i + np.random.normal(0, 0.05)
            geo_y = 0.1 * (i % 4) + np.random.normal(0, 0.05)
            
            # 物理特性
            fiber_nonlinearity = np.random.uniform(0.8, 1.2)  # 光纤非线性系数
            amplifier_noise = np.random.exponential(0.1)      # 放大器噪声
            dispersion_param = np.random.normal(17, 2)        # 色散参数
            capacity_util = np.random.uniform(0.3, 0.9)       # 容量利用率
            
            features.append([
                power_dbm, load_ratio, wavelength_idx, degree_centrality,
                geo_x, geo_y, fiber_nonlinearity, amplifier_noise,
                dispersion_param, capacity_util
            ])
        
        return torch.tensor(features, dtype=torch.float32)
    
    def _generate_physics_based_labels(self, new_src, new_dst, target_src, target_dst, node_features):
        """基于物理原理生成标签"""
        
        # 计算路径特征
        try:
            import networkx as nx
            nx_graph = dgl.to_networkx(self.full_graph).to_undirected()
            
            new_path = nx.shortest_path(nx_graph, new_src, new_dst)
            target_path = nx.shortest_path(nx_graph, target_src, target_dst)
        except:
            new_path = [new_src, new_dst]
            target_path = [target_src, target_dst]
        
        # 物理影响建模
        
        # 1. 路径重叠影响
        new_edges = set((min(new_path[i], new_path[i+1]), max(new_path[i], new_path[i+1])) 
                       for i in range(len(new_path)-1))
        target_edges = set((min(target_path[i], target_path[i+1]), max(target_path[i], target_path[i+1])) 
                          for i in range(len(target_path)-1))
        
        path_overlap_ratio = len(new_edges.intersection(target_edges)) / max(len(new_edges.union(target_edges)), 1)
        
        # 2. 功率和波长相关性
        new_power = (node_features[new_src][0] + node_features[new_dst][0]) / 2
        target_power = (node_features[target_src][0] + node_features[target_dst][0]) / 2
        new_wavelength = (node_features[new_src][2] + node_features[new_dst][2]) / 2
        target_wavelength = (node_features[target_src][2] + node_features[target_dst][2]) / 2
        
        power_interaction = torch.exp(-torch.abs(new_power - target_power) * 0.2)
        wavelength_interaction = torch.exp(-torch.abs(new_wavelength - target_wavelength) * 3.0)
        
        # 3. 地理距离影响
        new_geo = (node_features[new_src][4:6] + node_features[new_dst][4:6]) / 2
        target_geo = (node_features[target_src][4:6] + node_features[target_dst][4:6]) / 2
        geo_distance = torch.sqrt(torch.sum((new_geo - target_geo)**2))
        geo_impact = torch.exp(-geo_distance * 2.0)
        
        # 4. 非线性效应
        nonlinearity_factor = (node_features[new_src][6] * node_features[target_src][6]).item()
        
        # 5. 放大器噪声交互
        noise_interaction = (node_features[new_src][7] + node_features[target_src][7]).item()
        
        # 综合物理影响分数
        physical_impact = (
            path_overlap_ratio * 0.35 +
            power_interaction.item() * 0.2 +
            wavelength_interaction.item() * 0.2 +
            geo_impact.item() * 0.15 +
            nonlinearity_factor * 0.05 +
            noise_interaction * 0.05
        )
        
        # 添加随机性模拟测量不确定性
        noise = np.random.normal(0, 0.08)
        final_impact = physical_impact + noise
        
        # 生成标签
        impact_threshold = 0.4
        impact_label = 1 if final_impact > impact_threshold else 0
        
        # QoT降级（dB）
        if impact_label == 1:
            qot_degradation = final_impact * np.random.uniform(0.5, 2.5)  # 0.5-2.5 dB degradation
        else:
            qot_degradation = max(0, np.random.exponential(0.1))  # minimal degradation
        
        return impact_label, float(qot_degradation)

def train_intelligent_subgraph_system():
    """训练智能子图系统"""
    print("🧠 智能子图QoT系统训练")
    print("=" * 60)
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🔧 使用设备: {device}")
    
    # 生成数据
    print("\n📊 生成训练数据...")
    data_generator = IntelligentQoTDataGenerator(network_size=14)
    scenarios = data_generator.generate_enhanced_scenarios(num_scenarios=4000)
    
    # 数据划分
    train_scenarios, test_scenarios = train_test_split(
        scenarios, test_size=0.25, random_state=42,
        stratify=[s['impact_label'] for s in scenarios]
    )
    
    print(f"📊 数据划分: 训练{len(train_scenarios)}, 测试{len(test_scenarios)}")
    
    # 创建模型
    model = IntelligentSubgraphGAT(
        node_feature_dim=10,  # 丰富的10维特征
        hidden_dim=128,
        num_layers=3,
        num_heads=8,
        num_classes=2,
        dropout=0.15
    ).to(device)
    
    print(f"🧠 模型参数: {sum(p.numel() for p in model.parameters()):,}")
    
    # 多任务训练设置
    optimizer = torch.optim.AdamW(model.parameters(), lr=0.001, weight_decay=1e-4)
    classification_criterion = nn.CrossEntropyLoss()
    regression_criterion = nn.MSELoss()
    
    # 学习率调度器
    scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer, T_0=20, T_mult=2, eta_min=1e-6
    )
    
    # 训练循环
    epochs = 100
    best_combined_score = 0
    train_history = {
        'classification_loss': [], 'regression_loss': [], 'total_loss': [],
        'classification_acc': [], 'regression_r2': []
    }
    
    print(f"\n🚀 开始多任务训练...")
    
    for epoch in range(epochs):
        model.train()
        total_cls_loss = 0
        total_reg_loss = 0
        cls_correct = 0
        reg_predictions = []
        reg_targets = []
        
        start_time = time.time()
        
        for scenario in train_scenarios:
            full_graph = scenario['full_graph'].to(device)
            node_features = scenario['node_features'].to(device)
            new_lightpath_nodes = scenario['new_lightpath_nodes']
            target_lightpath_nodes = scenario['target_lightpath_nodes']
            impact_label = torch.tensor([scenario['impact_label']], dtype=torch.long).to(device)
            qot_target = torch.tensor([scenario['qot_degradation']], dtype=torch.float).to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            impact_logits, qot_pred = model(
                full_graph, node_features, new_lightpath_nodes, target_lightpath_nodes
            )
            
            # 多任务损失
            cls_loss = classification_criterion(impact_logits, impact_label)
            reg_loss = regression_criterion(qot_pred, qot_target)
            
            # 加权组合损失
            total_loss = cls_loss + 0.5 * reg_loss
            
            total_loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            total_cls_loss += cls_loss.item()
            total_reg_loss += reg_loss.item()
            
            # 统计准确率
            cls_pred = torch.argmax(impact_logits, dim=1)
            cls_correct += (cls_pred == impact_label).sum().item()
            
            reg_predictions.append(qot_pred.item())
            reg_targets.append(qot_target.item())
        
        scheduler.step()
        
        # 计算指标
        avg_cls_loss = total_cls_loss / len(train_scenarios)
        avg_reg_loss = total_reg_loss / len(train_scenarios)
        cls_accuracy = cls_correct / len(train_scenarios)
        reg_r2 = r2_score(reg_targets, reg_predictions)
        
        train_history['classification_loss'].append(avg_cls_loss)
        train_history['regression_loss'].append(avg_reg_loss)
        train_history['total_loss'].append(avg_cls_loss + 0.5 * avg_reg_loss)
        train_history['classification_acc'].append(cls_accuracy)
        train_history['regression_r2'].append(reg_r2)
        
        epoch_time = time.time() - start_time
        
        # 测试评估
        if epoch % 10 == 0:
            test_results = evaluate_intelligent_model(model, test_scenarios, device)
            
            combined_score = test_results['classification_acc'] * 0.6 + test_results['regression_r2'] * 0.4
            
            print(f"Epoch {epoch:3d}: Cls Loss={avg_cls_loss:.4f}, Reg Loss={avg_reg_loss:.4f}")
            print(f"         Cls Acc={cls_accuracy:.4f}, Reg R²={reg_r2:.4f}")
            print(f"         Test - Cls Acc={test_results['classification_acc']:.4f}, "
                  f"Reg R²={test_results['regression_r2']:.4f}")
            print(f"         Combined Score={combined_score:.4f}, Time={epoch_time:.2f}s")
            
            if combined_score > best_combined_score:
                best_combined_score = combined_score
                torch.save(model.state_dict(), 'intelligent_subgraph_qot_best.pth')
    
    # 最终测试评估
    print(f"\n🧪 最终测试评估...")
    model.load_state_dict(torch.load('intelligent_subgraph_qot_best.pth'))
    
    final_results = evaluate_intelligent_model_detailed(model, test_scenarios, device)
    
    print(f"✅ 最终测试结果:")
    print(f"   分类准确率: {final_results['classification_acc']:.4f}")
    print(f"   分类精确率: {final_results['classification_precision']:.4f}")
    print(f"   分类召回率: {final_results['classification_recall']:.4f}")
    print(f"   回归R²: {final_results['regression_r2']:.4f}")
    print(f"   回归RMSE: {final_results['regression_rmse']:.4f}")
    print(f"   平均子图大小: {final_results['avg_subgraph_size']:.1f}")
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results = {
        'experiment_info': {
            'timestamp': timestamp,
            'model_type': 'IntelligentSubgraphQoT',
            'train_samples': len(train_scenarios),
            'test_samples': len(test_scenarios),
            'node_features': 10,
            'model_parameters': sum(p.numel() for p in model.parameters())
        },
        'final_results': final_results,
        'training_history': train_history
    }
    
    results_file = f'intelligent_subgraph_qot_results_{timestamp}.json'
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📁 结果保存至: {results_file}")
    
    return results

def evaluate_intelligent_model(model, test_scenarios, device):
    """简单模型评估"""
    model.eval()
    cls_correct = 0
    reg_predictions = []
    reg_targets = []
    
    with torch.no_grad():
        for scenario in test_scenarios:
            full_graph = scenario['full_graph'].to(device)
            node_features = scenario['node_features'].to(device)
            new_lightpath_nodes = scenario['new_lightpath_nodes']
            target_lightpath_nodes = scenario['target_lightpath_nodes']
            impact_label = scenario['impact_label']
            qot_target = scenario['qot_degradation']
            
            impact_logits, qot_pred = model(
                full_graph, node_features, new_lightpath_nodes, target_lightpath_nodes
            )
            
            cls_pred = torch.argmax(impact_logits, dim=1).cpu().item()
            cls_correct += (cls_pred == impact_label)
            
            reg_predictions.append(qot_pred.cpu().item())
            reg_targets.append(qot_target)
    
    cls_accuracy = cls_correct / len(test_scenarios)
    reg_r2 = r2_score(reg_targets, reg_predictions)
    
    return {
        'classification_acc': cls_accuracy,
        'regression_r2': reg_r2
    }

def evaluate_intelligent_model_detailed(model, test_scenarios, device):
    """详细模型评估"""
    model.eval()
    cls_predictions = []
    cls_targets = []
    reg_predictions = []
    reg_targets = []
    subgraph_sizes = []
    
    with torch.no_grad():
        for scenario in test_scenarios:
            full_graph = scenario['full_graph'].to(device)
            node_features = scenario['node_features'].to(device)
            new_lightpath_nodes = scenario['new_lightpath_nodes']
            target_lightpath_nodes = scenario['target_lightpath_nodes']
            
            impact_logits, qot_pred, subgraph_info = model(
                full_graph, node_features, new_lightpath_nodes, target_lightpath_nodes,
                return_subgraph_info=True
            )
            
            cls_pred = torch.argmax(impact_logits, dim=1).cpu().item()
            cls_predictions.append(cls_pred)
            cls_targets.append(scenario['impact_label'])
            
            reg_predictions.append(qot_pred.cpu().item())
            reg_targets.append(scenario['qot_degradation'])
            
            subgraph_sizes.append(subgraph_info['subgraph_size'])
    
    # 计算指标
    cls_accuracy = accuracy_score(cls_targets, cls_predictions)
    cls_precision, cls_recall, cls_f1, _ = precision_recall_fscore_support(
        cls_targets, cls_predictions, average='weighted'
    )
    
    reg_r2 = r2_score(reg_targets, reg_predictions)
    reg_rmse = np.sqrt(mean_squared_error(reg_targets, reg_predictions))
    
    return {
        'classification_acc': cls_accuracy,
        'classification_precision': cls_precision,
        'classification_recall': cls_recall,
        'classification_f1': cls_f1,
        'regression_r2': reg_r2,
        'regression_rmse': reg_rmse,
        'avg_subgraph_size': np.mean(subgraph_sizes),
        'subgraph_size_std': np.std(subgraph_sizes)
    }

if __name__ == "__main__":
    results = train_intelligent_subgraph_system()