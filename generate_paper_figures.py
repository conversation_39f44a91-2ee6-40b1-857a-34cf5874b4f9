#!/usr/bin/env python3
"""
为学术论文生成架构图和结果图表
基于真实实验结果
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
import networkx as nx
from matplotlib.patches import FancyBboxPatch
import json

def create_system_architecture_figure():
    """生成系统架构图"""
    fig, ax = plt.subplots(1, 1, figsize=(12, 8))
    
    # 设置matplotlib中文字体
    plt.rcParams['font.size'] = 10
    plt.rcParams['axes.titlesize'] = 12
    plt.rcParams['axes.labelsize'] = 11
    
    # 清除坐标轴
    ax.set_xlim(0, 10)
    ax.set_ylim(0, 8)
    ax.axis('off')
    
    # 1. 输入：光网络拓扑
    network_box = FancyBboxPatch((0.5, 6), 2, 1.2, 
                                boxstyle="round,pad=0.1", 
                                facecolor='lightblue', 
                                edgecolor='black', linewidth=2)
    ax.add_patch(network_box)
    ax.text(1.5, 6.6, 'Optical Network\n(14-node Japanese)', 
            ha='center', va='center', fontweight='bold', fontsize=9)
    
    # 2. 子图构建
    subgraph_box = FancyBboxPatch((3.5, 6), 2.5, 1.2,
                                 boxstyle="round,pad=0.1",
                                 facecolor='lightgreen',
                                 edgecolor='black', linewidth=2)
    ax.add_patch(subgraph_box)
    ax.text(4.75, 6.6, 'Subgraph Construction\n(6-10 nodes)', 
            ha='center', va='center', fontweight='bold', fontsize=9)
    
    # 3. GAT处理
    gat_box = FancyBboxPatch((7, 6), 2.5, 1.2,
                            boxstyle="round,pad=0.1",
                            facecolor='lightyellow',
                            edgecolor='black', linewidth=2)
    ax.add_patch(gat_box)
    ax.text(8.25, 6.6, 'Multi-head GAT\n(4 heads, 2 layers)', 
            ha='center', va='center', fontweight='bold', fontsize=9)
    
    # 4. 特征工程
    feature_box = FancyBboxPatch((1, 4), 3, 1,
                                boxstyle="round,pad=0.1",
                                facecolor='lightcoral',
                                edgecolor='black', linewidth=2)
    ax.add_patch(feature_box)
    ax.text(2.5, 4.5, '7-D Node Features\n(Path indicators, Power, Degree)', 
            ha='center', va='center', fontweight='bold', fontsize=9)
    
    # 5. 图级池化
    pooling_box = FancyBboxPatch((5.5, 4), 2, 1,
                                boxstyle="round,pad=0.1",
                                facecolor='lightsteelblue',
                                edgecolor='black', linewidth=2)
    ax.add_patch(pooling_box)
    ax.text(6.5, 4.5, 'Graph-level\nMean Pooling', 
            ha='center', va='center', fontweight='bold', fontsize=9)
    
    # 6. 分类器
    classifier_box = FancyBboxPatch((3.5, 2), 3, 1,
                                   boxstyle="round,pad=0.1",
                                   facecolor='plum',
                                   edgecolor='black', linewidth=2)
    ax.add_patch(classifier_box)
    ax.text(5, 2.5, 'Binary Classifier\n(128→32→2)', 
            ha='center', va='center', fontweight='bold', fontsize=9)
    
    # 7. 输出
    output_box = FancyBboxPatch((4, 0.2), 2, 0.8,
                               boxstyle="round,pad=0.1",
                               facecolor='gold',
                               edgecolor='black', linewidth=2)
    ax.add_patch(output_box)
    ax.text(5, 0.6, 'Interference\nPrediction', 
            ha='center', va='center', fontweight='bold', fontsize=9)
    
    # 添加箭头
    arrows = [
        # 网络→子图构建
        [(2.5, 6.6), (3.5, 6.6)],
        # 子图构建→GAT
        [(6, 6.6), (7, 6.6)],
        # 网络→特征工程
        [(1.5, 6), (2.5, 5)],
        # 特征→池化
        [(4, 4.5), (5.5, 4.5)],
        # GAT→池化
        [(8.25, 6), (6.5, 5)],
        # 池化→分类器
        [(6.5, 4), (5, 3)],
        # 分类器→输出
        [(5, 2), (5, 1)]
    ]
    
    for start, end in arrows:
        ax.annotate('', xy=end, xytext=start,
                   arrowprops=dict(arrowstyle='->', lw=2, color='black'))
    
    ax.set_title('Subgraph GAT Architecture for Lightpath Interference Identification', 
                fontsize=14, fontweight='bold', pad=20)
    
    plt.tight_layout()
    plt.savefig('figure1_system_architecture.png', dpi=300, bbox_inches='tight')
    plt.savefig('figure1_system_architecture.pdf', bbox_inches='tight')
    plt.close()
    
    print("✅ 系统架构图已生成: figure1_system_architecture.png/pdf")

def create_subgraph_example_figure():
    """生成子图示例图"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
    
    # 左图：完整网络
    G_full = nx.Graph()
    # 14节点日本网络拓扑
    edges = [(0,1), (0,2), (1,3), (2,4), (3,4), (3,5), (4,5), (4,8), (4,6),
             (5,7), (6,7), (6,11), (7,9), (8,11), (11,9), (9,10), (8,13),
             (11,13), (11,10), (11,12), (13,12), (10,12)]
    
    G_full.add_edges_from(edges)
    
    # 设置节点位置（手动调整为更好的布局）
    pos_full = {
        0: (0, 4), 1: (1, 5), 2: (1, 3), 3: (2, 4), 4: (3, 3),
        5: (3, 5), 6: (4, 2), 7: (4, 4), 8: (5, 1), 9: (5, 3),
        10: (6, 2), 11: (6, 1), 12: (7, 1), 13: (7, 0)
    }
    
    # 绘制完整网络
    nx.draw_networkx_nodes(G_full, pos_full, ax=ax1, node_color='lightblue', 
                          node_size=500, alpha=0.8)
    nx.draw_networkx_edges(G_full, pos_full, ax=ax1, edge_color='gray', 
                          width=1, alpha=0.6)
    nx.draw_networkx_labels(G_full, pos_full, ax=ax1, font_size=8, font_weight='bold')
    
    # 高亮新光路和目标光路
    new_path = [0, 1, 3, 4]  # 示例新光路
    target_path = [2, 4, 5, 7]  # 示例目标光路
    
    # 绘制路径
    path_edges_new = [(new_path[i], new_path[i+1]) for i in range(len(new_path)-1)]
    path_edges_target = [(target_path[i], target_path[i+1]) for i in range(len(target_path)-1)]
    
    nx.draw_networkx_edges(G_full, pos_full, edgelist=path_edges_new, 
                          edge_color='red', width=3, ax=ax1, label='New Lightpath')
    nx.draw_networkx_edges(G_full, pos_full, edgelist=path_edges_target, 
                          edge_color='blue', width=3, ax=ax1, label='Target Lightpath')
    
    ax1.set_title('(a) Full Network Graph\n(14 nodes, 22 edges)', fontweight='bold')
    ax1.legend(loc='upper right')
    ax1.axis('off')
    
    # 右图：提取的子图
    relevant_nodes = list(set(new_path + target_path + [6, 9, 8]))  # 添加邻居节点
    G_sub = G_full.subgraph(relevant_nodes)
    
    # 重新映射位置
    pos_sub = {node: pos_full[node] for node in relevant_nodes}
    
    # 节点颜色编码
    node_colors = []
    for node in relevant_nodes:
        if node in new_path[:1]:  # 新光路源
            node_colors.append('red')
        elif node in new_path[-1:]:  # 新光路目标
            node_colors.append('darkred')
        elif node in target_path[:1]:  # 目标光路源
            node_colors.append('blue')
        elif node in target_path[-1:]:  # 目标光路目标
            node_colors.append('darkblue')
        elif node in (new_path + target_path):  # 路径上的节点
            node_colors.append('purple')
        else:  # 邻居节点
            node_colors.append('lightgray')
    
    nx.draw_networkx_nodes(G_sub, pos_sub, ax=ax2, node_color=node_colors, 
                          node_size=600, alpha=0.8)
    nx.draw_networkx_edges(G_sub, pos_sub, ax=ax2, edge_color='gray', 
                          width=1.5, alpha=0.7)
    nx.draw_networkx_labels(G_sub, pos_sub, ax=ax2, font_size=9, font_weight='bold')
    
    # 添加图例
    legend_elements = [
        plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='red', 
                  markersize=10, label='New LP Source'),
        plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='darkred', 
                  markersize=10, label='New LP Dest'),
        plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='blue', 
                  markersize=10, label='Target LP Source'),
        plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='darkblue', 
                  markersize=10, label='Target LP Dest'),
        plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='purple', 
                  markersize=10, label='Path Nodes'),
        plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='lightgray', 
                  markersize=10, label='Neighbors')
    ]
    
    ax2.set_title(f'(b) Extracted Subgraph\n({len(relevant_nodes)} nodes)', fontweight='bold')
    ax2.legend(handles=legend_elements, loc='upper right', fontsize=8)
    ax2.axis('off')
    
    plt.tight_layout()
    plt.savefig('figure2_subgraph_example.png', dpi=300, bbox_inches='tight')
    plt.savefig('figure2_subgraph_example.pdf', bbox_inches='tight')
    plt.close()
    
    print("✅ 子图示例图已生成: figure2_subgraph_example.png/pdf")

def create_performance_results_figure():
    """生成性能结果对比图"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 10))
    
    # 真实实验结果数据
    methods = ['Subgraph GAT\n(Ours)', 'Full Graph\nGCN', 'Full Graph\nGAT']
    colors = ['#2ecc71', '#e74c3c', '#3498db']
    
    # 性能指标
    accuracies = [93.00, 68.67, 64.00]
    precisions = [92.98, 47.15, 58.16]
    recalls = [93.00, 68.67, 64.00]
    f1_scores = [92.99, 55.91, 59.43]
    
    # 1. 准确率对比
    bars1 = ax1.bar(methods, accuracies, color=colors, alpha=0.8)
    ax1.set_ylabel('Test Accuracy (%)')
    ax1.set_title('(a) Test Accuracy Comparison', fontweight='bold')
    ax1.set_ylim(40, 100)
    
    for bar, acc in zip(bars1, accuracies):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{acc:.2f}%', ha='center', va='bottom', fontweight='bold')
    
    # 2. 精确率对比
    bars2 = ax2.bar(methods, precisions, color=colors, alpha=0.8)
    ax2.set_ylabel('Precision (%)')
    ax2.set_title('(b) Precision Comparison', fontweight='bold')
    ax2.set_ylim(40, 100)
    
    for bar, prec in zip(bars2, precisions):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{prec:.2f}%', ha='center', va='bottom', fontweight='bold')
    
    # 3. 召回率对比
    bars3 = ax3.bar(methods, recalls, color=colors, alpha=0.8)
    ax3.set_ylabel('Recall (%)')
    ax3.set_title('(c) Recall Comparison', fontweight='bold')
    ax3.set_ylim(40, 100)
    
    for bar, recall in zip(bars3, recalls):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{recall:.2f}%', ha='center', va='bottom', fontweight='bold')
    
    # 4. F1分数对比
    bars4 = ax4.bar(methods, f1_scores, color=colors, alpha=0.8)
    ax4.set_ylabel('F1 Score (%)')
    ax4.set_title('(d) F1 Score Comparison', fontweight='bold')
    ax4.set_ylim(40, 100)
    
    for bar, f1 in zip(bars4, f1_scores):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{f1:.2f}%', ha='center', va='bottom', fontweight='bold')
    
    # 添加网格
    for ax in [ax1, ax2, ax3, ax4]:
        ax.grid(True, alpha=0.3)
        ax.tick_params(axis='x', rotation=0)
    
    plt.tight_layout()
    plt.savefig('figure4_performance_results.png', dpi=300, bbox_inches='tight')
    plt.savefig('figure4_performance_results.pdf', bbox_inches='tight')
    plt.close()
    
    print("✅ 性能结果图已生成: figure4_performance_results.png/pdf")

def create_complexity_comparison_figure():
    """生成复杂度对比图"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    methods = ['Subgraph GAT\n(Ours)', 'Full Graph\nGCN', 'Full Graph\nGAT']
    colors = ['#2ecc71', '#e74c3c', '#3498db']
    
    # 参数量对比
    parameters = [21986, 1282, 17666]
    bars1 = ax1.bar(methods, parameters, color=colors, alpha=0.8)
    ax1.set_ylabel('Model Parameters')
    ax1.set_title('(a) Model Complexity Comparison', fontweight='bold')
    
    for bar, param in zip(bars1, parameters):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 500,
                f'{param:,}', ha='center', va='bottom', fontweight='bold', fontsize=9)
    
    # 训练时间对比
    train_times = [35.57, 6.12, 35.04]
    bars2 = ax2.bar(methods, train_times, color=colors, alpha=0.8)
    ax2.set_ylabel('Training Time (seconds)')
    ax2.set_title('(b) Training Time Comparison', fontweight='bold')
    
    for bar, time in zip(bars2, train_times):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{time:.2f}s', ha='center', va='bottom', fontweight='bold')
    
    # 添加网格
    for ax in [ax1, ax2]:
        ax.grid(True, alpha=0.3)
        ax.tick_params(axis='x', rotation=0)
    
    plt.tight_layout()
    plt.savefig('figure3_complexity_comparison.png', dpi=300, bbox_inches='tight')
    plt.savefig('figure3_complexity_comparison.pdf', bbox_inches='tight')
    plt.close()
    
    print("✅ 复杂度对比图已生成: figure3_complexity_comparison.png/pdf")

def main():
    """生成所有论文图表"""
    print("🎨 生成论文图表...")
    print("=" * 50)
    
    # 生成所有图表
    create_system_architecture_figure()
    create_subgraph_example_figure()
    create_complexity_comparison_figure()
    create_performance_results_figure()
    
    print(f"\n✅ 所有图表生成完成!")
    print("📊 生成的图表文件:")
    print("   - figure1_system_architecture.png/pdf (系统架构图)")
    print("   - figure2_subgraph_example.png/pdf (子图示例图)")
    print("   - figure3_complexity_comparison.png/pdf (复杂度对比)")
    print("   - figure4_performance_results.png/pdf (性能结果)")

if __name__ == "__main__":
    main()