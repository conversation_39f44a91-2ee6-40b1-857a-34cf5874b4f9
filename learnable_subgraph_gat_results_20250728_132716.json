{"experiment_info": {"timestamp": "20250728_132716", "model_type": "LearnableSubgraphGAT", "task": "End-to-End Lightpath Impact Learning", "train_samples": 2250, "test_samples": 750, "model_parameters": 108260}, "results": {"final_test_accuracy": 0.5906666666666667, "best_accuracy": 0.5906666666666667, "precision": 0.3488871111111111, "recall": 0.5906666666666667, "f1_score": 0.4386677842972897}, "training_history": {"train_losses": [0.6807370363473892, 0.6781726060973273, 0.6780579253832499, 0.6775630703369776, 0.6775287102593316, 0.6773961693975661, 0.6775843297905392, 0.6774001611603631, 0.6779368166128794, 0.6772773574193318, 0.677375137037701, 0.6774353581004673, 0.6773197960986032, 0.6772395197418001, 0.6772991357909308, 0.6773855158090591, 0.6772776111629274, 0.6772512250608868, 0.6773445193105274, 0.6774251990848117, 0.6774326980643802, 0.677306555363867, 0.6772906711763805, 0.6773074648777644, 0.6773395203351974, 0.6768986893230015, 0.6773467387623258, 0.6775346344312032, 0.6782101724545161, 0.6773951729933421, 0.6776072692738639, 0.6773483238750034, 0.6773628575801849, 0.677621647503641, 0.6772307554615868, 0.6775274266931746, 0.6774659412172106, 0.6772738028764724, 0.6772419992023044, 0.6773120459053251, 0.6772397517098321, 0.6772505678335825, 0.677388996442159, 0.6773159943819046, 0.6773279769817988, 0.6772726723353069, 0.6774527677959866, 0.6768738667435116, 0.6773225804169972, 0.6774584144088958, 0.6766333986653222, 0.6772790171305338, 0.6780865606069565, 0.6777135422494677, 0.6776004545423719, 0.6779642898506588, 0.6772575684918297, 0.6780166545841428, 0.6774598145617379, 0.6774844823413425, 0.6773745602369309, 0.6772506216102177, 0.6773073738416036, 0.6772510660886765, 0.6772547797494465, 0.6769955512020323, 0.6774866649044885, 0.677549093524615, 0.6774454714854559, 0.6774041704336802, 0.6773147039016088, 0.6772558648056454, 0.6773334890471564, 0.6773668705887265, 0.6776084314584732, 0.6772999902963638, 0.6772627181079652, 0.6772585169606739, 0.6772799026436276, 0.6772730917533238], "train_accuracies": [0.584, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5888888888888889, 0.5906666666666667, 0.5897777777777777, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.592, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667, 0.5906666666666667]}}