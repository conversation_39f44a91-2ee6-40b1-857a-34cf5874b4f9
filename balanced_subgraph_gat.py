#!/usr/bin/env python3
"""
平衡子图GAT实验 - 解决数据不平衡问题
目标: 生成40-60%受影响比例的真实数据集
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import dgl
import numpy as np
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score
from tqdm import tqdm
import json
import time
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置随机种子
torch.manual_seed(42)
np.random.seed(42)

class BalancedNetworkTopology:
    """日本网络拓扑"""
    
    def __init__(self):
        self.num_nodes = 14
        self.node_positions = {
            0: (139.6917, 35.6895),   # Tokyo
            1: (135.5023, 34.6937),   # Osaka  
            2: (136.9066, 35.1815),   # Nagoya
            3: (130.4017, 33.5904),   # <PERSON><PERSON><PERSON>
            4: (131.4202, 31.9077),   # <PERSON><PERSON><PERSON>
            5: (140.1230, 36.3909),   # <PERSON><PERSON><PERSON><PERSON>
            6: (141.3625, 43.0642),   # <PERSON><PERSON><PERSON>
            7: (140.7400, 40.8244),   # <PERSON><PERSON><PERSON>
            8: (132.4553, 34.3853),   # Hiroshima
            9: (133.9250, 34.6851),   # <PERSON><PERSON><PERSON><PERSON>
            10: (135.8681, 35.2661),  # Kyoto
            11: (136.6256, 36.5951),  # <PERSON><PERSON><PERSON>
            12: (138.3831, 34.9756),  # <PERSON><PERSON><PERSON>
            13: (139.0238, 36.0014)   # <PERSON><PERSON><PERSON>
        }
        
        self.edges = [
            (0, 1), (0, 2), (0, 5), (0, 6), (0, 7), (0, 12), (0, 13),
            (1, 2), (1, 3), (1, 8), (1, 9), (1, 10),
            (2, 10), (2, 11), (2, 12), (2, 13),
            (3, 4), (3, 8),
            (5, 7), (5, 13),
            (6, 7),
            (7, 11),
            (8, 9),
            (9, 10),
            (10, 11),
            (11, 13),
            (12, 13)
        ]
        
    def create_graph(self):
        g = dgl.graph(self.edges, num_nodes=self.num_nodes)
        g = dgl.to_bidirected(g)
        return g
    
    def calculate_distance(self, node1, node2):
        lat1, lon1 = self.node_positions[node1]
        lat2, lon2 = self.node_positions[node2]
        lat_diff = lat1 - lat2
        lon_diff = lon1 - lon2
        distance = np.sqrt(lat_diff**2 + lon_diff**2) * 111  # 1度约111公里
        return distance

class BalancedDataGenerator:
    """平衡的数据生成器 - 确保40-60%受影响比例"""
    
    def __init__(self, topology):
        self.topology = topology
        self.graph = topology.create_graph()
        
    def generate_scenarios(self, num_scenarios=3000):
        """生成平衡的训练场景"""
        scenarios = []
        labels = []
        osnr_changes = []
        
        print(f"🔄 生成 {num_scenarios} 个平衡场景...")
        
        # 目标分布: 50% 受影响
        target_affected_ratio = 0.5
        
        for i in tqdm(range(num_scenarios)):
            # 生成节点特征
            node_features = self._generate_node_features(i)
            edge_features = self._generate_edge_features()
            
            # 随机选择光路
            num_nodes = self.topology.num_nodes
            new_src, new_dst = np.random.choice(num_nodes, 2, replace=False)
            target_src, target_dst = np.random.choice(num_nodes, 2, replace=False)
            
            # 计算影响 - 使用平衡策略
            current_ratio = len(labels) / max(1, i) if i > 0 else 0
            impact_result = self._calculate_balanced_impact(
                (new_src, new_dst), (target_src, target_dst), 
                node_features, current_ratio, target_affected_ratio
            )
            
            scenario = {
                'node_features': node_features.astype(np.float32),
                'edge_features': edge_features.astype(np.float32),
                'new_lightpath': [new_src, new_dst],
                'target_lightpath': [target_src, target_dst]
            }
            
            scenarios.append(scenario)
            labels.append(impact_result['is_affected'])
            osnr_changes.append(impact_result['osnr_change'])
            
            # 进度和平衡监控
            if (i + 1) % 500 == 0:
                affected_count = sum(labels)
                affected_ratio = affected_count / (i + 1)
                print(f"   进度: {i+1}/{num_scenarios}, 受影响: {affected_count} ({affected_ratio*100:.1f}%)")
        
        affected_count = sum(labels)
        print(f"✅ 场景生成完成! 总数: {len(scenarios)}")
        print(f"   受影响: {affected_count} ({affected_count/len(scenarios)*100:.1f}%)")
        print(f"   未受影响: {len(scenarios)-affected_count} ({(len(scenarios)-affected_count)/len(scenarios)*100:.1f}%)")
        
        return scenarios, labels, osnr_changes
    
    def _generate_node_features(self, scenario_id):
        """生成8维节点特征"""
        np.random.seed(scenario_id % 1000)  # 部分随机性
        
        num_nodes = self.graph.num_nodes()
        features = np.zeros((num_nodes, 8))
        
        for i in range(num_nodes):
            degree = self.graph.in_degrees(i)
            lat, lon = self.topology.node_positions[i]
            
            features[i, 0] = np.random.uniform(0.5, 2.0)  # 节点功率
            features[i, 1] = np.random.uniform(0.2, 0.8)  # 负载利用率
            features[i, 2] = degree / 8.0  # 归一化度数
            features[i, 3] = np.random.uniform(0.3, 0.9)  # 波长利用率
            features[i, 4] = (lon - 130.0) / 11.0  # 归一化经度
            features[i, 5] = (lat - 31.0) / 13.0   # 归一化纬度
            features[i, 6] = np.random.uniform(20, 100)  # 容量
            features[i, 7] = np.random.uniform(10, 30)   # 光路数量
        
        np.random.seed(None)
        return features
    
    def _generate_edge_features(self):
        """生成6维边特征"""
        edges = self.graph.edges()
        num_edges = len(edges[0])
        features = np.zeros((num_edges, 6))
        
        for i in range(num_edges):
            src, dst = edges[0][i].item(), edges[1][i].item()
            distance = self.topology.calculate_distance(src, dst)
            
            features[i, 0] = distance / 1000.0  # 归一化距离
            features[i, 1] = distance * 0.2  # 光纤损耗
            features[i, 2] = np.random.uniform(0.2, 0.8)  # 利用率
            features[i, 3] = np.random.randint(1, 10)     # 光路数量
            features[i, 4] = np.random.randint(0, 3)      # 波长冲突
            features[i, 5] = np.random.uniform(0.5, 2.0)  # 总功率
            
        return features
    
    def _calculate_balanced_impact(self, new_lightpath, target_lightpath, 
                                  node_features, current_ratio, target_ratio):
        """计算平衡的影响 - 动态调整阈值以维持目标比例"""
        
        new_src, new_dst = new_lightpath
        target_src, target_dst = target_lightpath
        
        # 1. 基础物理因子计算
        path_overlap = self._calculate_path_overlap(new_lightpath, target_lightpath)
        geo_distance = self._calculate_geo_distance(new_lightpath, target_lightpath)
        power_diff = self._calculate_power_difference(new_lightpath, target_lightpath, node_features)
        wavelength_conflict = self._calculate_wavelength_conflict(new_lightpath, target_lightpath, node_features)
        
        # 2. 综合影响分数 (0-1)
        impact_score = (
            path_overlap * 0.3 +           # 路径重叠权重
            (1 - geo_distance) * 0.25 +    # 地理邻近权重  
            power_diff * 0.2 +             # 功率差异权重
            wavelength_conflict * 0.25     # 波长冲突权重
        )
        
        # 3. 动态阈值调整 - 极大降低阈值以获得平衡
        if current_ratio < target_ratio - 0.05:
            # 当前受影响比例太低，极大降低阈值
            threshold = 0.02
        elif current_ratio > target_ratio + 0.05:
            # 当前受影响比例太高，提高阈值
            threshold = 0.4
        else:
            # 接近目标比例，使用低阈值
            threshold = 0.15
        
        # 4. 最终判断
        is_affected = impact_score > threshold
        
        # 5. OSNR变化计算
        if is_affected:
            osnr_change = impact_score * np.random.uniform(0.5, 2.0)
        else:
            osnr_change = max(0, np.random.normal(0.05, 0.1))
        
        return {
            'is_affected': int(is_affected),
            'osnr_change': float(osnr_change),
            'impact_score': float(impact_score),
            'threshold': float(threshold),
            'path_overlap': float(path_overlap),
            'geo_distance': float(geo_distance),
            'power_diff': float(power_diff),
            'wavelength_conflict': float(wavelength_conflict)
        }
    
    def _calculate_path_overlap(self, new_lightpath, target_lightpath):
        """计算路径重叠度"""
        new_src, new_dst = new_lightpath
        target_src, target_dst = target_lightpath
        
        new_nodes = set([new_src, new_dst])
        target_nodes = set([target_src, target_dst])
        
        intersection = len(new_nodes & target_nodes)
        union = len(new_nodes | target_nodes)
        
        return intersection / union if union > 0 else 0
    
    def _calculate_geo_distance(self, new_lightpath, target_lightpath):
        """计算地理距离 (归一化到0-1)"""
        new_src, new_dst = new_lightpath
        target_src, target_dst = target_lightpath
        
        # 计算光路中心点
        new_center_lat = (self.topology.node_positions[new_src][1] + self.topology.node_positions[new_dst][1]) / 2
        new_center_lon = (self.topology.node_positions[new_src][0] + self.topology.node_positions[new_dst][0]) / 2
        
        target_center_lat = (self.topology.node_positions[target_src][1] + self.topology.node_positions[target_dst][1]) / 2
        target_center_lon = (self.topology.node_positions[target_src][0] + self.topology.node_positions[target_dst][0]) / 2
        
        distance = np.sqrt((new_center_lat - target_center_lat)**2 + (new_center_lon - target_center_lon)**2)
        max_distance = 15.0  # 日本地理跨度
        
        return min(distance / max_distance, 1.0)
    
    def _calculate_power_difference(self, new_lightpath, target_lightpath, node_features):
        """计算功率差异"""
        new_src, new_dst = new_lightpath
        target_src, target_dst = target_lightpath
        
        new_power = (node_features[new_src, 0] + node_features[new_dst, 0]) / 2
        target_power = (node_features[target_src, 0] + node_features[target_dst, 0]) / 2
        
        power_diff = abs(new_power - target_power)
        return min(power_diff / 1.5, 1.0)  # 归一化
    
    def _calculate_wavelength_conflict(self, new_lightpath, target_lightpath, node_features):
        """计算波长冲突概率"""
        new_src, new_dst = new_lightpath
        target_src, target_dst = target_lightpath
        
        new_util = (node_features[new_src, 3] + node_features[new_dst, 3]) / 2
        target_util = (node_features[target_src, 3] + node_features[target_dst, 3]) / 2
        
        # 利用率越高，冲突概率越大
        conflict_prob = (new_util + target_util) / 2
        return min(conflict_prob, 1.0)

class SimpleSubgraphGAT(nn.Module):
    """简化的子图GAT模型 - 专注于分类任务"""
    
    def __init__(self, node_feature_dim=8, edge_feature_dim=6, hidden_dim=64, num_classes=2):
        super().__init__()
        
        # 子图选择器 - 简化版本
        self.relevance_scorer = nn.Sequential(
            nn.Linear(node_feature_dim * 3, hidden_dim),  # 新光路+目标光路+当前节点特征
            nn.ReLU(),
            nn.Linear(hidden_dim, 1),
            nn.Sigmoid()
        )
        
        # GAT层
        self.gat1 = dgl.nn.GATConv(node_feature_dim, hidden_dim//2, num_heads=2, 
                                  feat_drop=0.1, attn_drop=0.1, allow_zero_in_degree=True)
        self.gat2 = dgl.nn.GATConv(hidden_dim, hidden_dim//2, num_heads=2,
                                  feat_drop=0.1, attn_drop=0.1, allow_zero_in_degree=True)
        
        # 分类器
        self.classifier = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),  # 平均+最大池化
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, num_classes)
        )
        
    def forward(self, graph, node_features, new_lightpath, target_lightpath):
        batch_size = node_features.shape[0]
        device = node_features.device
        
        # 简化的子图选择
        subgraph_outputs = []
        
        for i in range(batch_size):
            # 获取关键节点特征
            new_src, new_dst = new_lightpath[i]
            target_src, target_dst = target_lightpath[i]
            
            key_features = torch.cat([
                node_features[i, new_src] + node_features[i, new_dst],
                node_features[i, target_src] + node_features[i, target_dst]
            ])
            
            # 计算每个节点的相关性
            relevance_scores = []
            for j in range(node_features.shape[1]):
                node_feat = node_features[i, j]
                combined = torch.cat([key_features, node_feat])
                score = self.relevance_scorer(combined)
                relevance_scores.append(score)
            
            relevance_scores = torch.stack(relevance_scores).squeeze()
            
            # 选择Top-6节点作为子图
            _, top_indices = torch.topk(relevance_scores, min(6, len(relevance_scores)))
            
            # 提取子图 - 修复设备问题
            if graph.device != torch.device('cpu'):
                # 图在GPU上，需要将索引也移到GPU
                subgraph = dgl.node_subgraph(graph, top_indices)
            else:
                # 图在CPU上，需要将索引移到CPU
                subgraph = dgl.node_subgraph(graph, top_indices.cpu())
            subgraph = dgl.add_self_loop(subgraph)
            sub_node_features = node_features[i][top_indices]
            
            # GAT处理
            h = self.gat1(subgraph, sub_node_features).flatten(1)
            h = F.relu(h)
            h = self.gat2(subgraph, h).flatten(1)
            
            # 池化
            mean_pool = torch.mean(h, dim=0)
            max_pool = torch.max(h, dim=0)[0]
            graph_repr = torch.cat([mean_pool, max_pool])
            
            subgraph_outputs.append(graph_repr)
        
        # 批量处理
        batch_repr = torch.stack(subgraph_outputs)
        logits = self.classifier(batch_repr)
        
        return logits

class BalancedDataset(torch.utils.data.Dataset):
    """平衡数据集"""
    
    def __init__(self, scenarios, labels, graph):
        self.scenarios = scenarios
        self.labels = labels
        self.graph = graph
        
    def __len__(self):
        return len(self.scenarios)
    
    def __getitem__(self, idx):
        scenario = self.scenarios[idx]
        
        return {
            'graph': self.graph,
            'node_features': torch.FloatTensor(scenario['node_features']).unsqueeze(0),
            'new_lightpath': torch.LongTensor([scenario['new_lightpath']]),
            'target_lightpath': torch.LongTensor([scenario['target_lightpath']]),
            'labels': torch.LongTensor([self.labels[idx]])
        }

def train_model(model, train_loader, test_loader, epochs=50, device='cuda'):
    """训练模型"""
    model = model.to(device)
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)
    criterion = nn.CrossEntropyLoss()
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5, factor=0.8)
    
    best_acc = 0
    best_auc = 0
    
    print("🚀 开始训练...")
    
    for epoch in range(epochs):
        # 训练
        model.train()
        total_loss = 0
        correct = 0
        total = 0
        
        for batch in train_loader:
            batch = {k: v.to(device) if torch.is_tensor(v) else v for k, v in batch.items()}
            # 确保图也在正确设备上
            if hasattr(batch['graph'], 'to'):
                batch['graph'] = batch['graph'].to(device)
            
            outputs = model(batch['graph'], batch['node_features'], 
                          batch['new_lightpath'], batch['target_lightpath'])
            
            loss = criterion(outputs, batch['labels'])
            
            optimizer.zero_grad()
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
            optimizer.step()
            
            total_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            total += batch['labels'].size(0)
            correct += (predicted == batch['labels']).sum().item()
        
        train_acc = correct / total
        
        # 测试
        model.eval()
        test_correct = 0
        test_total = 0
        all_probs = []
        all_labels = []
        
        with torch.no_grad():
            for batch in test_loader:
                batch = {k: v.to(device) if torch.is_tensor(v) else v for k, v in batch.items()}
                # 确保图也在正确设备上
                if hasattr(batch['graph'], 'to'):
                    batch['graph'] = batch['graph'].to(device)
                
                outputs = model(batch['graph'], batch['node_features'],
                              batch['new_lightpath'], batch['target_lightpath'])
                
                probs = F.softmax(outputs, dim=1)
                _, predicted = torch.max(outputs.data, 1)
                
                test_total += batch['labels'].size(0)
                test_correct += (predicted == batch['labels']).sum().item()
                
                all_probs.extend(probs[:, 1].cpu().numpy())
                all_labels.extend(batch['labels'].cpu().numpy())
        
        test_acc = test_correct / test_total
        
        # 计算AUC
        try:
            auc = roc_auc_score(all_labels, all_probs)
        except:
            auc = 0.5
        
        scheduler.step(test_acc)
        
        if test_acc > best_acc:
            best_acc = test_acc
            best_auc = auc
        
        if epoch % 10 == 0 or epoch < 5:
            print(f"Epoch {epoch:2d}: Train Acc={train_acc:.4f}, Test Acc={test_acc:.4f}, AUC={auc:.4f}")
    
    print(f"\n🎯 最佳结果: 准确率={best_acc:.4f}, AUC={best_auc:.4f}")
    return best_acc, best_auc

def run_balanced_experiment():
    """运行平衡实验"""
    print("🧠 平衡子图GAT实验")
    print("=" * 50)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 使用设备: {device}")
    
    # 初始化
    topology = BalancedNetworkTopology()
    data_gen = BalancedDataGenerator(topology)
    graph = topology.create_graph()
    
    print(f"🌐 网络: {graph.num_nodes()}节点, {graph.num_edges()}边")
    
    # 生成平衡数据
    scenarios, labels, osnr_changes = data_gen.generate_scenarios(3000)
    
    # 数据划分
    split_idx = int(0.8 * len(scenarios))
    train_scenarios = scenarios[:split_idx]
    train_labels = labels[:split_idx]
    test_scenarios = scenarios[split_idx:]
    test_labels = labels[split_idx:]
    
    print(f"📊 数据划分: 训练{len(train_scenarios)}, 测试{len(test_scenarios)}")
    
    # 创建数据集
    def collate_fn(batch):
        return {
            'graph': batch[0]['graph'],
            'node_features': torch.cat([item['node_features'] for item in batch], dim=0),
            'new_lightpath': torch.cat([item['new_lightpath'] for item in batch], dim=0),
            'target_lightpath': torch.cat([item['target_lightpath'] for item in batch], dim=0),
            'labels': torch.cat([item['labels'] for item in batch], dim=0)
        }
    
    train_dataset = BalancedDataset(train_scenarios, train_labels, graph)
    test_dataset = BalancedDataset(test_scenarios, test_labels, graph)
    
    train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=1, shuffle=True, collate_fn=collate_fn)
    test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=1, shuffle=False, collate_fn=collate_fn)
    
    # 创建模型
    model = SimpleSubgraphGAT(node_feature_dim=8, edge_feature_dim=6, hidden_dim=64)
    print(f"🧠 模型参数: {sum(p.numel() for p in model.parameters())}")
    
    # 训练
    best_acc, best_auc = train_model(model, train_loader, test_loader, epochs=80, device=device)
    
    # 最终评估
    model.eval()
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for batch in test_loader:
            batch = {k: v.to(device) if torch.is_tensor(v) else v for k, v in batch.items()}
            if hasattr(batch['graph'], 'to'):
                batch['graph'] = batch['graph'].to(device)
            outputs = model(batch['graph'], batch['node_features'],
                          batch['new_lightpath'], batch['target_lightpath'])
            _, predicted = torch.max(outputs, 1)
            all_preds.extend(predicted.cpu().numpy())
            all_labels.extend(batch['labels'].cpu().numpy())
    
    # 详细结果
    print("\n📊 详细评估结果:")
    if len(set(all_labels)) > 1:
        report = classification_report(all_labels, all_preds, output_dict=True)
        print(f"✅ 最终准确率: {report['accuracy']:.4f}")
        print(f"   精确率: {report['weighted avg']['precision']:.4f}")
        print(f"   召回率: {report['weighted avg']['recall']:.4f}")
        print(f"   F1分数: {report['weighted avg']['f1-score']:.4f}")
        print(f"   AUC: {best_auc:.4f}")
    
    # 保存结果
    results = {
        'timestamp': datetime.now().strftime('%Y%m%d_%H%M%S'),
        'best_accuracy': best_acc,
        'best_auc': best_auc,
        'data_distribution': {
            'total_samples': len(scenarios),
            'affected': sum(labels),
            'unaffected': len(labels) - sum(labels)
        }
    }
    
    filename = f"balanced_subgraph_gat_results_{results['timestamp']}.json"
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"📁 结果保存至: {filename}")
    return model, results

if __name__ == "__main__":
    model, results = run_balanced_experiment() 