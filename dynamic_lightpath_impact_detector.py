#!/usr/bin/env python3
"""
动态光路影响识别系统
智能识别光路动态拆建时真正受影响的光路，并进行QoT更新

核心功能：
1. 实时影响传播建模
2. 多层次影响评估（物理层、网络层、服务层）
3. 自适应阈值调整
4. 增量式QoT更新策略
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import networkx as nx
from typing import Dict, List, Tuple, Set, Optional
from dataclasses import dataclass
from collections import deque, defaultdict
import time
import json
from intelligent_subgraph_qot_system import IntelligentSubgraphGAT
import dgl

@dataclass
class LightpathInfo:
    """光路信息数据结构"""
    id: int
    source: int
    destination: int
    path: List[int]
    wavelength: int
    power_dbm: float
    modulation: str
    bitrate_gbps: float
    current_qot: float
    establishment_time: float
    priority: int = 1

@dataclass
class ImpactAnalysisResult:
    """影响分析结果"""
    lightpath_id: int
    impact_probability: float
    estimated_qot_change: float
    impact_sources: List[str]  # ['crosstalk', 'power', 'wavelength', 'routing']
    confidence_score: float
    update_required: bool

class PhysicalLayerImpactModel(nn.Module):
    """物理层影响建模网络"""
    
    def __init__(self, feature_dim=12, hidden_dim=64):
        super().__init__()
        
        # 物理特征编码器
        self.physical_encoder = nn.Sequential(
            nn.Linear(feature_dim, hidden_dim),
            nn.BatchNorm1d(hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU()
        )
        
        # 串扰建模网络
        self.crosstalk_model = nn.Sequential(
            nn.Linear(hidden_dim, 32),
            nn.ReLU(),
            nn.Linear(32, 16),
            nn.ReLU(),
            nn.Linear(16, 1),
            nn.Sigmoid()
        )
        
        # 功率影响建模
        self.power_impact_model = nn.Sequential(
            nn.Linear(hidden_dim, 32),
            nn.ReLU(),
            nn.Linear(32, 1),
            nn.Tanh()  # 功率影响可正可负
        )
        
        # 非线性效应建模
        self.nonlinear_model = nn.Sequential(
            nn.Linear(hidden_dim, 32),
            nn.ReLU(),
            nn.Linear(32, 1),
            nn.Sigmoid()
        )
        
    def forward(self, lightpath_features):
        """
        物理层影响预测
        
        Args:
            lightpath_features: 光路特征 [batch_size, feature_dim]
        
        Returns:
            crosstalk_impact: 串扰影响 [0, 1]
            power_impact: 功率影响 [-1, 1]
            nonlinear_impact: 非线性影响 [0, 1]
        """
        encoded = self.physical_encoder(lightpath_features)
        
        crosstalk_impact = self.crosstalk_model(encoded)
        power_impact = self.power_impact_model(encoded)
        nonlinear_impact = self.nonlinear_model(encoded)
        
        return crosstalk_impact, power_impact, nonlinear_impact

class NetworkLayerImpactModel(nn.Module):
    """网络层影响建模网络"""
    
    def __init__(self, node_dim=10, edge_dim=6, hidden_dim=64):
        super().__init__()
        
        # 图卷积层用于建模网络拓扑影响
        self.gconv_layers = nn.ModuleList([
            dgl.nn.GraphConv(node_dim, hidden_dim, activation=F.relu),
            dgl.nn.GraphConv(hidden_dim, hidden_dim, activation=F.relu),
            dgl.nn.GraphConv(hidden_dim, hidden_dim // 2)
        ])
        
        # 路由影响预测器
        self.routing_impact_predictor = nn.Sequential(
            nn.Linear(hidden_dim // 2, 32),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(32, 16),
            nn.ReLU(),
            nn.Linear(16, 1),
            nn.Sigmoid()
        )
        
        # 拥塞影响预测器
        self.congestion_predictor = nn.Sequential(
            nn.Linear(hidden_dim // 2, 32),
            nn.ReLU(),
            nn.Linear(32, 1),
            nn.Sigmoid()
        )
        
    def forward(self, graph, node_features):
        """
        网络层影响预测
        
        Returns:
            routing_impact: 路由变化影响
            congestion_impact: 拥塞影响
        """
        h = node_features
        
        for gconv in self.gconv_layers:
            h = gconv(graph, h)
        
        # 图级别表示
        graph_repr = dgl.mean_nodes(graph, 'h', ntype=None)
        
        routing_impact = self.routing_impact_predictor(graph_repr)
        congestion_impact = self.congestion_predictor(graph_repr)
        
        return routing_impact, congestion_impact

class DynamicImpactDetector:
    """动态影响检测器 - 主要类"""
    
    def __init__(self, network_graph, intelligent_model_path=None):
        """
        初始化动态影响检测器
        
        Args:
            network_graph: 网络拓扑图
            intelligent_model_path: 训练好的智能模型路径
        """
        self.network_graph = network_graph
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 当前活跃光路
        self.active_lightpaths: Dict[int, LightpathInfo] = {}
        
        # 影响历史记录
        self.impact_history = defaultdict(list)
        
        # 网络状态
        self.network_state = {
            'congestion_matrix': np.zeros((network_graph.num_nodes(), network_graph.num_nodes())),
            'wavelength_usage': defaultdict(set),  # wavelength -> set of edges
            'power_levels': defaultdict(float),    # node -> total power
            'last_update_time': time.time()
        }
        
        # 初始化物理和网络层模型
        self.physical_model = PhysicalLayerImpactModel().to(self.device)
        self.network_model = NetworkLayerImpactModel().to(self.device)
        
        # 加载预训练的智能模型
        if intelligent_model_path:
            self.intelligent_model = IntelligentSubgraphGAT(
                node_feature_dim=10, hidden_dim=128, num_layers=3
            ).to(self.device)
            try:
                self.intelligent_model.load_state_dict(torch.load(intelligent_model_path))
                self.intelligent_model.eval()
                print(f"✅ 加载预训练智能模型: {intelligent_model_path}")
            except Exception as e:
                print(f"⚠️ 无法加载智能模型: {e}")
                self.intelligent_model = None
        else:
            self.intelligent_model = None
        
        # 自适应阈值参数
        self.adaptive_thresholds = {
            'high_impact': 0.7,      # 高影响阈值
            'medium_impact': 0.4,    # 中影响阈值
            'low_impact': 0.1,       # 低影响阈值
            'confidence_min': 0.6    # 最小置信度阈值
        }
        
        print(f"🎯 动态影响检测器初始化完成")
        print(f"   网络节点数: {network_graph.num_nodes()}")
        print(f"   网络边数: {network_graph.num_edges()}")
        print(f"   使用设备: {self.device}")
        
    def add_lightpath(self, lightpath: LightpathInfo) -> Dict:
        """
        添加新光路并分析影响
        
        Args:
            lightpath: 新光路信息
            
        Returns:
            影响分析结果
        """
        print(f"🔍 分析新光路 LP-{lightpath.id} 的影响...")
        print(f"   路径: {lightpath.source} → {lightpath.destination}")
        print(f"   波长: {lightpath.wavelength}, 功率: {lightpath.power_dbm} dBm")
        
        # 1. 识别潜在受影响的光路
        candidate_lightpaths = self._identify_candidate_lightpaths(lightpath)
        print(f"   候选受影响光路数: {len(candidate_lightpaths)}")
        
        # 2. 多层次影响分析
        impact_results = []
        
        for candidate_id in candidate_lightpaths:
            candidate_lp = self.active_lightpaths[candidate_id]
            
            # 物理层影响分析
            physical_impact = self._analyze_physical_impact(lightpath, candidate_lp)
            
            # 网络层影响分析
            network_impact = self._analyze_network_impact(lightpath, candidate_lp)
            
            # 使用智能模型进行精确预测
            intelligent_impact = self._analyze_with_intelligent_model(lightpath, candidate_lp)
            
            # 综合影响评估
            combined_impact = self._combine_impact_scores(
                physical_impact, network_impact, intelligent_impact
            )
            
            if combined_impact['update_required']:
                impact_results.append(combined_impact)
        
        # 3. 更新网络状态
        self._update_network_state(lightpath)
        
        # 4. 添加到活跃光路
        self.active_lightpaths[lightpath.id] = lightpath
        
        # 5. 自适应阈值调整
        self._adapt_thresholds(impact_results)
        
        analysis_result = {
            'new_lightpath_id': lightpath.id,
            'affected_lightpaths': impact_results,
            'total_affected': len(impact_results),
            'network_state_updated': True,
            'analysis_timestamp': time.time()
        }
        
        print(f"✅ 影响分析完成: {len(impact_results)} 条光路需要更新")
        
        return analysis_result
    
    def remove_lightpath(self, lightpath_id: int) -> Dict:
        """
        移除光路并分析影响
        
        Args:
            lightpath_id: 要移除的光路ID
            
        Returns:
            影响分析结果
        """
        if lightpath_id not in self.active_lightpaths:
            return {'error': f'光路 LP-{lightpath_id} 不存在'}
        
        removed_lightpath = self.active_lightpaths[lightpath_id]
        print(f"🗑️ 移除光路 LP-{lightpath_id}")
        print(f"   路径: {removed_lightpath.source} → {removed_lightpath.destination}")
        
        # 1. 识别可能受益的光路（原本受该光路影响的）
        potentially_improved_lightpaths = []
        
        for lp_id, lp_info in self.active_lightpaths.items():
            if lp_id != lightpath_id:
                # 检查是否原本受到移除光路的影响
                if self._had_impact_relationship(removed_lightpath, lp_info):
                    potentially_improved_lightpaths.append(lp_id)
        
        # 2. 分析QoT改善情况
        improvement_results = []
        
        for candidate_id in potentially_improved_lightpaths:
            candidate_lp = self.active_lightpaths[candidate_id]
            
            # 预测QoT改善
            improvement = self._predict_qot_improvement(removed_lightpath, candidate_lp)
            
            if improvement['improvement_significant']:
                improvement_results.append(improvement)
        
        # 3. 更新网络状态
        self._remove_from_network_state(removed_lightpath)
        
        # 4. 从活跃光路中移除
        del self.active_lightpaths[lightpath_id]
        
        analysis_result = {
            'removed_lightpath_id': lightpath_id,
            'improved_lightpaths': improvement_results,
            'total_improved': len(improvement_results),
            'network_state_updated': True,
            'analysis_timestamp': time.time()
        }
        
        print(f"✅ 移除分析完成: {len(improvement_results)} 条光路QoT改善")
        
        return analysis_result
    
    def _identify_candidate_lightpaths(self, new_lightpath: LightpathInfo) -> List[int]:
        """识别候选受影响光路"""
        candidates = []
        
        for lp_id, existing_lp in self.active_lightpaths.items():
            # 快速筛选条件
            
            # 1. 波长邻近性
            wavelength_diff = abs(new_lightpath.wavelength - existing_lp.wavelength)
            if wavelength_diff > 10:  # 波长相差太远，影响较小
                continue
            
            # 2. 路径重叠检查
            path_overlap = self._calculate_path_overlap(new_lightpath.path, existing_lp.path)
            if path_overlap < 0.05:  # 几乎无重叠
                continue
            
            # 3. 功率差异
            power_diff = abs(new_lightpath.power_dbm - existing_lp.power_dbm)
            if power_diff > 15:  # 功率相差太大
                continue
            
            # 4. 地理接近性（基于节点ID的简单估计）
            geo_proximity = self._estimate_geographic_proximity(new_lightpath, existing_lp)
            if geo_proximity < 0.1:
                continue
            
            candidates.append(lp_id)
        
        return candidates
    
    def _analyze_physical_impact(self, new_lp: LightpathInfo, target_lp: LightpathInfo) -> Dict:
        """物理层影响分析"""
        
        # 构建物理特征向量
        features = self._build_physical_features(new_lp, target_lp)
        features_tensor = torch.tensor(features, dtype=torch.float32).unsqueeze(0).to(self.device)
        
        with torch.no_grad():
            crosstalk, power_impact, nonlinear = self.physical_model(features_tensor)
        
        # 计算综合物理影响
        physical_score = (
            crosstalk.item() * 0.4 +
            abs(power_impact.item()) * 0.35 +
            nonlinear.item() * 0.25
        )
        
        return {
            'type': 'physical',
            'score': physical_score,
            'components': {
                'crosstalk': crosstalk.item(),
                'power_impact': power_impact.item(),
                'nonlinear_effects': nonlinear.item()
            },
            'confidence': min(1.0, physical_score * 1.2)
        }
    
    def _analyze_network_impact(self, new_lp: LightpathInfo, target_lp: LightpathInfo) -> Dict:
        """网络层影响分析"""
        
        # 构建网络特征
        node_features = self._build_network_node_features()
        node_features_tensor = torch.tensor(node_features, dtype=torch.float32).to(self.device)
        
        with torch.no_grad():
            routing_impact, congestion_impact = self.network_model(
                self.network_graph.to(self.device), node_features_tensor
            )
        
        # 路径重叠影响
        path_overlap = self._calculate_path_overlap(new_lp.path, target_lp.path)
        
        # 计算综合网络影响
        network_score = (
            routing_impact.item() * 0.4 +
            congestion_impact.item() * 0.3 +
            path_overlap * 0.3
        )
        
        return {
            'type': 'network',
            'score': network_score,
            'components': {
                'routing_impact': routing_impact.item(),
                'congestion_impact': congestion_impact.item(),
                'path_overlap': path_overlap
            },
            'confidence': min(1.0, network_score * 1.1)
        }
    
    def _analyze_with_intelligent_model(self, new_lp: LightpathInfo, target_lp: LightpathInfo) -> Optional[Dict]:
        """使用智能模型进行精确分析"""
        if self.intelligent_model is None:
            return None
        
        # 构建节点特征
        node_features = self._build_intelligent_model_features()
        node_features_tensor = torch.tensor(node_features, dtype=torch.float32).to(self.device)
        
        # 光路节点
        new_lightpath_nodes = [new_lp.source, new_lp.destination]
        target_lightpath_nodes = [target_lp.source, target_lp.destination]
        
        with torch.no_grad():
            impact_logits, qot_prediction = self.intelligent_model(
                self.network_graph.to(self.device), node_features_tensor,
                new_lightpath_nodes, target_lightpath_nodes
            )
            
            impact_prob = F.softmax(impact_logits, dim=1)[0, 1].item()  # 受影响的概率
            qot_change = qot_prediction.item()
        
        return {
            'type': 'intelligent',
            'score': impact_prob,
            'qot_change_prediction': qot_change,
            'confidence': max(impact_prob, 1 - impact_prob)  # 置信度基于预测的确定性
        }
    
    def _combine_impact_scores(self, physical_impact: Dict, network_impact: Dict, 
                             intelligent_impact: Optional[Dict]) -> ImpactAnalysisResult:
        """综合多层次影响评估"""
        
        # 权重配置
        weights = {
            'physical': 0.35,
            'network': 0.25,
            'intelligent': 0.4 if intelligent_impact else 0.0
        }
        
        # 如果没有智能模型，重新分配权重
        if intelligent_impact is None:
            weights['physical'] = 0.6
            weights['network'] = 0.4
        
        # 计算加权平均影响分数
        total_score = (
            physical_impact['score'] * weights['physical'] +
            network_impact['score'] * weights['network']
        )
        
        impact_sources = ['physical', 'network']
        estimated_qot_change = 0.0
        
        if intelligent_impact:
            total_score += intelligent_impact['score'] * weights['intelligent']
            impact_sources.append('intelligent')
            estimated_qot_change = intelligent_impact.get('qot_change_prediction', 0.0)
        
        # 综合置信度
        confidences = [physical_impact['confidence'], network_impact['confidence']]
        if intelligent_impact:
            confidences.append(intelligent_impact['confidence'])
        
        combined_confidence = np.mean(confidences)
        
        # 决定是否需要更新
        update_required = (
            total_score > self.adaptive_thresholds['medium_impact'] and
            combined_confidence > self.adaptive_thresholds['confidence_min']
        )
        
        return ImpactAnalysisResult(
            lightpath_id=0,  # 将由调用者设置
            impact_probability=total_score,
            estimated_qot_change=estimated_qot_change,
            impact_sources=impact_sources,
            confidence_score=combined_confidence,
            update_required=update_required
        )
    
    def _build_physical_features(self, new_lp: LightpathInfo, target_lp: LightpathInfo) -> List[float]:
        """构建物理层特征向量"""
        features = [
            # 功率相关
            new_lp.power_dbm,
            target_lp.power_dbm,
            abs(new_lp.power_dbm - target_lp.power_dbm),
            
            # 波长相关
            float(new_lp.wavelength),
            float(target_lp.wavelength),
            abs(new_lp.wavelength - target_lp.wavelength),
            
            # 路径相关
            len(new_lp.path),
            len(target_lp.path),
            self._calculate_path_overlap(new_lp.path, target_lp.path),
            
            # 业务相关
            new_lp.bitrate_gbps,
            target_lp.bitrate_gbps,
            abs(new_lp.bitrate_gbps - target_lp.bitrate_gbps)
        ]
        
        return features
    
    def _build_network_node_features(self) -> List[List[float]]:
        """构建网络层节点特征"""
        num_nodes = self.network_graph.num_nodes()
        features = []
        
        for node_id in range(num_nodes):
            # 节点度数
            degree = self.network_graph.in_degrees()[node_id].item()
            
            # 当前功率水平
            power_level = self.network_state['power_levels'].get(node_id, 0.0)
            
            # 拥塞水平
            congestion = np.sum(self.network_state['congestion_matrix'][node_id])
            
            # 波长使用情况
            wavelength_usage = len([wl for wl, edges in self.network_state['wavelength_usage'].items() 
                                  if any(node_id in edge for edge in edges)])
            
            # 活跃光路数量
            active_lightpaths = sum(1 for lp in self.active_lightpaths.values() 
                                  if node_id in lp.path)
            
            node_features = [
                float(degree), power_level, congestion, float(wavelength_usage),
                float(active_lightpaths), 0.0, 0.0, 0.0, 0.0, 0.0  # 补齐到10维
            ]
            
            features.append(node_features)
        
        return features
    
    def _build_intelligent_model_features(self) -> List[List[float]]:
        """为智能模型构建节点特征"""
        return self._build_network_node_features()  # 复用网络特征
    
    def _calculate_path_overlap(self, path1: List[int], path2: List[int]) -> float:
        """计算路径重叠比例"""
        if not path1 or not path2:
            return 0.0
        
        edges1 = set()
        edges2 = set()
        
        for i in range(len(path1) - 1):
            edges1.add(tuple(sorted([path1[i], path1[i+1]])))
        
        for i in range(len(path2) - 1):
            edges2.add(tuple(sorted([path2[i], path2[i+1]])))
        
        if not edges2:
            return 0.0
        
        overlap = len(edges1.intersection(edges2))
        return overlap / len(edges2.union(edges1))
    
    def _estimate_geographic_proximity(self, lp1: LightpathInfo, lp2: LightpathInfo) -> float:
        """估计地理接近性（简化版本）"""
        # 基于节点ID的简单距离估计
        dist1 = abs(lp1.source - lp2.source) + abs(lp1.destination - lp2.destination)
        dist2 = abs(lp1.source - lp2.destination) + abs(lp1.destination - lp2.source)
        
        min_dist = min(dist1, dist2)
        return 1.0 / (1.0 + min_dist * 0.1)
    
    def _update_network_state(self, lightpath: LightpathInfo):
        """更新网络状态"""
        # 更新功率水平
        for node in lightpath.path:
            self.network_state['power_levels'][node] += lightpath.power_dbm * 0.1
        
        # 更新波长使用
        edges = [(lightpath.path[i], lightpath.path[i+1]) for i in range(len(lightpath.path) - 1)]
        self.network_state['wavelength_usage'][lightpath.wavelength].update(edges)
        
        # 更新拥塞矩阵
        for i, node1 in enumerate(lightpath.path[:-1]):
            node2 = lightpath.path[i+1]
            self.network_state['congestion_matrix'][node1][node2] += 1
            self.network_state['congestion_matrix'][node2][node1] += 1
        
        self.network_state['last_update_time'] = time.time()
    
    def _remove_from_network_state(self, lightpath: LightpathInfo):
        """从网络状态中移除光路影响"""
        # 移除功率影响
        for node in lightpath.path:
            self.network_state['power_levels'][node] -= lightpath.power_dbm * 0.1
            self.network_state['power_levels'][node] = max(0, self.network_state['power_levels'][node])
        
        # 移除波长使用
        edges = [(lightpath.path[i], lightpath.path[i+1]) for i in range(len(lightpath.path) - 1)]
        wavelength_edges = self.network_state['wavelength_usage'][lightpath.wavelength]
        for edge in edges:
            wavelength_edges.discard(edge)
        
        # 更新拥塞矩阵
        for i, node1 in enumerate(lightpath.path[:-1]):
            node2 = lightpath.path[i+1]
            self.network_state['congestion_matrix'][node1][node2] -= 1
            self.network_state['congestion_matrix'][node2][node1] -= 1
            self.network_state['congestion_matrix'][node1][node2] = max(0, self.network_state['congestion_matrix'][node1][node2])
            self.network_state['congestion_matrix'][node2][node1] = max(0, self.network_state['congestion_matrix'][node2][node1])
    
    def _had_impact_relationship(self, removed_lp: LightpathInfo, candidate_lp: LightpathInfo) -> bool:
        """检查两个光路之间是否存在影响关系"""
        # 简化的影响关系判断
        wavelength_close = abs(removed_lp.wavelength - candidate_lp.wavelength) <= 5
        path_overlap = self._calculate_path_overlap(removed_lp.path, candidate_lp.path) > 0.1
        
        return wavelength_close and path_overlap
    
    def _predict_qot_improvement(self, removed_lp: LightpathInfo, candidate_lp: LightpathInfo) -> Dict:
        """预测QoT改善情况"""
        # 简化的QoT改善预测
        path_overlap = self._calculate_path_overlap(removed_lp.path, candidate_lp.path)
        wavelength_diff = abs(removed_lp.wavelength - candidate_lp.wavelength)
        power_diff = abs(removed_lp.power_dbm - candidate_lp.power_dbm)
        
        # 估计改善程度
        improvement_score = (
            path_overlap * 0.4 +
            (1.0 / (1.0 + wavelength_diff * 0.1)) * 0.3 +
            (1.0 / (1.0 + power_diff * 0.05)) * 0.3
        )
        
        estimated_improvement_db = improvement_score * 2.0  # 最大2dB改善
        
        return {
            'lightpath_id': candidate_lp.id,
            'improvement_score': improvement_score,
            'estimated_improvement_db': estimated_improvement_db,
            'improvement_significant': improvement_score > 0.3
        }
    
    def _adapt_thresholds(self, impact_results: List[ImpactAnalysisResult]):
        """自适应阈值调整"""
        if not impact_results:
            return
        
        # 基于最近的影响分析结果调整阈值
        impact_scores = [result.impact_probability for result in impact_results]
        confidence_scores = [result.confidence_score for result in impact_results]
        
        # 调整影响阈值（基于分布）
        if len(impact_scores) > 5:
            median_impact = np.median(impact_scores)
            std_impact = np.std(impact_scores)
            
            # 动态调整
            self.adaptive_thresholds['high_impact'] = min(0.8, median_impact + std_impact)
            self.adaptive_thresholds['medium_impact'] = max(0.2, median_impact)
            self.adaptive_thresholds['low_impact'] = max(0.05, median_impact - std_impact)
        
        # 调整置信度阈值
        if len(confidence_scores) > 5:
            min_confidence = np.percentile(confidence_scores, 25)  # 25th percentile
            self.adaptive_thresholds['confidence_min'] = max(0.4, min_confidence)
    
    def get_network_statistics(self) -> Dict:
        """获取网络统计信息"""
        return {
            'active_lightpaths': len(self.active_lightpaths),
            'total_power': sum(self.network_state['power_levels'].values()),
            'wavelength_utilization': len(self.network_state['wavelength_usage']),
            'avg_congestion': np.mean(self.network_state['congestion_matrix']),
            'current_thresholds': self.adaptive_thresholds.copy(),
            'last_update': self.network_state['last_update_time']
        }

def test_dynamic_impact_detector():
    """测试动态影响检测器"""
    print("🧪 测试动态光路影响检测器")
    print("=" * 50)
    
    # 创建测试网络
    u = torch.tensor([0,0,1,2,3,3,4,4,4,5,6,6,7,8,11,9,8,13,11,11,13,10])
    v = torch.tensor([1,2,3,4,4,5,5,8,6,7,7,11,9,11,9,10,13,11,10,12,12,12])
    test_graph = dgl.graph((u, v))
    test_graph = dgl.to_bidirected(test_graph)
    
    # 创建检测器
    detector = DynamicImpactDetector(test_graph)
    
    # 测试光路
    test_lightpaths = [
        LightpathInfo(
            id=1, source=0, destination=5, path=[0, 1, 3, 4, 5],
            wavelength=40, power_dbm=0.0, modulation='16QAM',
            bitrate_gbps=100, current_qot=25.5, establishment_time=time.time()
        ),
        LightpathInfo(
            id=2, source=2, destination=6, path=[2, 3, 4, 6],
            wavelength=42, power_dbm=1.0, modulation='16QAM',
            bitrate_gbps=100, current_qot=24.8, establishment_time=time.time()
        ),
        LightpathInfo(
            id=3, source=7, destination=11, path=[7, 9, 11],
            wavelength=38, power_dbm=-1.0, modulation='QPSK',
            bitrate_gbps=50, current_qot=26.2, establishment_time=time.time()
        )
    ]
    
    print(f"\n📈 添加测试光路...")
    
    # 依次添加光路并分析影响
    for i, lp in enumerate(test_lightpaths):
        print(f"\n--- 添加光路 {i+1}/3 ---")
        result = detector.add_lightpath(lp)
        
        print(f"受影响光路数: {result['total_affected']}")
        for affected in result['affected_lightpaths']:
            print(f"  LP-{affected.lightpath_id}: 影响概率={affected.impact_probability:.3f}, "
                  f"置信度={affected.confidence_score:.3f}")
    
    # 测试光路移除
    print(f"\n📉 移除测试光路...")
    remove_result = detector.remove_lightpath(2)
    print(f"QoT改善光路数: {remove_result['total_improved']}")
    
    # 显示网络统计
    stats = detector.get_network_statistics()
    print(f"\n📊 网络统计:")
    print(f"  活跃光路数: {stats['active_lightpaths']}")
    print(f"  总功率: {stats['total_power']:.2f}")
    print(f"  平均拥塞: {stats['avg_congestion']:.3f}")
    print(f"  自适应阈值: {stats['current_thresholds']}")
    
    print(f"\n✅ 动态影响检测器测试完成!")

if __name__ == "__main__":
    test_dynamic_impact_detector()