#!/usr/bin/env python3
"""
全面对比测试系统
将子图GAT方法与全图GNN基线进行全面对比

对比维度：
1. 预测精度 (R², RMSE, MAE)
2. 计算复杂度 (时间复杂度, 空间复杂度)
3. 可扩展性 (不同网络规模下的性能)
4. 实时性能 (推理速度, 吞吐量)
5. 模型复杂度 (参数数量, 内存占用)
6. 鲁棒性 (噪声和异常值处理能力)
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import dgl
import numpy as np
import time
import json
import pickle
import psutil
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional
from datetime import datetime
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.model_selection import train_test_split
import pandas as pd
from collections import defaultdict
import threading
import multiprocessing
import warnings
warnings.filterwarnings('ignore')

# 导入我们的模型
from intelligent_subgraph_qot_system import IntelligentSubgraphGAT, IntelligentQoTDataGenerator
from dynamic_lightpath_impact_detector import DynamicImpactDetector
from optimized_qot_updater import OptimizedQoTUpdater

class FullGraphGNN(nn.Module):
    """全图GNN基线模型"""
    
    def __init__(self, node_feature_dim=10, hidden_dim=128, num_layers=3, 
                 num_heads=8, num_classes=2, dropout=0.15):
        super(FullGraphGNN, self).__init__()
        
        # 全图GAT层
        self.gat_layers = nn.ModuleList()
        
        # 输入层
        self.gat_layers.append(dgl.nn.GATConv(
            node_feature_dim, hidden_dim, num_heads,
            feat_drop=dropout, attn_drop=dropout, allow_zero_in_degree=True
        ))
        
        # 隐藏层
        for _ in range(num_layers - 1):
            self.gat_layers.append(dgl.nn.GATConv(
                hidden_dim * num_heads, hidden_dim, num_heads,
                feat_drop=dropout, attn_drop=dropout, allow_zero_in_degree=True
            ))
        
        # 全图池化
        self.global_pooling = nn.Sequential(
            nn.Linear(hidden_dim * num_heads, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 光路特征融合
        self.lightpath_encoder = nn.Sequential(
            nn.Linear(8, hidden_dim // 2),  # 光路特征编码
            nn.ReLU(),
            nn.Dropout(dropout * 0.5)
        )
        
        # 多任务预测头
        self.impact_classifier = nn.Sequential(
            nn.Linear(hidden_dim + hidden_dim // 2, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, num_classes)
        )
        
        self.qot_regressor = nn.Sequential(
            nn.Linear(hidden_dim + hidden_dim // 2, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, 1)
        )
        
    def forward(self, full_graph, full_node_features, new_lightpath_nodes, target_lightpath_nodes):
        """
        全图GNN前向传播
        """
        # 1. 全图GAT处理
        h = full_node_features
        
        for gat_layer in self.gat_layers:
            h = gat_layer(full_graph, h).flatten(1)
            h = F.relu(h)
        
        # 2. 全图池化
        graph_repr = torch.mean(h, dim=0)  # 简单平均池化
        graph_repr = self.global_pooling(graph_repr.unsqueeze(0))
        
        # 3. 光路特征编码
        new_src_feat = full_node_features[new_lightpath_nodes[0]]
        new_dst_feat = full_node_features[new_lightpath_nodes[1]]
        target_src_feat = full_node_features[target_lightpath_nodes[0]]
        target_dst_feat = full_node_features[target_lightpath_nodes[1]]
        
        lightpath_feat = torch.cat([
            new_src_feat, new_dst_feat, target_src_feat, target_dst_feat
        ], dim=0)
        
        lightpath_repr = self.lightpath_encoder(lightpath_feat.unsqueeze(0))
        
        # 4. 特征融合
        combined_repr = torch.cat([graph_repr, lightpath_repr], dim=1)
        
        # 5. 多任务预测
        impact_logits = self.impact_classifier(combined_repr)
        qot_prediction = self.qot_regressor(combined_repr)
        
        return impact_logits, qot_prediction

class PerformanceProfiler:
    """性能分析器"""
    
    def __init__(self):
        self.profiles = defaultdict(list)
        self.start_times = {}
        self.memory_usage = []
        self.cpu_usage = []
        
    def start_profile(self, operation_name: str):
        """开始性能分析"""
        self.start_times[operation_name] = time.time()
        
        # 记录内存和CPU使用情况
        process = psutil.Process()
        memory_mb = process.memory_info().rss / 1024 / 1024
        cpu_percent = process.cpu_percent()
        
        self.memory_usage.append(memory_mb)
        self.cpu_usage.append(cpu_percent)
        
    def end_profile(self, operation_name: str):
        """结束性能分析"""
        if operation_name in self.start_times:
            elapsed_time = time.time() - self.start_times[operation_name]
            self.profiles[operation_name].append(elapsed_time)
            del self.start_times[operation_name]
            return elapsed_time
        return 0.0
    
    def get_stats(self) -> Dict:
        """获取性能统计"""
        stats = {}
        
        for operation, times in self.profiles.items():
            stats[operation] = {
                'count': len(times),
                'total_time': sum(times),
                'avg_time': np.mean(times),
                'min_time': np.min(times),
                'max_time': np.max(times),
                'std_time': np.std(times)
            }
        
        stats['memory'] = {
            'avg_mb': np.mean(self.memory_usage) if self.memory_usage else 0,
            'max_mb': np.max(self.memory_usage) if self.memory_usage else 0,
            'min_mb': np.min(self.memory_usage) if self.memory_usage else 0
        }
        
        stats['cpu'] = {
            'avg_percent': np.mean(self.cpu_usage) if self.cpu_usage else 0,
            'max_percent': np.max(self.cpu_usage) if self.cpu_usage else 0
        }
        
        return stats

class ComprehensiveComparison:
    """全面对比测试系统"""
    
    def __init__(self, network_sizes=[14, 28, 56], num_scenarios_per_size=1000):
        """
        初始化对比测试系统
        
        Args:
            network_sizes: 测试的网络规模
            num_scenarios_per_size: 每个网络规模的测试场景数
        """
        self.network_sizes = network_sizes
        self.num_scenarios_per_size = num_scenarios_per_size
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 初始化性能分析器
        self.profiler = PerformanceProfiler()
        
        # 对比结果存储
        self.comparison_results = {}
        
        print(f"🔬 全面对比测试系统初始化")
        print(f"   测试网络规模: {network_sizes}")
        print(f"   每规模场景数: {num_scenarios_per_size}")
        print(f"   使用设备: {self.device}")
        
    def run_comprehensive_comparison(self) -> Dict:
        """运行全面对比测试"""
        print("🚀 开始全面对比测试")
        print("=" * 80)
        
        all_results = {}
        
        for network_size in self.network_sizes:
            print(f"\n📊 测试网络规模: {network_size} 节点")
            
            # 生成测试数据
            test_data = self._generate_test_data(network_size)
            
            # 训练和测试模型
            subgraph_results = self._test_subgraph_model(test_data, network_size)
            fullgraph_results = self._test_fullgraph_model(test_data, network_size)
            
            # 性能对比分析
            comparison = self._analyze_performance_comparison(
                subgraph_results, fullgraph_results, network_size
            )
            
            all_results[f"network_{network_size}"] = comparison
            
            # 打印当前结果摘要
            self._print_comparison_summary(comparison, network_size)
        
        # 综合分析
        overall_analysis = self._overall_analysis(all_results)
        all_results['overall_analysis'] = overall_analysis
        
        # 生成详细报告
        self._generate_comprehensive_report(all_results)
        
        # 创建可视化图表
        self._create_comparison_plots(all_results)
        
        self.comparison_results = all_results
        return all_results
    
    def _generate_test_data(self, network_size: int) -> Dict:
        """生成指定网络规模的测试数据"""
        print(f"   📋 生成 {network_size} 节点网络的测试数据...")
        
        # 创建可扩展的网络拓扑
        if network_size == 14:
            # 使用原始日本网络
            u = torch.tensor([0,0,1,2,3,3,4,4,4,5,6,6,7,8,11,9,8,13,11,11,13,10])
            v = torch.tensor([1,2,3,4,4,5,5,8,6,7,7,11,9,11,9,10,13,11,10,12,12,12])
        else:
            # 生成更大的网络（基于小世界网络）
            u, v = self._generate_scalable_network(network_size)
        
        full_graph = dgl.graph((u, v))
        full_graph = dgl.to_bidirected(full_graph)
        
        # 生成测试场景
        data_generator = IntelligentQoTDataGenerator(network_size)
        data_generator.full_graph = full_graph
        data_generator.network_size = network_size
        
        scenarios = data_generator.generate_enhanced_scenarios(self.num_scenarios_per_size)
        
        return {
            'graph': full_graph,
            'scenarios': scenarios,
            'network_size': network_size
        }
    
    def _generate_scalable_network(self, network_size: int) -> Tuple[torch.Tensor, torch.Tensor]:
        """生成可扩展的网络拓扑"""
        import networkx as nx
        
        # 生成小世界网络（适合光网络特性）
        k = max(4, min(8, network_size // 4))  # 每个节点的初始邻居数
        p = 0.3  # 重连概率
        
        nx_graph = nx.watts_strogatz_graph(network_size, k, p, seed=42)
        
        # 转换为边列表
        edges = list(nx_graph.edges())
        u = torch.tensor([e[0] for e in edges])
        v = torch.tensor([e[1] for e in edges])
        
        return u, v
    
    def _test_subgraph_model(self, test_data: Dict, network_size: int) -> Dict:
        """测试子图模型"""
        print(f"   🧠 测试智能子图GAT模型...")
        
        self.profiler.start_profile(f'subgraph_training_{network_size}')
        
        # 准备数据
        scenarios = test_data['scenarios']
        train_scenarios, test_scenarios = train_test_split(
            scenarios, test_size=0.3, random_state=42,
            stratify=[s['impact_label'] for s in scenarios]
        )
        
        # 创建子图模型
        subgraph_model = IntelligentSubgraphGAT(
            node_feature_dim=10,
            hidden_dim=min(128, max(64, network_size * 2)),  # 根据网络大小调整
            num_layers=3,
            num_heads=8,
            num_classes=2,
            dropout=0.15
        ).to(self.device)
        
        # 训练模型
        training_results = self._train_model(
            subgraph_model, train_scenarios, test_scenarios, 
            model_type='subgraph', network_size=network_size
        )
        
        training_time = self.profiler.end_profile(f'subgraph_training_{network_size}')
        
        # 测试推理性能
        inference_results = self._test_inference_performance(
            subgraph_model, test_scenarios, model_type='subgraph', network_size=network_size
        )
        
        # 计算模型复杂度
        complexity_results = self._analyze_model_complexity(subgraph_model, test_data['graph'])
        
        return {
            'model_type': 'subgraph',
            'network_size': network_size,
            'training_results': training_results,
            'inference_results': inference_results,
            'complexity_results': complexity_results,
            'training_time': training_time,
            'model_parameters': sum(p.numel() for p in subgraph_model.parameters())
        }
    
    def _test_fullgraph_model(self, test_data: Dict, network_size: int) -> Dict:
        """测试全图模型"""
        print(f"   🌐 测试全图GNN基线模型...")
        
        self.profiler.start_profile(f'fullgraph_training_{network_size}')
        
        # 准备数据
        scenarios = test_data['scenarios']
        train_scenarios, test_scenarios = train_test_split(
            scenarios, test_size=0.3, random_state=42,
            stratify=[s['impact_label'] for s in scenarios]
        )
        
        # 创建全图模型
        fullgraph_model = FullGraphGNN(
            node_feature_dim=10,
            hidden_dim=min(128, max(64, network_size * 2)),  # 根据网络大小调整
            num_layers=3,
            num_heads=8,
            num_classes=2,
            dropout=0.15
        ).to(self.device)
        
        # 训练模型
        training_results = self._train_model(
            fullgraph_model, train_scenarios, test_scenarios, 
            model_type='fullgraph', network_size=network_size
        )
        
        training_time = self.profiler.end_profile(f'fullgraph_training_{network_size}')
        
        # 测试推理性能
        inference_results = self._test_inference_performance(
            fullgraph_model, test_scenarios, model_type='fullgraph', network_size=network_size
        )
        
        # 计算模型复杂度
        complexity_results = self._analyze_model_complexity(fullgraph_model, test_data['graph'])
        
        return {
            'model_type': 'fullgraph',
            'network_size': network_size,
            'training_results': training_results,
            'inference_results': inference_results,
            'complexity_results': complexity_results,
            'training_time': training_time,
            'model_parameters': sum(p.numel() for p in fullgraph_model.parameters())
        }
    
    def _train_model(self, model, train_scenarios, test_scenarios, model_type, network_size) -> Dict:
        """训练模型"""
        
        optimizer = torch.optim.AdamW(model.parameters(), lr=0.001, weight_decay=1e-4)
        classification_criterion = nn.CrossEntropyLoss()
        regression_criterion = nn.MSELoss()
        
        # 训练配置（根据网络大小调整）
        epochs = max(20, min(50, 100 - network_size))
        
        model.train()
        train_losses = []
        train_accuracies = []
        
        for epoch in range(epochs):
            total_cls_loss = 0
            total_reg_loss = 0
            cls_correct = 0
            reg_predictions = []
            reg_targets = []
            
            for scenario in train_scenarios:
                full_graph = scenario['full_graph'].to(self.device)
                node_features = scenario['node_features'].to(self.device)
                new_lightpath_nodes = scenario['new_lightpath_nodes']
                target_lightpath_nodes = scenario['target_lightpath_nodes']
                impact_label = torch.tensor([scenario['impact_label']], dtype=torch.long).to(self.device)
                qot_target = torch.tensor([scenario['qot_degradation']], dtype=torch.float).to(self.device)
                
                optimizer.zero_grad()
                
                # 前向传播
                impact_logits, qot_pred = model(
                    full_graph, node_features, new_lightpath_nodes, target_lightpath_nodes
                )
                
                # 多任务损失
                cls_loss = classification_criterion(impact_logits, impact_label)
                reg_loss = regression_criterion(qot_pred, qot_target)
                total_loss = cls_loss + 0.5 * reg_loss
                
                total_loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                total_cls_loss += cls_loss.item()
                total_reg_loss += reg_loss.item()
                
                cls_pred = torch.argmax(impact_logits, dim=1)
                cls_correct += (cls_pred == impact_label).sum().item()
                
                reg_predictions.append(qot_pred.item())
                reg_targets.append(qot_target.item())
            
            # 计算指标
            avg_cls_loss = total_cls_loss / len(train_scenarios)
            cls_accuracy = cls_correct / len(train_scenarios)
            reg_r2 = r2_score(reg_targets, reg_predictions)
            
            train_losses.append(avg_cls_loss)
            train_accuracies.append(cls_accuracy)
        
        # 最终测试
        model.eval()
        test_cls_correct = 0
        test_reg_predictions = []
        test_reg_targets = []
        
        with torch.no_grad():
            for scenario in test_scenarios:
                full_graph = scenario['full_graph'].to(self.device)
                node_features = scenario['node_features'].to(self.device)
                new_lightpath_nodes = scenario['new_lightpath_nodes']
                target_lightpath_nodes = scenario['target_lightpath_nodes']
                
                impact_logits, qot_pred = model(
                    full_graph, node_features, new_lightpath_nodes, target_lightpath_nodes
                )
                
                cls_pred = torch.argmax(impact_logits, dim=1).cpu().item()
                test_cls_correct += (cls_pred == scenario['impact_label'])
                
                test_reg_predictions.append(qot_pred.cpu().item())
                test_reg_targets.append(scenario['qot_degradation'])
        
        test_cls_accuracy = test_cls_correct / len(test_scenarios)
        test_reg_r2 = r2_score(test_reg_targets, test_reg_predictions)
        test_reg_rmse = np.sqrt(mean_squared_error(test_reg_targets, test_reg_predictions))
        test_reg_mae = mean_absolute_error(test_reg_targets, test_reg_predictions)
        
        return {
            'train_accuracy': train_accuracies[-1],
            'test_accuracy': test_cls_accuracy,
            'test_r2': test_reg_r2,
            'test_rmse': test_reg_rmse,
            'test_mae': test_reg_mae,
            'epochs_trained': epochs,
            'final_loss': train_losses[-1] if train_losses else 0.0
        }
    
    def _test_inference_performance(self, model, test_scenarios, model_type, network_size) -> Dict:
        """测试推理性能"""
        model.eval()
        
        # 预热
        with torch.no_grad():
            for i, scenario in enumerate(test_scenarios[:10]):
                if i >= 10:
                    break
                full_graph = scenario['full_graph'].to(self.device)
                node_features = scenario['node_features'].to(self.device)
                new_lightpath_nodes = scenario['new_lightpath_nodes']
                target_lightpath_nodes = scenario['target_lightpath_nodes']
                
                _ = model(full_graph, node_features, new_lightpath_nodes, target_lightpath_nodes)
        
        # 实际性能测试
        inference_times = []
        memory_usage = []
        
        with torch.no_grad():
            for scenario in test_scenarios[:100]:  # 测试前100个场景
                # 测量推理时间
                torch.cuda.synchronize() if torch.cuda.is_available() else None
                start_time = time.time()
                
                full_graph = scenario['full_graph'].to(self.device)
                node_features = scenario['node_features'].to(self.device)
                new_lightpath_nodes = scenario['new_lightpath_nodes']
                target_lightpath_nodes = scenario['target_lightpath_nodes']
                
                impact_logits, qot_pred = model(
                    full_graph, node_features, new_lightpath_nodes, target_lightpath_nodes
                )
                
                torch.cuda.synchronize() if torch.cuda.is_available() else None
                end_time = time.time()
                
                inference_times.append(end_time - start_time)
                
                # 测量内存使用
                if torch.cuda.is_available():
                    memory_usage.append(torch.cuda.memory_allocated() / 1024 / 1024)  # MB
        
        # 计算吞吐量（每秒处理的场景数）
        avg_inference_time = np.mean(inference_times)
        throughput = 1.0 / avg_inference_time if avg_inference_time > 0 else 0
        
        return {
            'avg_inference_time': avg_inference_time,
            'min_inference_time': np.min(inference_times),
            'max_inference_time': np.max(inference_times),
            'std_inference_time': np.std(inference_times),
            'throughput_per_second': throughput,
            'avg_memory_mb': np.mean(memory_usage) if memory_usage else 0,
            'max_memory_mb': np.max(memory_usage) if memory_usage else 0
        }
    
    def _analyze_model_complexity(self, model, graph) -> Dict:
        """分析模型复杂度"""
        
        # 参数数量
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        # 模型大小（MB）
        param_size = sum(p.numel() * p.element_size() for p in model.parameters()) / 1024 / 1024
        
        # FLOPs估计（简化计算）
        num_nodes = graph.num_nodes()
        num_edges = graph.num_edges()
        
        # 估算FLOPs（基于GAT操作）
        estimated_flops = num_nodes * num_edges * 128 * 3  # 简化估计
        
        return {
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'model_size_mb': param_size,
            'estimated_flops': estimated_flops,
            'complexity_ratio': total_params / max(num_nodes, 1)  # 参数/节点比
        }
    
    def _analyze_performance_comparison(self, subgraph_results, fullgraph_results, network_size) -> Dict:
        """分析性能对比"""
        
        comparison = {
            'network_size': network_size,
            'accuracy_comparison': {
                'subgraph_accuracy': subgraph_results['training_results']['test_accuracy'],
                'fullgraph_accuracy': fullgraph_results['training_results']['test_accuracy'],
                'accuracy_difference': subgraph_results['training_results']['test_accuracy'] - 
                                     fullgraph_results['training_results']['test_accuracy']
            },
            'regression_comparison': {
                'subgraph_r2': subgraph_results['training_results']['test_r2'],
                'fullgraph_r2': fullgraph_results['training_results']['test_r2'],
                'subgraph_rmse': subgraph_results['training_results']['test_rmse'],
                'fullgraph_rmse': fullgraph_results['training_results']['test_rmse'],
                'r2_difference': subgraph_results['training_results']['test_r2'] - 
                               fullgraph_results['training_results']['test_r2']
            },
            'speed_comparison': {
                'subgraph_inference_time': subgraph_results['inference_results']['avg_inference_time'],
                'fullgraph_inference_time': fullgraph_results['inference_results']['avg_inference_time'],
                'speedup_ratio': fullgraph_results['inference_results']['avg_inference_time'] / 
                               max(subgraph_results['inference_results']['avg_inference_time'], 1e-6),
                'subgraph_throughput': subgraph_results['inference_results']['throughput_per_second'],
                'fullgraph_throughput': fullgraph_results['inference_results']['throughput_per_second']
            },
            'complexity_comparison': {
                'subgraph_parameters': subgraph_results['model_parameters'],
                'fullgraph_parameters': fullgraph_results['model_parameters'],
                'parameter_reduction': 1 - (subgraph_results['model_parameters'] / 
                                           max(fullgraph_results['model_parameters'], 1)),
                'subgraph_model_size': subgraph_results['complexity_results']['model_size_mb'],
                'fullgraph_model_size': fullgraph_results['complexity_results']['model_size_mb']
            },
            'training_comparison': {
                'subgraph_training_time': subgraph_results['training_time'],
                'fullgraph_training_time': fullgraph_results['training_time'],
                'training_speedup': fullgraph_results['training_time'] / 
                                  max(subgraph_results['training_time'], 1e-6)
            }
        }
        
        return comparison
    
    def _print_comparison_summary(self, comparison, network_size):
        """打印对比摘要"""
        print(f"\n   📈 网络规模 {network_size} 对比结果:")
        print(f"      精度对比:")
        print(f"        子图模型: {comparison['accuracy_comparison']['subgraph_accuracy']:.4f}")
        print(f"        全图模型: {comparison['accuracy_comparison']['fullgraph_accuracy']:.4f}")
        print(f"        精度差异: {comparison['accuracy_comparison']['accuracy_difference']:+.4f}")
        
        print(f"      速度对比:")
        print(f"        子图推理: {comparison['speed_comparison']['subgraph_inference_time']*1000:.2f}ms")
        print(f"        全图推理: {comparison['speed_comparison']['fullgraph_inference_time']*1000:.2f}ms")
        print(f"        加速比: {comparison['speed_comparison']['speedup_ratio']:.2f}x")
        
        print(f"      复杂度对比:")
        print(f"        参数减少: {comparison['complexity_comparison']['parameter_reduction']*100:.1f}%")
        print(f"        模型大小减少: {(1-comparison['complexity_comparison']['subgraph_model_size']/max(comparison['complexity_comparison']['fullgraph_model_size'],1))*100:.1f}%")
    
    def _overall_analysis(self, all_results) -> Dict:
        """综合分析"""
        
        # 收集所有网络规模的结果
        accuracy_trends = {'subgraph': [], 'fullgraph': []}
        speed_trends = {'subgraph': [], 'fullgraph': []}
        complexity_trends = {'subgraph': [], 'fullgraph': []}
        
        network_sizes = []
        
        for key, result in all_results.items():
            if key.startswith('network_'):
                network_size = result['network_size']
                network_sizes.append(network_size)
                
                accuracy_trends['subgraph'].append(result['accuracy_comparison']['subgraph_accuracy'])
                accuracy_trends['fullgraph'].append(result['accuracy_comparison']['fullgraph_accuracy'])
                
                speed_trends['subgraph'].append(result['speed_comparison']['subgraph_inference_time'])
                speed_trends['fullgraph'].append(result['speed_comparison']['fullgraph_inference_time'])
                
                complexity_trends['subgraph'].append(result['complexity_comparison']['subgraph_parameters'])
                complexity_trends['fullgraph'].append(result['complexity_comparison']['fullgraph_parameters'])
        
        # 计算平均改进
        avg_accuracy_improvement = np.mean([
            result['accuracy_comparison']['accuracy_difference'] 
            for key, result in all_results.items() 
            if key.startswith('network_')
        ])
        
        avg_speedup = np.mean([
            result['speed_comparison']['speedup_ratio'] 
            for key, result in all_results.items() 
            if key.startswith('network_')
        ])
        
        avg_parameter_reduction = np.mean([
            result['complexity_comparison']['parameter_reduction'] 
            for key, result in all_results.items() 
            if key.startswith('network_')
        ])
        
        return {
            'avg_accuracy_improvement': avg_accuracy_improvement,
            'avg_speedup': avg_speedup,
            'avg_parameter_reduction': avg_parameter_reduction,
            'scalability_analysis': {
                'accuracy_trends': accuracy_trends,
                'speed_trends': speed_trends,
                'complexity_trends': complexity_trends,
                'network_sizes': network_sizes
            },
            'winner_by_metric': {
                'accuracy': 'subgraph' if avg_accuracy_improvement > 0 else 'fullgraph',
                'speed': 'subgraph' if avg_speedup > 1 else 'fullgraph',
                'complexity': 'subgraph' if avg_parameter_reduction > 0 else 'fullgraph'
            }
        }
    
    def _generate_comprehensive_report(self, all_results):
        """生成综合报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f'comprehensive_comparison_report_{timestamp}.json'
        
        # 添加性能分析数据
        performance_stats = self.profiler.get_stats()
        all_results['performance_profiling'] = performance_stats
        
        # 添加系统信息
        all_results['system_info'] = {
            'device': str(self.device),
            'cuda_available': torch.cuda.is_available(),
            'cpu_count': multiprocessing.cpu_count(),
            'memory_total_gb': psutil.virtual_memory().total / 1024**3,
            'timestamp': timestamp
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(all_results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n📄 详细报告已保存: {report_file}")
        
        # 生成摘要报告
        self._generate_summary_report(all_results, timestamp)
    
    def _generate_summary_report(self, all_results, timestamp):
        """生成摘要报告"""
        summary_file = f'comparison_summary_{timestamp}.md'
        
        overall = all_results['overall_analysis']
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("# 子图GAT vs 全图GNN 对比测试摘要\n\n")
            f.write(f"测试时间: {timestamp}\n\n")
            
            f.write("## 核心结果\n\n")
            f.write(f"- **平均精度改进**: {overall['avg_accuracy_improvement']:+.4f}\n")
            f.write(f"- **平均加速比**: {overall['avg_speedup']:.2f}x\n")
            f.write(f"- **平均参数减少**: {overall['avg_parameter_reduction']*100:.1f}%\n\n")
            
            f.write("## 获胜指标\n\n")
            for metric, winner in overall['winner_by_metric'].items():
                f.write(f"- **{metric.capitalize()}**: {winner.capitalize()} GNN\n")
            
            f.write("\n## 网络规模对比\n\n")
            f.write("| 网络规模 | 子图精度 | 全图精度 | 子图速度(ms) | 全图速度(ms) | 加速比 |\n")
            f.write("|---------|---------|---------|-------------|-------------|--------|\n")
            
            for key, result in all_results.items():
                if key.startswith('network_'):
                    size = result['network_size']
                    sub_acc = result['accuracy_comparison']['subgraph_accuracy']
                    full_acc = result['accuracy_comparison']['fullgraph_accuracy']
                    sub_time = result['speed_comparison']['subgraph_inference_time'] * 1000
                    full_time = result['speed_comparison']['fullgraph_inference_time'] * 1000
                    speedup = result['speed_comparison']['speedup_ratio']
                    
                    f.write(f"| {size} | {sub_acc:.4f} | {full_acc:.4f} | {sub_time:.2f} | {full_time:.2f} | {speedup:.2f}x |\n")
            
            f.write(f"\n## 结论\n\n")
            
            if overall['avg_accuracy_improvement'] > 0.01 and overall['avg_speedup'] > 1.5:
                f.write("✅ **子图GAT方法显著优于全图GNN基线**\n")
                f.write("- 在保持更高精度的同时，显著提升了推理速度\n")
            elif overall['avg_speedup'] > 2.0:
                f.write("⚡ **子图GAT在速度上具有显著优势**\n")
                f.write("- 在可接受的精度损失下，大幅提升了推理效率\n")
            else:
                f.write("📊 **两种方法各有优势，需要根据具体需求选择**\n")
        
        print(f"📝 摘要报告已保存: {summary_file}")
    
    def _create_comparison_plots(self, all_results):
        """创建对比图表"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # 收集数据
        network_sizes = []
        subgraph_accuracies = []
        fullgraph_accuracies = []
        subgraph_times = []
        fullgraph_times = []
        speedup_ratios = []
        parameter_reductions = []
        
        for key, result in all_results.items():
            if key.startswith('network_'):
                network_sizes.append(result['network_size'])
                subgraph_accuracies.append(result['accuracy_comparison']['subgraph_accuracy'])
                fullgraph_accuracies.append(result['accuracy_comparison']['fullgraph_accuracy'])
                subgraph_times.append(result['speed_comparison']['subgraph_inference_time'] * 1000)
                fullgraph_times.append(result['speed_comparison']['fullgraph_inference_time'] * 1000)
                speedup_ratios.append(result['speed_comparison']['speedup_ratio'])
                parameter_reductions.append(result['complexity_comparison']['parameter_reduction'] * 100)
        
        # 1. 精度对比
        x = np.arange(len(network_sizes))
        width = 0.35
        
        ax1.bar(x - width/2, subgraph_accuracies, width, label='Subgraph GAT', color='#2ecc71', alpha=0.8)
        ax1.bar(x + width/2, fullgraph_accuracies, width, label='Full-graph GNN', color='#3498db', alpha=0.8)
        ax1.set_xlabel('Network Size')
        ax1.set_ylabel('Accuracy')
        ax1.set_title('Accuracy Comparison')
        ax1.set_xticks(x)
        ax1.set_xticklabels(network_sizes)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 推理时间对比
        ax2.plot(network_sizes, subgraph_times, 'o-', label='Subgraph GAT', color='#2ecc71', linewidth=2, markersize=8)
        ax2.plot(network_sizes, fullgraph_times, 's-', label='Full-graph GNN', color='#3498db', linewidth=2, markersize=8)
        ax2.set_xlabel('Network Size')
        ax2.set_ylabel('Inference Time (ms)')
        ax2.set_title('Inference Time Comparison')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.set_yscale('log')
        
        # 3. 加速比
        ax3.bar(range(len(network_sizes)), speedup_ratios, color='#e74c3c', alpha=0.7)
        ax3.set_xlabel('Network Size')
        ax3.set_ylabel('Speedup Ratio')
        ax3.set_title('Speedup Ratio (Subgraph vs Full-graph)')
        ax3.set_xticks(range(len(network_sizes)))
        ax3.set_xticklabels(network_sizes)
        ax3.axhline(y=1, color='black', linestyle='--', alpha=0.5)
        ax3.grid(True, alpha=0.3)
        
        # 4. 参数减少
        ax4.bar(range(len(network_sizes)), parameter_reductions, color='#f39c12', alpha=0.7)
        ax4.set_xlabel('Network Size')
        ax4.set_ylabel('Parameter Reduction (%)')
        ax4.set_title('Model Complexity Reduction')
        ax4.set_xticks(range(len(network_sizes)))
        ax4.set_xticklabels(network_sizes)
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        plot_file = f'comprehensive_comparison_plots_{timestamp}.png'
        plt.savefig(plot_file, dpi=300, bbox_inches='tight')
        print(f"📊 对比图表已保存: {plot_file}")
        
        plt.close()

def run_comprehensive_comparison_test():
    """运行全面对比测试"""
    print("🚀 启动全面对比测试系统")
    print("=" * 80)
    
    # 创建对比测试系统
    comparison_system = ComprehensiveComparison(
        network_sizes=[14, 28],  # 简化测试，避免过长时间
        num_scenarios_per_size=500
    )
    
    try:
        # 运行对比测试
        results = comparison_system.run_comprehensive_comparison()
        
        # 打印最终摘要
        print("\n" + "=" * 80)
        print("🏆 最终对比结果摘要")
        print("=" * 80)
        
        overall = results['overall_analysis']
        
        print(f"📊 平均精度改进: {overall['avg_accuracy_improvement']:+.4f}")
        print(f"⚡ 平均加速比: {overall['avg_speedup']:.2f}x")
        print(f"🗜️  平均参数减少: {overall['avg_parameter_reduction']*100:.1f}%")
        
        print(f"\n🏅 各指标获胜者:")
        for metric, winner in overall['winner_by_metric'].items():
            print(f"   {metric.capitalize()}: {winner.capitalize()} GNN")
        
        # 结论
        if overall['avg_speedup'] > 1.5 and overall['avg_accuracy_improvement'] > -0.02:
            print(f"\n✅ 结论: 子图GAT方法在保持精度的同时显著提升了效率，适合实际部署")
        elif overall['avg_speedup'] > 2.0:
            print(f"\n⚡ 结论: 子图GAT方法在速度上具有显著优势，适合对实时性要求高的场景")
        else:
            print(f"\n📝 结论: 两种方法各有优势，建议根据具体应用场景选择")
            
        return results
        
    except Exception as e:
        print(f"❌ 对比测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    results = run_comprehensive_comparison_test()