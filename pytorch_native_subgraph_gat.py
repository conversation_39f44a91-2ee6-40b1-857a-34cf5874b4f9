#!/usr/bin/env python3
"""
PyTorch原生子图GAT vs 全图GNN对比实验
去除DGL依赖，使用PyTorch原生实现图注意力机制
基于真实的日本网络拓扑进行光路影响识别任务
"""

import time
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, classification_report
import json
from datetime import datetime
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

class GraphAttentionLayer(nn.Module):
    """PyTorch原生图注意力层实现"""
    
    def __init__(self, in_features, out_features, dropout=0.1, alpha=0.2, concat=True):
        super(GraphAttentionLayer, self).__init__()
        self.dropout = dropout
        self.in_features = in_features
        self.out_features = out_features
        self.alpha = alpha
        self.concat = concat
        
        # 权重矩阵
        self.W = nn.Parameter(torch.empty(size=(in_features, out_features)))
        nn.init.xavier_uniform_(self.W.data, gain=1.414)
        
        # 注意力机制参数
        self.a = nn.Parameter(torch.empty(size=(2*out_features, 1)))
        nn.init.xavier_uniform_(self.a.data, gain=1.414)
        
        self.leakyrelu = nn.LeakyReLU(self.alpha)
        
    def forward(self, h, adj):
        """
        h: 节点特征矩阵 [N, in_features]
        adj: 邻接矩阵 [N, N]
        """
        Wh = torch.mm(h, self.W)  # [N, out_features]
        N = Wh.size()[0]
        
        # 计算注意力系数
        a_input = self._prepare_attentional_mechanism_input(Wh)  # [N, N, 2*out_features]
        e = self.leakyrelu(torch.matmul(a_input, self.a).squeeze(2))  # [N, N]
        
        # 掩码非邻接节点
        zero_vec = -9e15*torch.ones_like(e)
        attention = torch.where(adj > 0, e, zero_vec)
        attention = F.softmax(attention, dim=1)
        attention = F.dropout(attention, self.dropout, training=self.training)
        
        # 应用注意力权重
        h_prime = torch.matmul(attention, Wh)  # [N, out_features]
        
        if self.concat:
            return F.elu(h_prime)
        else:
            return h_prime
    
    def _prepare_attentional_mechanism_input(self, Wh):
        """准备注意力机制输入"""
        N = Wh.size()[0]
        Wh_repeated_in_chunks = Wh.repeat_interleave(N, dim=0)  # [N*N, out_features]
        Wh_repeated_alternating = Wh.repeat(N, 1)  # [N*N, out_features]
        
        all_combinations_matrix = torch.cat([Wh_repeated_in_chunks, Wh_repeated_alternating], dim=1)
        return all_combinations_matrix.view(N, N, 2 * self.out_features)

class MultiHeadGATLayer(nn.Module):
    """多头图注意力层"""
    
    def __init__(self, in_features, out_features, num_heads, dropout=0.1, alpha=0.2, concat=True):
        super(MultiHeadGATLayer, self).__init__()
        self.num_heads = num_heads
        self.concat = concat
        
        self.attentions = nn.ModuleList([
            GraphAttentionLayer(in_features, out_features, dropout, alpha, concat)
            for _ in range(num_heads)
        ])
        
    def forward(self, h, adj):
        """前向传播"""
        if self.concat:
            # 多头拼接
            h_list = [att(h, adj) for att in self.attentions]
            return torch.cat(h_list, dim=1)  # [N, num_heads * out_features]
        else:
            # 多头平均
            h_list = [att(h, adj) for att in self.attentions]
            return torch.mean(torch.stack(h_list), dim=0)  # [N, out_features]

class PyTorchSubgraphGAT(nn.Module):
    """PyTorch原生子图GAT模型"""
    
    def __init__(self, in_dim, hidden_dim, num_heads, num_layers, num_classes, dropout=0.1):
        super(PyTorchSubgraphGAT, self).__init__()
        self.num_layers = num_layers
        self.num_heads = num_heads
        
        # GAT层
        self.gat_layers = nn.ModuleList()
        
        # 第一层
        self.gat_layers.append(MultiHeadGATLayer(
            in_dim, hidden_dim, num_heads, dropout, concat=True
        ))
        
        # 中间层
        for _ in range(num_layers - 1):
            self.gat_layers.append(MultiHeadGATLayer(
                hidden_dim * num_heads, hidden_dim, num_heads, dropout, concat=True
            ))
        
        # 图级池化和分类器
        self.classifier = nn.Sequential(
            nn.Linear(hidden_dim * num_heads, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, num_classes)
        )
        
    def forward(self, node_features, adj_matrix):
        """
        node_features: [N, in_dim] 节点特征
        adj_matrix: [N, N] 邻接矩阵
        """
        h = node_features
        
        # GAT层前向传播
        for gat_layer in self.gat_layers:
            h = gat_layer(h, adj_matrix)
        
        # 图级池化（平均池化）
        graph_repr = torch.mean(h, dim=0, keepdim=True)  # [1, hidden_dim * num_heads]
        
        # 分类
        logits = self.classifier(graph_repr)
        return logits

class PyTorchFullGraphGNN(nn.Module):
    """PyTorch原生全图GNN模型"""
    
    def __init__(self, in_dim, hidden_dim, num_layers, num_classes, gnn_type='GCN', dropout=0.1):
        super(PyTorchFullGraphGNN, self).__init__()
        self.num_layers = num_layers
        self.gnn_type = gnn_type
        
        if gnn_type == 'GAT':
            # 使用GAT层
            self.gnn_layers = nn.ModuleList()
            num_heads = 4
            
            # 第一层
            self.gnn_layers.append(MultiHeadGATLayer(
                in_dim, hidden_dim, num_heads, dropout, concat=True
            ))
            
            # 中间层
            for _ in range(num_layers - 1):
                self.gnn_layers.append(MultiHeadGATLayer(
                    hidden_dim * num_heads, hidden_dim, num_heads, dropout, concat=True
                ))
            
            self.classifier = nn.Linear(hidden_dim * num_heads, num_classes)
            
        else:  # GCN
            # 简单的GCN实现
            self.gnn_layers = nn.ModuleList()
            
            # 第一层
            self.gnn_layers.append(nn.Linear(in_dim, hidden_dim))
            
            # 中间层
            for _ in range(num_layers - 1):
                self.gnn_layers.append(nn.Linear(hidden_dim, hidden_dim))
            
            self.classifier = nn.Linear(hidden_dim, num_classes)
            
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, node_features, adj_matrix, target_node_idx):
        """
        node_features: [N, in_dim] 节点特征
        adj_matrix: [N, N] 邻接矩阵
        target_node_idx: int 目标节点索引
        """
        h = node_features
        
        if self.gnn_type == 'GAT':
            # GAT前向传播
            for gat_layer in self.gnn_layers:
                h = gat_layer(h, adj_matrix)
        else:
            # GCN前向传播
            for i, linear_layer in enumerate(self.gnn_layers):
                # 简单的图卷积: A * H * W
                h = torch.mm(adj_matrix, h)  # 聚合邻居特征
                h = linear_layer(h)  # 线性变换
                
                if i < len(self.gnn_layers) - 1:
                    h = F.relu(h)
                    h = self.dropout(h)
        
        # 只对目标节点进行分类
        target_logits = self.classifier(h[target_node_idx:target_node_idx+1])
        return target_logits

class LightpathImpactDataGenerator:
    """光路影响数据生成器 - PyTorch原生版本"""
    
    def __init__(self):
        self.create_japan_network()
        
    def create_japan_network(self):
        """创建真实的日本网络拓扑"""
        # 14节点日本网络的邻接信息
        edges = [
            (0,1), (0,2), (1,3), (2,4), (3,4), (3,5), (4,5), (4,8), (4,6),
            (5,7), (6,7), (6,11), (7,9), (8,11), (11,9), (9,10), (8,13),
            (11,13), (11,10), (11,12), (13,12), (10,12), (13,11), (10,12)
        ]
        
        # 创建邻接矩阵
        self.num_nodes = 14
        self.adj_matrix = torch.zeros(self.num_nodes, self.num_nodes)
        
        for i, j in edges:
            self.adj_matrix[i, j] = 1.0
            self.adj_matrix[j, i] = 1.0  # 无向图
        
        # 添加自环
        self.adj_matrix += torch.eye(self.num_nodes)
        
        # 链路长度矩阵
        self.link_length = np.array([
            [0,160,240,0,0,0,0,0,0,0,0,0,0,0],
            [160,0,0,240,0,0,0,0,0,0,0,0,0,0],
            [240,0,0,0,240,0,0,0,0,0,0,0,0,0],
            [0,240,0,0,80,40,0,0,0,0,0,0,0,0],
            [0,0,240,80,0,40,80,0,240,0,0,0,0,0],
            [0,0,0,40,40,0,0,160,0,0,0,0,0,0],
            [0,0,0,0,80,0,0,80,0,0,0,240,0,0],
            [0,0,0,0,0,160,80,0,0,160,0,0,0,0],
            [0,0,0,0,240,0,0,0,0,0,0,240,0,240],
            [0,0,0,0,0,0,0,160,0,0,40,40,0,0],
            [0,0,0,0,0,0,0,0,0,40,0,40,320,0],
            [0,0,0,0,0,0,240,0,240,40,40,0,320,240],
            [0,0,0,0,0,0,0,0,0,0,320,320,0,160],
            [0,0,0,0,0,0,0,0,240,0,0,240,160,0]
        ])
        
        print(f"🌐 创建日本网络: {self.num_nodes}节点, {int(self.adj_matrix.sum()/2)}边")
        
    def find_shortest_path(self, src, dst):
        """使用简单的BFS寻找最短路径"""
        if src == dst:
            return [src]
        
        queue = [(src, [src])]
        visited = set([src])
        
        while queue:
            node, path = queue.pop(0)
            
            # 查找邻居
            neighbors = torch.nonzero(self.adj_matrix[node] > 0).flatten().tolist()
            
            for neighbor in neighbors:
                if neighbor == node:  # 跳过自环
                    continue
                    
                if neighbor == dst:
                    return path + [neighbor]
                
                if neighbor not in visited:
                    visited.add(neighbor)
                    queue.append((neighbor, path + [neighbor]))
        
        # 如果没找到路径，返回直连
        return [src, dst]
        
    def generate_lightpath_scenarios(self, num_scenarios: int = 1000):
        """生成光路影响场景数据"""
        print(f"🔄 生成 {num_scenarios} 个光路影响场景...")
        
        scenarios = []
        
        for i in range(num_scenarios):
            if i % 200 == 0:
                print(f"   进度: {i}/{num_scenarios}")
            
            # 随机选择新光路和目标光路
            nodes = list(range(self.num_nodes))
            
            # 新光路：源节点和目标节点
            new_src = np.random.choice(nodes)
            new_dst = np.random.choice([n for n in nodes if n != new_src])
            
            # 目标光路：源节点和目标节点  
            target_src = np.random.choice(nodes)
            target_dst = np.random.choice([n for n in nodes if n != target_src])
            
            # 计算路径
            new_path = self.find_shortest_path(new_src, new_dst)
            target_path = self.find_shortest_path(target_src, target_dst)
            
            # 计算影响
            is_affected = self._calculate_impact(new_path, target_path, new_src, new_dst, target_src, target_dst)
            
            # 生成特征
            scenario_data = {
                'new_lightpath': {'src': new_src, 'dst': new_dst, 'path': new_path},
                'target_lightpath': {'src': target_src, 'dst': target_dst, 'path': target_path},
                'full_graph_features': self._generate_full_graph_features(),
                'subgraph': self._extract_subgraph(new_path, target_path),
                'label': 1 if is_affected else 0,
                'target_node': target_src
            }
            
            scenarios.append(scenario_data)
        
        print(f"✅ 场景生成完成! 总数: {len(scenarios)}")
        
        # 统计标签分布
        affected_count = sum(1 for s in scenarios if s['label'] == 1)
        print(f"   受影响: {affected_count} ({affected_count/len(scenarios)*100:.1f}%)")
        print(f"   未受影响: {len(scenarios)-affected_count} ({(len(scenarios)-affected_count)/len(scenarios)*100:.1f}%)")
        
        return scenarios
    
    def _calculate_impact(self, new_path, target_path, new_src, new_dst, target_src, target_dst):
        """计算光路影响"""
        # 1. 路径重叠
        new_edges = set((min(new_path[i], new_path[i+1]), max(new_path[i], new_path[i+1])) 
                       for i in range(len(new_path)-1))
        target_edges = set((min(target_path[i], target_path[i+1]), max(target_path[i], target_path[i+1])) 
                          for i in range(len(target_path)-1))
        
        path_overlap = len(new_edges.intersection(target_edges)) / max(len(new_edges.union(target_edges)), 1)
        
        # 2. 节点距离
        node_distance = min(
            abs(new_src - target_src), abs(new_src - target_dst),
            abs(new_dst - target_src), abs(new_dst - target_dst)
        )
        
        # 3. 波长距离（模拟）
        new_wavelength = np.random.randint(1, 81)
        target_wavelength = np.random.randint(1, 81)
        wavelength_distance = abs(new_wavelength - target_wavelength)
        
        # 4. 功率差异（模拟）
        new_power = np.random.uniform(-3, 3)
        target_power = np.random.uniform(-3, 3)
        power_diff = abs(new_power - target_power)
        
        # 综合判断影响
        impact_score = (
            path_overlap * 0.4 +                    # 路径重叠权重最高
            (1 / (node_distance + 1)) * 0.3 +       # 节点距离越近影响越大
            (1 / (wavelength_distance + 1)) * 0.2 + # 波长距离越近影响越大
            (power_diff / 6) * 0.1                  # 功率差异影响
        )
        
        return impact_score > 0.3  # 阈值判断
    
    def _generate_full_graph_features(self):
        """生成全图节点特征"""
        # 节点特征：[功率, 负载, 中心性, 度数]
        features = []
        degrees = self.adj_matrix.sum(dim=1) - 1  # 减去自环
        max_degree = degrees.max()
        
        for i in range(self.num_nodes):
            power = np.random.uniform(-3, 3)  # 发射功率
            load = np.random.uniform(0, 1)    # 节点负载
            centrality = degrees[i].item() / max_degree if max_degree > 0 else 0  # 归一化度数
            betweenness = np.random.uniform(0, 1)  # 模拟中介中心性
            
            features.append([power, load, centrality, betweenness])
        
        return torch.tensor(features, dtype=torch.float32)
    
    def _extract_subgraph(self, new_path, target_path):
        """提取相关子图"""
        # 1. 获取相关节点
        relevant_nodes = set(new_path + target_path)
        
        # 2. 添加1-hop邻居
        for node in list(relevant_nodes):
            neighbors = torch.nonzero(self.adj_matrix[node] > 0).flatten().tolist()
            relevant_nodes.update(neighbors[:3])  # 最多添加3个邻居
        
        relevant_nodes = sorted(list(relevant_nodes))
        
        # 3. 创建节点映射
        node_mapping = {old_id: new_id for new_id, old_id in enumerate(relevant_nodes)}
        
        # 4. 提取子图邻接矩阵
        subgraph_size = len(relevant_nodes)
        subgraph_adj = torch.zeros(subgraph_size, subgraph_size)
        
        for i, old_i in enumerate(relevant_nodes):
            for j, old_j in enumerate(relevant_nodes):
                subgraph_adj[i, j] = self.adj_matrix[old_i, old_j]
        
        # 5. 生成子图节点特征
        subgraph_features = []
        for old_node_id in relevant_nodes:
            # 特征：[是否新光路源, 是否新光路目标, 是否目标光路源, 是否目标光路目标, 
            #       功率, 度数, 路径重叠指示]
            features = [
                1.0 if old_node_id in new_path[:1] else 0.0,      # 新光路源
                1.0 if old_node_id in new_path[-1:] else 0.0,     # 新光路目标
                1.0 if old_node_id in target_path[:1] else 0.0,   # 目标光路源
                1.0 if old_node_id in target_path[-1:] else 0.0,  # 目标光路目标
                np.random.uniform(-3, 3),                          # 功率
                float(self.adj_matrix[old_node_id].sum() - 1) / 10.0,  # 归一化度数
                1.0 if old_node_id in (new_path + target_path) else 0.0   # 是否在关键路径上
            ]
            subgraph_features.append(features)
        
        subgraph_data = {
            'adj_matrix': subgraph_adj,
            'features': torch.tensor(subgraph_features, dtype=torch.float32),
            'node_mapping': node_mapping,
            'relevant_nodes': relevant_nodes
        }
        
        return subgraph_data

def train_and_compare_models():
    """训练并对比子图GAT和全图GNN"""
    print("🏁 PyTorch原生 子图GAT vs 普通GNN对比实验")
    print("=" * 60)
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🔧 使用设备: {device}")
    
    # 生成数据
    data_generator = LightpathImpactDataGenerator()
    scenarios = data_generator.generate_lightpath_scenarios(num_scenarios=1000)
    
    # 数据划分
    train_scenarios, test_scenarios = train_test_split(scenarios, test_size=0.3, random_state=42,
                                                     stratify=[s['label'] for s in scenarios])
    
    print(f"📊 数据划分: 训练{len(train_scenarios)}, 测试{len(test_scenarios)}")
    
    # 模型参数
    hidden_dim = 32
    num_layers = 2
    num_classes = 2
    dropout = 0.1
    
    results = {}
    
    # ==================== 子图GAT训练 ====================
    print(f"\n🎯 训练PyTorch原生子图GAT...")
    
    subgraph_model = PyTorchSubgraphGAT(
        in_dim=7,  # 子图节点特征维度
        hidden_dim=hidden_dim,
        num_heads=4,
        num_layers=num_layers,
        num_classes=num_classes,
        dropout=dropout
    ).to(device)
    
    print(f"   子图GAT参数: {sum(p.numel() for p in subgraph_model.parameters())}")
    
    # 准备子图数据
    subgraph_train_data = []
    subgraph_test_data = []
    
    for scenario in train_scenarios:
        subgraph_data = scenario['subgraph']
        subgraph_train_data.append((subgraph_data['features'], subgraph_data['adj_matrix'], scenario['label']))
    
    for scenario in test_scenarios:
        subgraph_data = scenario['subgraph']
        subgraph_test_data.append((subgraph_data['features'], subgraph_data['adj_matrix'], scenario['label']))
    
    # 训练子图GAT
    subgraph_results = train_subgraph_model(subgraph_model, subgraph_train_data, subgraph_test_data, 
                                          device, "PyTorchSubgraphGAT", epochs=30)
    results['PyTorchSubgraphGAT'] = subgraph_results
    
    # ==================== 全图GCN训练 ====================
    print(f"\n🎯 训练PyTorch原生全图GCN...")
    
    fullgraph_gcn = PyTorchFullGraphGNN(
        in_dim=4,  # 全图节点特征维度
        hidden_dim=hidden_dim,
        num_layers=num_layers,
        num_classes=num_classes,
        gnn_type='GCN',
        dropout=dropout
    ).to(device)
    
    print(f"   全图GCN参数: {sum(p.numel() for p in fullgraph_gcn.parameters())}")
    
    # 准备全图数据
    fullgraph_train_data = []
    fullgraph_test_data = []
    
    for scenario in train_scenarios:
        fullgraph_train_data.append((scenario['full_graph_features'], data_generator.adj_matrix, 
                                   scenario['target_node'], scenario['label']))
    
    for scenario in test_scenarios:
        fullgraph_test_data.append((scenario['full_graph_features'], data_generator.adj_matrix, 
                                  scenario['target_node'], scenario['label']))
    
    # 训练全图GCN
    fullgraph_gcn_results = train_fullgraph_model(fullgraph_gcn, fullgraph_train_data, fullgraph_test_data, 
                                                device, "PyTorchFullGraphGCN", epochs=30)
    results['PyTorchFullGraphGCN'] = fullgraph_gcn_results
    
    # ==================== 全图GAT训练 ====================
    print(f"\n🎯 训练PyTorch原生全图GAT...")
    
    fullgraph_gat = PyTorchFullGraphGNN(
        in_dim=4,
        hidden_dim=hidden_dim,
        num_layers=num_layers,
        num_classes=num_classes,
        gnn_type='GAT',
        dropout=dropout
    ).to(device)
    
    print(f"   全图GAT参数: {sum(p.numel() for p in fullgraph_gat.parameters())}")
    
    # 训练全图GAT
    fullgraph_gat_results = train_fullgraph_model(fullgraph_gat, fullgraph_train_data, fullgraph_test_data, 
                                                device, "PyTorchFullGraphGAT", epochs=30)
    results['PyTorchFullGraphGAT'] = fullgraph_gat_results
    
    # ==================== 结果对比 ====================
    print(f"\n📊 实验结果对比:")
    print("=" * 80)
    print(f"{'方法':<20} {'测试准确率':<12} {'精确率':<10} {'召回率':<10} {'F1分数':<10} {'训练时间(s)':<12}")
    print("-" * 80)
    
    for method_name, result in results.items():
        print(f"{method_name:<20} {result['test_accuracy']:<12.4f} {result['precision']:<10.4f} "
              f"{result['recall']:<10.4f} {result['f1_score']:<10.4f} {result['train_time']:<12.2f}")
    
    # 保存详细结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    final_results = {
        'experiment_info': {
            'timestamp': timestamp,
            'task': 'Lightpath Impact Classification (PyTorch Native)',
            'num_scenarios': len(scenarios),
            'train_samples': len(train_scenarios),
            'test_samples': len(test_scenarios),
            'implementation': 'PyTorch Native (No DGL)',
            'network_topology': '14-node Japanese Network'
        },
        'results': results
    }
    
    results_file = f'pytorch_native_subgraph_gat_results_{timestamp}.json'
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(final_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📁 详细结果保存至: {results_file}")
    
    return final_results

def train_subgraph_model(model, train_data, test_data, device, model_name, epochs=30):
    """训练子图模型"""
    print(f"   开始训练{model_name}...")
    
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-5)
    criterion = nn.CrossEntropyLoss()
    
    start_time = time.time()
    
    # 训练循环
    for epoch in range(epochs):
        model.train()
        total_loss = 0
        
        for features, adj_matrix, label in train_data:
            features = features.to(device)
            adj_matrix = adj_matrix.to(device)
            label = torch.tensor([label], dtype=torch.long).to(device)
            
            logits = model(features, adj_matrix)
            loss = criterion(logits, label)
            
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
        
        if epoch % 10 == 0:
            avg_loss = total_loss / len(train_data)
            print(f"     Epoch {epoch}: Loss = {avg_loss:.4f}")
    
    train_time = time.time() - start_time
    
    # 测试评估
    model.eval()
    predictions = []
    true_labels = []
    
    with torch.no_grad():
        for features, adj_matrix, label in test_data:
            features = features.to(device)
            adj_matrix = adj_matrix.to(device)
            
            logits = model(features, adj_matrix)
            pred = torch.argmax(logits, dim=1).cpu().item()
            
            predictions.append(pred)
            true_labels.append(label)
    
    # 计算性能指标
    accuracy = accuracy_score(true_labels, predictions)
    precision, recall, f1, _ = precision_recall_fscore_support(true_labels, predictions, average='weighted')
    
    print(f"   {model_name} 测试准确率: {accuracy:.4f}")
    
    return {
        'test_accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1,
        'train_time': train_time,
        'predictions': predictions,
        'true_labels': true_labels
    }

def train_fullgraph_model(model, train_data, test_data, device, model_name, epochs=30):
    """训练全图模型"""
    print(f"   开始训练{model_name}...")
    
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-5)
    criterion = nn.CrossEntropyLoss()
    
    start_time = time.time()
    
    # 训练循环
    for epoch in range(epochs):
        model.train()
        total_loss = 0
        
        for features, adj_matrix, target_node_idx, label in train_data:
            features = features.to(device)
            adj_matrix = adj_matrix.to(device)
            label = torch.tensor([label], dtype=torch.long).to(device)
            
            logits = model(features, adj_matrix, target_node_idx)
            loss = criterion(logits, label)
            
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
        
        if epoch % 10 == 0:
            avg_loss = total_loss / len(train_data)
            print(f"     Epoch {epoch}: Loss = {avg_loss:.4f}")
    
    train_time = time.time() - start_time
    
    # 测试评估
    model.eval()
    predictions = []
    true_labels = []
    
    with torch.no_grad():
        for features, adj_matrix, target_node_idx, label in test_data:
            features = features.to(device)
            adj_matrix = adj_matrix.to(device)
            
            logits = model(features, adj_matrix, target_node_idx)
            pred = torch.argmax(logits, dim=1).cpu().item()
            
            predictions.append(pred)
            true_labels.append(label)
    
    # 计算性能指标
    accuracy = accuracy_score(true_labels, predictions)
    precision, recall, f1, _ = precision_recall_fscore_support(true_labels, predictions, average='weighted')
    
    print(f"   {model_name} 测试准确率: {accuracy:.4f}")
    
    return {
        'test_accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1,
        'train_time': train_time,
        'predictions': predictions,
        'true_labels': true_labels
    }

if __name__ == "__main__":
    results = train_and_compare_models()