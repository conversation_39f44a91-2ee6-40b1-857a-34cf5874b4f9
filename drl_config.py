"""
DRL QoT优化系统配置文件
"""

import numpy as np
from dataclasses import dataclass
from typing import Dict, List

@dataclass
class DRLConfig:
    """DRL训练配置"""
    
    # 环境配置
    max_wavelengths: int = 80
    max_lightpaths: int = 50
    max_episodes: int = 1000
    
    # 网络文件路径
    network_file: str = "Data/CORONET_Global_Topology.json"
    equipment_file: str = "Data/default_edfa_config.json"
    
    # DQN配置
    learning_rate: float = 1e-4
    gamma: float = 0.99
    epsilon_start: float = 1.0
    epsilon_end: float = 0.01
    epsilon_decay: float = 0.995
    
    # 训练配置
    batch_size: int = 32
    memory_size: int = 10000
    target_update_freq: int = 10
    save_interval: int = 100
    
    # 奖励权重
    reward_weights: Dict[str, float] = None
    
    def __post_init__(self):
        if self.reward_weights is None:
            self.reward_weights = {
                'qot_success': 10.0,      # QoT达标奖励
                'qot_safety': 5.0,        # 全网QoT安全域保障
                'blocking_penalty': -20.0, # 阻塞惩罚
                'efficiency': 1.0,        # 资源利用效率
                'nonlinear_penalty': -2.0, # 非线性效应惩罚
                'power_efficiency': 1.5   # 功率效率奖励
            }

@dataclass
class PhysicsConfig:
    """物理层配置"""
    
    # 光纤参数
    fiber_loss_db_per_km: float = 0.2
    dispersion_ps_per_nm_per_km: float = 17.0
    nonlinear_coefficient: float = 1.3e-3
    effective_area_m2: float = 80e-12
    
    # EDFA参数
    edfa_noise_figure_db: float = 4.5
    edfa_gain_db: float = 20.0
    
    # 调制格式参数
    modulation_formats: Dict[str, Dict] = None
    
    def __post_init__(self):
        if self.modulation_formats is None:
            self.modulation_formats = {
                'QPSK': {
                    'osnr_threshold_db': 12.0,
                    'spectral_efficiency': 2.0,
                    'penalty_db': 0.0
                },
                '8QAM': {
                    'osnr_threshold_db': 15.0,
                    'spectral_efficiency': 3.0,
                    'penalty_db': 1.0
                },
                '16QAM': {
                    'osnr_threshold_db': 18.0,
                    'spectral_efficiency': 4.0,
                    'penalty_db': 2.0
                },
                '32QAM': {
                    'osnr_threshold_db': 21.0,
                    'spectral_efficiency': 5.0,
                    'penalty_db': 3.0
                }
            }

@dataclass
class NetworkConfig:
    """网络拓扑配置"""
    
    # 日本光网络拓扑（14节点）
    japan_topology: Dict = None
    
    # CORONET全球拓扑
    coronet_topology: Dict = None
    
    def __post_init__(self):
        # 日本14节点网络拓扑
        self.japan_topology = {
            'nodes': list(range(14)),
            'links': [
                (0, 1, {'length_km': 533, 'fiber_type': 'SMF'}),
                (0, 2, {'length_km': 699, 'fiber_type': 'SMF'}),
                (1, 3, {'length_km': 200, 'fiber_type': 'SMF'}),
                (2, 4, {'length_km': 150, 'fiber_type': 'SMF'}),
                (3, 5, {'length_km': 300, 'fiber_type': 'SMF'}),
                (4, 6, {'length_km': 250, 'fiber_type': 'SMF'}),
                (5, 7, {'length_km': 180, 'fiber_type': 'SMF'}),
                (6, 8, {'length_km': 220, 'fiber_type': 'SMF'}),
                (7, 9, {'length_km': 160, 'fiber_type': 'SMF'}),
                (8, 10, {'length_km': 190, 'fiber_type': 'SMF'}),
                (9, 11, {'length_km': 140, 'fiber_type': 'SMF'}),
                (10, 12, {'length_km': 170, 'fiber_type': 'SMF'}),
                (11, 13, {'length_km': 120, 'fiber_type': 'SMF'}),
                (12, 13, {'length_km': 100, 'fiber_type': 'SMF'})
            ]
        }

# 全局配置实例
drl_config = DRLConfig()
physics_config = PhysicsConfig()
network_config = NetworkConfig()

def get_optimized_reward_weights() -> Dict[str, float]:
    """
    获取优化的奖励权重
    基于光网络QoT优化的最佳实践
    """
    return {
        # 主要目标：QoT达标和安全域保障
        'qot_success': 15.0,          # 提高QoT达标奖励
        'qot_safety': 8.0,            # 强化安全域保障
        'blocking_penalty': -25.0,     # 增加阻塞惩罚
        
        # 物理层优化
        'nonlinear_penalty': -3.0,     # 非线性效应惩罚
        'power_efficiency': 2.0,       # 功率效率奖励
        'spectral_efficiency': 1.5,    # 频谱效率奖励
        
        # 网络级优化
        'load_balancing': 1.0,         # 负载均衡奖励
        'path_diversity': 0.5,         # 路径多样性奖励
        'crosstalk_penalty': -2.5      # 串扰惩罚
    }

def get_advanced_training_schedule() -> List[Dict]:
    """
    获取高级训练计划
    分阶段训练策略
    """
    return [
        {
            'phase': 'exploration',
            'episodes': 200,
            'epsilon_start': 1.0,
            'epsilon_end': 0.3,
            'learning_rate': 1e-3,
            'focus': 'exploration_and_basic_qot'
        },
        {
            'phase': 'exploitation',
            'episodes': 300,
            'epsilon_start': 0.3,
            'epsilon_end': 0.1,
            'learning_rate': 5e-4,
            'focus': 'qot_safety_domain'
        },
        {
            'phase': 'fine_tuning',
            'episodes': 500,
            'epsilon_start': 0.1,
            'epsilon_end': 0.01,
            'learning_rate': 1e-4,
            'focus': 'efficiency_optimization'
        }
    ]

def validate_config() -> bool:
    """验证配置的有效性"""
    try:
        # 检查文件路径
        import os
        if not os.path.exists(drl_config.network_file):
            print(f"⚠️  网络文件不存在: {drl_config.network_file}")
            return False
        
        if not os.path.exists(drl_config.equipment_file):
            print(f"⚠️  设备文件不存在: {drl_config.equipment_file}")
            return False
        
        # 检查参数合理性
        if drl_config.learning_rate <= 0 or drl_config.learning_rate > 1:
            print(f"⚠️  学习率不合理: {drl_config.learning_rate}")
            return False
        
        if drl_config.gamma <= 0 or drl_config.gamma > 1:
            print(f"⚠️  折扣因子不合理: {drl_config.gamma}")
            return False
        
        print("✅ 配置验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置验证失败: {e}")
        return False

def print_config_summary():
    """打印配置摘要"""
    print("=" * 60)
    print("🔧 DRL QoT优化系统配置摘要")
    print("=" * 60)
    
    print(f"📊 训练配置:")
    print(f"   - 最大训练轮数: {drl_config.max_episodes}")
    print(f"   - 学习率: {drl_config.learning_rate}")
    print(f"   - 折扣因子: {drl_config.gamma}")
    print(f"   - 批次大小: {drl_config.batch_size}")
    
    print(f"\n🌐 网络配置:")
    print(f"   - 最大波长数: {drl_config.max_wavelengths}")
    print(f"   - 最大光路数: {drl_config.max_lightpaths}")
    print(f"   - 网络文件: {drl_config.network_file}")
    
    print(f"\n⚡ 物理层配置:")
    print(f"   - 光纤损耗: {physics_config.fiber_loss_db_per_km} dB/km")
    print(f"   - 色散: {physics_config.dispersion_ps_per_nm_per_km} ps/nm/km")
    print(f"   - EDFA噪声系数: {physics_config.edfa_noise_figure_db} dB")
    
    print(f"\n🎯 奖励权重:")
    for key, value in drl_config.reward_weights.items():
        print(f"   - {key}: {value}")
    
    print("=" * 60)

if __name__ == "__main__":
    print_config_summary()
    validate_config()
