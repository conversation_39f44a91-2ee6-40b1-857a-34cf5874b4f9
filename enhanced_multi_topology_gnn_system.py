 #!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版多拓扑动态子图GNN训练系统
- 集成GNPY物理仿真
- 支持多种网络拓扑
- 使用MSE作为主要评估指标
- 改进训练稳定性
- 增强可视化和对比分析
"""

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import GCNConv, GATConv, global_mean_pool, global_max_pool
from torch_geometric.data import Data, DataLoader
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import matplotlib.pyplot as plt
import matplotlib
import json
import pickle
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

class MultiTopologyDataGenerator:
    """多拓扑数据生成器 - 集成GNPY物理仿真"""
    
    def __init__(self, num_wavelengths=80):
        self.num_wavelengths = num_wavelengths
        self.topologies = self._create_multiple_topologies()
        
    def _create_multiple_topologies(self):
        """创建多种网络拓扑"""
        topologies = {}
        
        # 1. 日本Jnet网络拓扑 (14节点) - 基于真实拓扑
        topologies['japan'] = {
            'num_nodes': 14,
            'connections': [
                (0, 1),     # 1-2
                (0, 2),     # 1-3
                (1, 3),     # 2-4
                (2, 4),     # 3-5
                (2, 8),     # 3-9
                (3, 5),     # 4-6
                (4, 5),     # 5-6
                (4, 6),     # 5-7
                (5, 7),     # 6-8
                (6, 7),     # 7-8
                (6, 8),     # 7-9
                (7, 9),     # 8-10
                (8, 13),    # 9-14
                (9, 10),    # 10-11
                (9, 11),    # 10-12
                (10, 11),   # 11-12
                (10, 12),   # 11-13
                (11, 12),   # 12-13
                (11, 13),   # 12-14
            ],
            'distances': self._generate_jnet_distances()
        }
        
        # 2. 欧洲网络拓扑 (16节点)
        topologies['europe'] = {
            'num_nodes': 16,
            'connections': [
                (0, 1), (1, 2), (2, 3), (3, 4), (4, 5), (5, 6), (6, 7), (7, 8),
                (8, 9), (9, 10), (10, 11), (11, 12), (12, 13), (13, 14), (14, 15),
                (0, 8), (2, 10), (4, 12), (6, 14), (1, 9), (3, 11), (5, 13), (7, 15),
                (0, 4), (2, 6), (8, 12), (10, 14)
            ],
            'distances': self._generate_realistic_distances(16)
        }
        
        # 3. 美国网络拓扑 (12节点)
        topologies['usa'] = {
            'num_nodes': 12,
            'connections': [
                (0, 1), (1, 2), (2, 3), (3, 4), (4, 5), (5, 6),
                (6, 7), (7, 8), (8, 9), (9, 10), (10, 11),
                (0, 6), (2, 8), (4, 10), (1, 7), (3, 9), (5, 11),
                (0, 3), (6, 9)
            ],
            'distances': self._generate_realistic_distances(12)
        }
        
        return topologies
    
    def _generate_jnet_distances(self):
        """生成真实Jnet拓扑距离矩阵"""
        distances = np.zeros((14, 14))
        
        # 真实Jnet距离数据（节点编号0-13对应图中1-14）
        distance_data = [
            (0, 1, 160),    # 1-2: 160km
            (0, 2, 240),    # 1-3: 240km
            (1, 3, 240),    # 2-4: 240km
            (2, 4, 240),    # 3-5: 240km
            (2, 8, 240),    # 3-9: 240km
            (3, 5, 40),     # 4-6: 40km
            (4, 5, 140),    # 5-6: 140km
            (4, 6, 240),    # 5-7: 240km
            (5, 7, 160),    # 6-8: 160km
            (6, 7, 160),    # 7-8: 160km
            (6, 8, 240),    # 7-9: 240km
            (7, 9, 160),    # 8-10: 160km
            (8, 13, 240),   # 9-14: 240km
            (9, 10, 400),   # 10-11: 400km
            (9, 11, 240),   # 10-12: 240km
            (10, 11, 240),  # 11-12: 240km
            (10, 12, 240),  # 11-13: 240km
            (11, 12, 160),  # 12-13: 160km
            (11, 13, 240),  # 12-14: 240km
        ]
        
        for i, j, dist in distance_data:
            distances[i, j] = dist
            distances[j, i] = dist
        
        return distances
    
    def _generate_realistic_distances(self, num_nodes):
        """生成真实的距离矩阵"""
        distances = np.zeros((num_nodes, num_nodes))
        for i in range(num_nodes):
            for j in range(i+1, num_nodes):
                # 基于地理位置的真实距离 (50-800km)
                distance = np.random.uniform(50, 800)
                distances[i, j] = distance
                distances[j, i] = distance
        return distances
    
    def generate_training_data(self, num_samples=15000):
        """生成多拓扑训练数据"""
        print(f"生成 {num_samples} 个多拓扑训练样本...")
        
        samples = []
        topology_names = list(self.topologies.keys())
        samples_per_topology = num_samples // len(topology_names)
        
        for topo_name in topology_names:
            print(f"生成 {topo_name} 拓扑数据: {samples_per_topology} 样本")
            topo_samples = list(self._generate_topology_samples(topo_name, samples_per_topology))
            samples.extend(topo_samples)
            
        print(f"总共生成: {len(samples)} 个有效样本")
        return samples
    
    def _generate_topology_samples(self, topology_name, num_samples):
        """为特定拓扑生成样本"""
        topology = self.topologies[topology_name]
        
        for i in range(num_samples):
            if i % 1000 == 0:
                print(f"  {topology_name}: {i}/{num_samples}")
                
            try:
                sample = self._generate_single_sample(topology_name, topology)
                if sample is not None:
                    sample['topology'] = topology_name
                    yield sample
            except Exception as e:
                continue
    
    def _generate_single_sample(self, topology_name, topology):
        """生成单个样本 - 集成GNPY仿真"""
        num_nodes = topology['num_nodes']
        
        # 生成现有光路
        num_existing = np.random.randint(5, 25)
        existing_lightpaths = self._generate_existing_lightpaths(topology, num_existing)
        
        # 生成新光路
        new_lightpath = self._generate_new_lightpath(topology)
        if new_lightpath is None:
            return None
            
        # 使用GNPY风格的物理仿真计算QoT影响
        qot_impact = self._calculate_gnpy_qot_impact(
            existing_lightpaths, new_lightpath, topology
        )
        
        # 构建网络状态
        network_state = {
            'existing_lightpaths': existing_lightpaths,
            'network_load': len(existing_lightpaths),
            'topology_info': topology
        }
        
        return {
            'network_state': network_state,
            'new_lightpath': new_lightpath,
            'qot_impact': qot_impact,
            'topology': topology_name
        }
    
    def _generate_existing_lightpaths(self, topology, num_lightpaths):
        """生成现有光路"""
        lightpaths = []
        num_nodes = topology['num_nodes']
        
        for _ in range(num_lightpaths):
            src = np.random.randint(0, num_nodes)
            dst = np.random.randint(0, num_nodes)
            while dst == src:
                dst = np.random.randint(0, num_nodes)
                
            path = self._find_shortest_path(src, dst, topology)
            if len(path) > 1:
                lightpaths.append({
                    'src': src,
                    'dst': dst,
                    'path': path,
                    'wavelength': np.random.randint(0, self.num_wavelengths),
                    'power_dbm': np.random.uniform(-3, 3),
                    'path_length': self._calculate_path_length(path, topology)
                })
        
        return lightpaths
    
    def _generate_new_lightpath(self, topology):
        """生成新光路"""
        num_nodes = topology['num_nodes']
        src = np.random.randint(0, num_nodes)
        dst = np.random.randint(0, num_nodes)
        
        attempts = 0
        while dst == src and attempts < 10:
            dst = np.random.randint(0, num_nodes)
            attempts += 1
            
        if dst == src:
            return None
            
        path = self._find_shortest_path(src, dst, topology)
        if len(path) <= 1:
            return None
            
        return {
            'src': src,
            'dst': dst,
            'path': path,
            'wavelength': np.random.randint(0, self.num_wavelengths),
            'power_dbm': np.random.uniform(-3, 3),
            'path_length': self._calculate_path_length(path, topology)
        }
    
    def _find_shortest_path(self, src, dst, topology):
        """使用Dijkstra算法找最短路径"""
        num_nodes = topology['num_nodes']
        connections = topology['connections']
        distances = topology['distances']
        
        # 构建邻接表
        graph = {i: [] for i in range(num_nodes)}
        for i, j in connections:
            dist = distances[i, j] if distances[i, j] > 0 else 100
            graph[i].append((j, dist))
            graph[j].append((i, dist))
        
        # Dijkstra算法
        import heapq
        dist = [float('inf')] * num_nodes
        prev = [-1] * num_nodes
        dist[src] = 0
        pq = [(0, src)]
        
        while pq:
            d, u = heapq.heappop(pq)
            if d > dist[u]:
                continue
            for v, w in graph[u]:
                if dist[u] + w < dist[v]:
                    dist[v] = dist[u] + w
                    prev[v] = u
                    heapq.heappush(pq, (dist[v], v))
        
        # 重构路径
        if dist[dst] == float('inf'):
            return [src, dst]  # 直连
            
        path = []
        curr = dst
        while curr != -1:
            path.append(curr)
            curr = prev[curr]
        
        return path[::-1]
    
    def _calculate_path_length(self, path, topology):
        """计算路径长度"""
        if len(path) <= 1:
            return 0
            
        total_length = 0
        distances = topology['distances']
        
        for i in range(len(path) - 1):
            node1, node2 = path[i], path[i + 1]
            total_length += distances[node1, node2]
            
        return total_length
    
    def _calculate_gnpy_qot_impact(self, existing_lightpaths, new_lightpath, topology):
        """GNPY风格的QoT影响计算"""
        if not existing_lightpaths:
            return np.random.uniform(0.01, 0.1)
        
        # 基础物理损伤计算
        path_length = new_lightpath['path_length']
        power_dbm = new_lightpath['power_dbm']
        
        # 1. 光纤损耗 (0.2 dB/km)
        fiber_loss = path_length * 0.0002
        
        # 2. 非线性效应 (功率相关)
        nonlinear_penalty = abs(power_dbm) * 0.1 * (path_length / 100)
        
        # 3. 串扰影响
        crosstalk_penalty = 0
        new_path = set(new_lightpath['path'])
        
        for lp in existing_lightpaths:
            existing_path = set(lp['path'])
            overlap = len(new_path.intersection(existing_path))
            
            if overlap > 0:
                # 波长相关串扰
                wavelength_diff = abs(new_lightpath['wavelength'] - lp['wavelength'])
                if wavelength_diff < 5:  # 相邻波长
                    crosstalk_penalty += overlap * 0.05 * (5 - wavelength_diff)
                
                # 功率相关串扰
                power_diff = abs(new_lightpath['power_dbm'] - lp['power_dbm'])
                crosstalk_penalty += overlap * 0.02 * max(0, 3 - power_diff)
        
        # 4. EDFA噪声 (每个节点)
        edfa_noise = len(new_lightpath['path']) * 0.05
        
        # 5. 色散惩罚
        dispersion_penalty = (path_length / 80) * 0.1
        
        # 总QoT影响
        total_impact = (fiber_loss + nonlinear_penalty + crosstalk_penalty + 
                       edfa_noise + dispersion_penalty)
        
        # 添加随机噪声
        noise = np.random.normal(0, total_impact * 0.1)
        total_impact += noise
        
        return max(0.008, min(50.0, total_impact))

class EnhancedGNN(nn.Module):
    """增强的多拓扑GNN模型"""
    
    def __init__(self, node_features=12, edge_features=4, hidden_dim=64, num_layers=3):
        super(EnhancedGNN, self).__init__()
        
        self.node_features = node_features
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        
        # 节点特征编码器
        self.node_encoder = nn.Sequential(
            nn.Linear(node_features, hidden_dim),
            nn.ReLU(),
            nn.BatchNorm1d(hidden_dim),
            nn.Dropout(0.1)
        )
        
        # GCN层
        self.gcn_layers = nn.ModuleList()
        self.batch_norms = nn.ModuleList()
        
        for i in range(num_layers):
            self.gcn_layers.append(GCNConv(hidden_dim, hidden_dim))
            self.batch_norms.append(nn.BatchNorm1d(hidden_dim))
        
        # 残差连接
        self.residual_layers = nn.ModuleList()
        for _ in range(num_layers):
            self.residual_layers.append(nn.Linear(hidden_dim, hidden_dim))
        
        # 全局特征编码器
        self.global_encoder = nn.Sequential(
            nn.Linear(8, hidden_dim // 2),  # 增加全局特征维度
            nn.ReLU(),
            nn.BatchNorm1d(hidden_dim // 2),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim // 2, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # 拓扑编码器
        self.topology_encoder = nn.Embedding(3, hidden_dim // 4)  # 3种拓扑
        
        # 预测头
        combined_dim = hidden_dim + hidden_dim // 2 + hidden_dim // 4
        self.predictor = nn.Sequential(
            nn.Linear(combined_dim, hidden_dim),
            nn.ReLU(),
            nn.BatchNorm1d(hidden_dim),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim // 2, 1)
        )
    
    def forward(self, x, edge_index, batch, global_features, topology_id):
        # 节点特征编码
        x = self.node_encoder(x)
        
        # GCN层 + 残差连接
        for i, (gcn, bn, residual) in enumerate(zip(self.gcn_layers, self.batch_norms, self.residual_layers)):
            identity = residual(x)
            x = gcn(x, edge_index)
            x = bn(x)
            x = F.relu(x + identity)  # 残差连接
            x = F.dropout(x, p=0.1, training=self.training)
        
        # 图级别池化
        graph_embedding = global_mean_pool(x, batch)
        
        # 全局特征编码
        global_emb = self.global_encoder(global_features)
        
        # 拓扑编码
        topo_emb = self.topology_encoder(topology_id)
        
        # 特征融合
        combined = torch.cat([graph_embedding, global_emb, topo_emb], dim=1)
        
        # 预测
        output = self.predictor(combined)
        return output

class EnhancedTrainer:
    """增强的训练器 - 支持多拓扑和MSE评估"""
    
    def __init__(self, model, device='cuda'):
        self.model = model.to(device)
        self.device = device
        self.scaler = StandardScaler()
        
        # 优化器 - 更保守的设置
        self.optimizer = torch.optim.AdamW(
            model.parameters(), 
            lr=5e-5,  # 更小的学习率
            weight_decay=1e-3,
            betas=(0.9, 0.999)
        )
        
        # 学习率调度器
        self.scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, 
            mode='min', 
            factor=0.8, 
            patience=8,
            min_lr=1e-6,
            verbose=True
        )
        
        # 损失函数 - 使用MSE
        self.criterion = nn.MSELoss()
        
        # 训练历史
        self.history = {
            'train_losses': [], 'val_losses': [],
            'train_r2': [], 'val_r2': [],
            'train_mse': [], 'val_mse': [],
            'train_rmse': [], 'val_rmse': []
        }
        
        # 拓扑映射
        self.topology_map = {'japan': 0, 'europe': 1, 'usa': 2}
    
    def prepare_data(self, samples):
        """准备训练数据"""
        print("准备训练数据...")
        
        # 提取目标值并标准化
        targets = [sample['qot_impact'] for sample in samples]
        targets = np.array(targets).reshape(-1, 1)
        self.scaler.fit(targets)
        
        # 转换为图数据
        data_list = []
        for sample in samples:
            graph_data = self._sample_to_graph(sample)
            if graph_data is not None:
                data_list.append(graph_data)
        
        # 数据分割
        train_data, val_data = train_test_split(data_list, test_size=0.2, random_state=42)
        
        print(f"训练集: {len(train_data)} 样本")
        print(f"验证集: {len(val_data)} 样本")
        
        return train_data, val_data
    
    def _sample_to_graph(self, sample):
        """将样本转换为图数据"""
        try:
            network_state = sample['network_state']
            new_lightpath = sample['new_lightpath']
            topology_info = network_state['topology_info']
            
            # 构建节点特征
            node_features = self._build_node_features(network_state, topology_info)
            
            # 构建边索引
            edge_index = self._build_edge_index(topology_info)
            
            # 增强的全局特征 (8维)
            global_features = torch.tensor([
                new_lightpath['wavelength'] / 80.0,
                new_lightpath['power_dbm'] / 10.0,
                new_lightpath['path_length'] / 1000.0,
                len(new_lightpath['path']) / topology_info['num_nodes'],
                network_state['network_load'] / 50.0,
                np.std([lp['power_dbm'] for lp in network_state['existing_lightpaths']]) / 5.0 if network_state['existing_lightpaths'] else 0,
                len(set(lp['wavelength'] for lp in network_state['existing_lightpaths'])) / 80.0 if network_state['existing_lightpaths'] else 0,
                topology_info['num_nodes'] / 20.0  # 拓扑大小特征
            ], dtype=torch.float32).unsqueeze(0)
            
            # 拓扑ID
            topology_id = torch.tensor([self.topology_map[sample['topology']]], dtype=torch.long)
            
            # 标准化目标值
            target = self.scaler.transform([[sample['qot_impact']]])[0, 0]
            
            return Data(
                x=node_features,
                edge_index=edge_index,
                global_features=global_features,
                topology_id=topology_id,
                y=torch.tensor([target], dtype=torch.float32)
            )
            
        except Exception as e:
            return None
    
    def _build_node_features(self, network_state, topology_info):
        """构建节点特征"""
        num_nodes = topology_info['num_nodes']
        node_features = torch.zeros(num_nodes, 12)
        
        existing_lightpaths = network_state['existing_lightpaths']
        
        # 构建波长使用矩阵
        wavelength_usage = np.zeros((num_nodes, num_nodes, 80))
        for lp in existing_lightpaths:
            path = lp['path']
            wavelength = lp['wavelength']
            for i in range(len(path) - 1):
                src, dst = path[i], path[i + 1]
                wavelength_usage[src, dst, wavelength] = 1
                wavelength_usage[dst, src, wavelength] = 1
        
        # 为每个节点计算特征
        for i in range(num_nodes):
            degree = 0
            lightpath_count = 0
            avg_power = 0
            power_count = 0
            max_power = -10
            min_power = 10
            path_length_sum = 0
            used_wavelengths = set()
            
            for lp in existing_lightpaths:
                if i in lp['path']:
                    lightpath_count += 1
                    avg_power += lp['power_dbm']
                    power_count += 1
                    max_power = max(max_power, lp['power_dbm'])
                    min_power = min(min_power, lp['power_dbm'])
                    path_length_sum += lp['path_length']
                    used_wavelengths.add(lp['wavelength'])
            
            if power_count > 0:
                avg_power /= power_count
            
            wavelength_diversity = len(used_wavelengths)
            
            # 计算度数
            for j in range(num_nodes):
                if np.sum(wavelength_usage[i, j, :]) > 0:
                    degree += 1
            
            # 波长利用率
            wavelength_util = np.sum(wavelength_usage[i, :, :]) / (num_nodes * 80)
            
            # 邻居特征
            neighbor_count = 0
            neighbor_load = 0
            for j in range(num_nodes):
                if i != j and np.sum(wavelength_usage[i, j, :]) > 0:
                    neighbor_count += 1
                    neighbor_load += np.sum(wavelength_usage[i, j, :])
            
            # 设置12维特征
            node_features[i, 0] = degree / num_nodes
            node_features[i, 1] = lightpath_count / 50.0
            node_features[i, 2] = (avg_power + 5) / 10.0
            node_features[i, 3] = wavelength_util
            node_features[i, 4] = i / num_nodes
            node_features[i, 5] = neighbor_count / num_nodes
            node_features[i, 6] = neighbor_load / 80.0
            node_features[i, 7] = (max_power - min_power + 1) / 10.0
            node_features[i, 8] = wavelength_diversity / 80.0
            node_features[i, 9] = path_length_sum / 1000.0
            node_features[i, 10] = np.random.normal(0, 0.01)  # 噪声特征
            node_features[i, 11] = (i % 3) / 3.0  # 节点类型
        
        return node_features
    
    def _build_edge_index(self, topology_info):
        """构建边索引"""
        connections = topology_info['connections']
        edges = []
        for i, j in connections:
            edges.append([i, j])
            edges.append([j, i])
        
        return torch.tensor(edges, dtype=torch.long).t().contiguous()
    
    def train(self, train_data, val_data, epochs=150):
        """训练模型"""
        print(f"开始训练 {epochs} 个epoch...")
        
        # 更小的批次大小提高稳定性
        train_loader = DataLoader(train_data, batch_size=16, shuffle=True)
        val_loader = DataLoader(val_data, batch_size=16, shuffle=False)
        
        best_val_mse = float('inf')
        patience = 25
        patience_counter = 0
        min_improvement = 1e-5
        
        for epoch in range(epochs):
            # 训练
            train_metrics = self._train_epoch(train_loader)
            
            # 验证
            val_metrics = self._validate_epoch(val_loader)
            
            # 学习率调度
            self.scheduler.step(val_metrics['mse'])
            
            # 记录历史 - 使用移动平均平滑
            alpha = 0.9  # 平滑系数
            if epoch == 0:
                self.history['train_losses'].append(train_metrics['loss'])
                self.history['val_losses'].append(val_metrics['loss'])
                self.history['train_r2'].append(train_metrics['r2'])
                self.history['val_r2'].append(val_metrics['r2'])
                self.history['train_mse'].append(train_metrics['mse'])
                self.history['val_mse'].append(val_metrics['mse'])
                self.history['train_rmse'].append(train_metrics['rmse'])
                self.history['val_rmse'].append(val_metrics['rmse'])
            else:
                # 移动平均平滑
                self.history['train_losses'].append(
                    alpha * self.history['train_losses'][-1] + (1-alpha) * train_metrics['loss']
                )
                self.history['val_losses'].append(
                    alpha * self.history['val_losses'][-1] + (1-alpha) * val_metrics['loss']
                )
                self.history['train_r2'].append(
                    alpha * self.history['train_r2'][-1] + (1-alpha) * train_metrics['r2']
                )
                self.history['val_r2'].append(
                    alpha * self.history['val_r2'][-1] + (1-alpha) * val_metrics['r2']
                )
                self.history['train_mse'].append(
                    alpha * self.history['train_mse'][-1] + (1-alpha) * train_metrics['mse']
                )
                self.history['val_mse'].append(
                    alpha * self.history['val_mse'][-1] + (1-alpha) * val_metrics['mse']
                )
                self.history['train_rmse'].append(
                    alpha * self.history['train_rmse'][-1] + (1-alpha) * train_metrics['rmse']
                )
                self.history['val_rmse'].append(
                    alpha * self.history['val_rmse'][-1] + (1-alpha) * val_metrics['rmse']
                )
            
            # 打印进度
            if epoch % 10 == 0:
                print(f"Epoch {epoch:3d}: "
                      f"Train MSE={train_metrics['mse']:.6f}, "
                      f"Val MSE={val_metrics['mse']:.6f}, "
                      f"Train R²={train_metrics['r2']:.4f}, "
                      f"Val R²={val_metrics['r2']:.4f}")
            
            # 早停 - 基于MSE
            if val_metrics['mse'] < best_val_mse - min_improvement:
                best_val_mse = val_metrics['mse']
                patience_counter = 0
                torch.save(self.model.state_dict(), 'enhanced_multi_topology_gnn_best.pth')
                print(f"保存最佳模型 (epoch {epoch}, val_mse: {val_metrics['mse']:.6f})")
            else:
                patience_counter += 1
                if patience_counter >= patience:
                    print(f"早停于epoch {epoch} (最佳验证MSE: {best_val_mse:.6f})")
                    break
        
        print("训练完成!")
        return self.history
    
    def _train_epoch(self, train_loader):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        predictions = []
        targets = []
        
        for batch in train_loader:
            batch = batch.to(self.device)
            
            self.optimizer.zero_grad()
            
            output = self.model(batch.x, batch.edge_index, batch.batch, 
                              batch.global_features, batch.topology_id)
            loss = self.criterion(output.squeeze(), batch.y)
            
            # 数值稳定性检查
            if torch.isnan(loss) or torch.isinf(loss):
                continue
            
            loss.backward()
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=0.5)
            self.optimizer.step()
            
            total_loss += loss.item()
            predictions.extend(output.squeeze().detach().cpu().numpy())
            targets.extend(batch.y.detach().cpu().numpy())
        
        # 反标准化
        if len(predictions) == 0:
            return {'loss': float('inf'), 'r2': -1.0, 'mse': float('inf'), 'rmse': float('inf')}
            
        predictions = self.scaler.inverse_transform(np.array(predictions).reshape(-1, 1)).flatten()
        targets = self.scaler.inverse_transform(np.array(targets).reshape(-1, 1)).flatten()
        
        # 数值稳定性
        predictions = np.clip(predictions, 0, 100)
        targets = np.clip(targets, 0, 100)
        
        r2 = r2_score(targets, predictions)
        mse = mean_squared_error(targets, predictions)
        rmse = np.sqrt(mse)
        
        return {
            'loss': total_loss / len(train_loader),
            'r2': r2,
            'mse': mse,
            'rmse': rmse
        }
    
    def _validate_epoch(self, val_loader):
        """验证一个epoch"""
        self.model.eval()
        total_loss = 0
        predictions = []
        targets = []
        
        with torch.no_grad():
            for batch in val_loader:
                batch = batch.to(self.device)
                
                output = self.model(batch.x, batch.edge_index, batch.batch,
                                  batch.global_features, batch.topology_id)
                loss = self.criterion(output.squeeze(), batch.y)
                
                total_loss += loss.item()
                predictions.extend(output.squeeze().cpu().numpy())
                targets.extend(batch.y.cpu().numpy())
        
        # 反标准化
        if len(predictions) == 0:
            return {'loss': float('inf'), 'r2': -1.0, 'mse': float('inf'), 'rmse': float('inf')}
            
        predictions = self.scaler.inverse_transform(np.array(predictions).reshape(-1, 1)).flatten()
        targets = self.scaler.inverse_transform(np.array(targets).reshape(-1, 1)).flatten()
        
        # 数值稳定性
        predictions = np.clip(predictions, 0, 100)
        targets = np.clip(targets, 0, 100)
        
        r2 = r2_score(targets, predictions)
        mse = mean_squared_error(targets, predictions)
        rmse = np.sqrt(mse)
        
        return {
            'loss': total_loss / len(val_loader),
            'r2': r2,
            'mse': mse,
            'rmse': rmse
        }

def plot_enhanced_results(history):
    """增强的结果可视化"""
    fig, axes = plt.subplots(2, 3, figsize=(18, 10))
    
    epochs = range(len(history['train_losses']))
    
    # 1. 损失曲线
    axes[0, 0].plot(epochs, history['train_losses'], 'b-', label='Train Loss', alpha=0.8)
    axes[0, 0].plot(epochs, history['val_losses'], 'r-', label='Val Loss', alpha=0.8)
    axes[0, 0].set_title('Loss Curves', fontsize=14, fontweight='bold')
    axes[0, 0].set_xlabel('Epoch')
    axes[0, 0].set_ylabel('Loss')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. R²分数
    axes[0, 1].plot(epochs, history['train_r2'], 'b-', label='Train R²', alpha=0.8)
    axes[0, 1].plot(epochs, history['val_r2'], 'r-', label='Val R²', alpha=0.8)
    axes[0, 1].set_title('R² Score', fontsize=14, fontweight='bold')
    axes[0, 1].set_xlabel('Epoch')
    axes[0, 1].set_ylabel('R²')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. MSE
    axes[0, 2].plot(epochs, history['train_mse'], 'b-', label='Train MSE', alpha=0.8)
    axes[0, 2].plot(epochs, history['val_mse'], 'r-', label='Val MSE', alpha=0.8)
    axes[0, 2].set_title('Mean Squared Error', fontsize=14, fontweight='bold')
    axes[0, 2].set_xlabel('Epoch')
    axes[0, 2].set_ylabel('MSE')
    axes[0, 2].legend()
    axes[0, 2].grid(True, alpha=0.3)
    
    # 4. RMSE
    axes[1, 0].plot(epochs, history['train_rmse'], 'b-', label='Train RMSE', alpha=0.8)
    axes[1, 0].plot(epochs, history['val_rmse'], 'r-', label='Val RMSE', alpha=0.8)
    axes[1, 0].set_title('Root Mean Squared Error', fontsize=14, fontweight='bold')
    axes[1, 0].set_xlabel('Epoch')
    axes[1, 0].set_ylabel('RMSE')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 5. 最终性能对比
    final_metrics = ['R²', 'MSE', 'RMSE']
    final_values = [
        history['val_r2'][-1],
        history['val_mse'][-1],
        history['val_rmse'][-1]
    ]
    
    bars = axes[1, 1].bar(final_metrics, final_values, 
                         color=['#2E86AB', '#A23B72', '#F18F01'], alpha=0.8)
    axes[1, 1].set_title('Final Performance', fontsize=14, fontweight='bold')
    axes[1, 1].set_ylabel('Score')
    
    # 添加数值标签
    for bar, value in zip(bars, final_values):
        height = bar.get_height()
        axes[1, 1].text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                       f'{value:.4f}', ha='center', va='bottom', fontweight='bold')
    
    # 6. 训练稳定性分析
    # 计算损失的变化率
    train_loss_diff = np.diff(history['train_losses'])
    val_loss_diff = np.diff(history['val_losses'])
    
    axes[1, 2].plot(range(1, len(epochs)), train_loss_diff, 'b-', 
                   label='Train Loss Change', alpha=0.7)
    axes[1, 2].plot(range(1, len(epochs)), val_loss_diff, 'r-', 
                   label='Val Loss Change', alpha=0.7)
    axes[1, 2].axhline(y=0, color='k', linestyle='--', alpha=0.5)
    axes[1, 2].set_title('Training Stability', fontsize=14, fontweight='bold')
    axes[1, 2].set_xlabel('Epoch')
    axes[1, 2].set_ylabel('Loss Change')
    axes[1, 2].legend()
    axes[1, 2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('enhanced_multi_topology_training_results.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """主函数"""
    print("增强版多拓扑动态子图GNN训练系统")
    print("=" * 60)
    print(f"使用设备: {'cuda' if torch.cuda.is_available() else 'cpu'}")
    
    try:
        # 步骤1: 多拓扑数据生成
        print("\n步骤1: 多拓扑数据生成 (集成GNPY仿真)")
        data_generator = MultiTopologyDataGenerator()
        samples = data_generator.generate_training_data(num_samples=6000)  # 减少样本数量确保完成
        
        # 数据统计
        targets = [s['qot_impact'] for s in samples]
        topology_counts = {}
        for s in samples:
            topo = s['topology']
            topology_counts[topo] = topology_counts.get(topo, 0) + 1
        
        print(f"目标值统计:")
        print(f"  平均值: {np.mean(targets):.4f}")
        print(f"  标准差: {np.std(targets):.4f}")
        print(f"  最小值: {np.min(targets):.4f}")
        print(f"  最大值: {np.max(targets):.4f}")
        print(f"拓扑分布: {topology_counts}")
        
        # 步骤2: 模型初始化
        print("\n步骤2: 增强模型初始化")
        model = EnhancedGNN(
            node_features=12,
            edge_features=4,
            hidden_dim=64,
            num_layers=3
        )
        
        total_params = sum(p.numel() for p in model.parameters())
        print(f"模型参数量: {total_params:,}")
        
        # 步骤3: 训练器初始化
        print("\n步骤3: 增强训练器初始化")
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        trainer = EnhancedTrainer(model, device=device)
        
        # 步骤4: 数据准备
        print("\n步骤4: 数据准备")
        train_data, val_data = trainer.prepare_data(samples)
        
        # 步骤5: 模型训练
        print("\n步骤5: 模型训练")
        history = trainer.train(train_data, val_data, epochs=80)  # 减少epoch确保完成
        
        # 步骤6: 结果可视化
        print("\n步骤6: 增强结果可视化")
        plot_enhanced_results(history)
        
        # 步骤7: 保存结果
        print("\n步骤7: 保存结果")
        
        # 保存训练历史
        with open('enhanced_multi_topology_training_history.json', 'w') as f:
            json.dump(history, f, indent=2)
        
        # 保存最终结果
        final_results = {
            'final_val_r2': history['val_r2'][-1],
            'final_val_mse': history['val_mse'][-1],
            'final_val_rmse': history['val_rmse'][-1],
            'model_params': total_params,
            'topology_counts': topology_counts,
            'data_stats': {
                'mean': float(np.mean(targets)),
                'std': float(np.std(targets)),
                'min': float(np.min(targets)),
                'max': float(np.max(targets))
            }
        }
        
        with open('enhanced_multi_topology_final_results.json', 'w') as f:
            json.dump(final_results, f, indent=2)
        
        print(f"\n最终结果:")
        print(f"验证集R²: {history['val_r2'][-1]:.4f}")
        print(f"验证集MSE: {history['val_mse'][-1]:.6f}")
        print(f"验证集RMSE: {history['val_rmse'][-1]:.4f}")
        print("✅ 增强训练成功!")
        
    except Exception as e:
        print(f"❌ 训练失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()