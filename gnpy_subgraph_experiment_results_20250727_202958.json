{"timestamp": "20250727_202958", "experiment_type": "gnpy_based_honest_comparison", "data_info": {"train_samples": 800, "test_samples": 200, "osnr_range": [10.0, 10.0], "path_length_range": [201.0539200722627, 800.0], "gnpy_params": {"fiber_loss": 0.2, "fiber_length_range": [50, 500], "edfa_gain_range": [15, 25], "edfa_nf": 4.5, "wavelength_range": [1530, 1565], "power_range": [-10, 5], "nonlinear_coefficient": 0.0013, "dispersion": 17, "effective_area": 8e-11}}, "baseline_results": {"Linear Regression": {"r2": 1.0, "mse": 0.0, "mae": 0.0, "predictions": [10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0]}, "Random Forest": {"r2": 1.0, "mse": 0.0, "mae": 0.0, "predictions": [10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0]}, "SVM": {"r2": 1.0, "mse": 0.0, "mae": 0.0, "predictions": [10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0]}}, "subgraph_gnn_results": {"r2": 0.0, "mse": 0.12043511122465134, "mae": 0.2921568751335144, "predictions": [10.238903999328613, 9.953286170959473, 10.672825813293457, 10.278470039367676, 10.495173454284668, 10.283730506896973, 10.371844291687012, 10.349295616149902, 10.285344123840332, 10.383193969726562, 10.20190143585205, 10.473897933959961, 10.480204582214355, 10.23449420928955, 9.6145658493042, 10.1481351852417, 10.262234687805176, 10.330243110656738, 10.210329055786133, 10.072772026062012, 10.469350814819336, 10.180095672607422, 9.785393714904785, 10.366079330444336, 10.499617576599121, 10.322381973266602, 10.316469192504883, 10.189434051513672, 10.220171928405762, 10.36097240447998, 10.219433784484863, 10.265969276428223, 9.786175727844238, 10.433934211730957, 10.613700866699219, 10.309221267700195, 9.852625846862793, 10.478662490844727, 10.569079399108887, 10.192429542541504, 10.297802925109863, 9.996602058410645, 10.284786224365234, 10.470297813415527, 10.52122974395752, 10.103113174438477, 9.875150680541992, 9.753825187683105, 10.155872344970703, 9.621370315551758, 10.26943302154541, 10.154377937316895, 10.752973556518555, 10.017950057983398, 10.272628784179688, 10.70387077331543, 10.19560718536377, 10.10107135772705, 10.195277214050293, 10.361235618591309, 10.00603199005127, 10.32846736907959, 9.945257186889648, 10.513300895690918, 10.407002449035645, 10.42409610748291, 9.977522850036621, 10.214089393615723, 10.344354629516602, 10.39228630065918, 10.004122734069824, 10.453051567077637, 10.163651466369629, 10.459136009216309, 10.541699409484863, 10.403082847595215, 10.362103462219238, 9.83067798614502, 9.921213150024414, 10.04922103881836, 10.216766357421875, 10.295074462890625, 9.80086612701416, 10.009325981140137, 10.15015697479248, 10.371132850646973, 10.314169883728027, 10.385115623474121, 10.277714729309082, 9.956608772277832, 10.64768123626709, 9.54514217376709, 10.52556324005127, 10.040587425231934, 9.947144508361816, 9.992203712463379, 10.039478302001953, 10.349574089050293, 9.871018409729004, 10.369293212890625, 10.173932075500488, 9.896454811096191, 9.989388465881348, 9.822822570800781, 10.233019828796387, 10.125090599060059, 9.927236557006836, 9.77295207977295, 10.726286888122559, 9.584378242492676, 10.2034273147583, 10.032841682434082, 10.4666109085083, 10.099148750305176, 10.525601387023926, 10.793042182922363, 10.187958717346191, 10.660338401794434, 10.28033447265625, 9.721415519714355, 10.298296928405762, 10.48055362701416, 10.04162311553955, 10.553187370300293, 9.994038581848145, 10.581034660339355, 10.074318885803223, 10.310905456542969, 9.989850997924805, 10.509112358093262, 9.924790382385254, 10.295608520507812, 10.385884284973145, 10.10294246673584, 10.37791919708252, 10.504645347595215, 9.496817588806152, 10.601195335388184, 10.508528709411621, 10.190214157104492, 10.611397743225098, 10.271480560302734, 10.535866737365723, 10.170629501342773, 10.509028434753418, 10.620269775390625, 10.521967887878418, 10.415470123291016, 10.387202262878418, 9.813233375549316, 10.285327911376953, 10.428069114685059, 9.770264625549316, 10.335406303405762, 10.029593467712402, 10.184244155883789, 10.516982078552246, 10.683984756469727, 10.248764991760254, 10.007994651794434, 10.054533004760742, 10.375907897949219, 10.782673835754395, 9.854912757873535, 10.56583309173584, 10.223278045654297, 9.981284141540527, 10.19253158569336, 10.221659660339355, 10.367634773254395, 10.020916938781738, 10.02668285369873, 10.329780578613281, 10.451705932617188, 10.294503211975098, 10.225401878356934, 10.063652992248535, 10.455021858215332, 10.333438873291016, 10.023026466369629, 10.438844680786133, 10.576384544372559, 9.491490364074707, 10.25090503692627, 10.254242897033691, 10.362879753112793, 9.9183988571167, 10.33205509185791, 9.906525611877441, 10.233502388000488, 10.27247142791748, 10.539676666259766, 10.288873672485352, 9.93144702911377, 10.273029327392578, 10.135893821716309, 9.98178482055664, 9.836708068847656, 9.864581108093262, 9.820281028747559]}, "summary": {"best_baseline_method": "Linear Regression", "best_baseline_r2": 1.0, "subgraph_gnn_r2": 0.0, "improvement_percent": -100.0}}