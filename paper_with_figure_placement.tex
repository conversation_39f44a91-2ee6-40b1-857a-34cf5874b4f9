% IEEE Conference Paper Template with Figure Placement
% 展示图表在论文中的正确位置

\documentclass[conference]{IEEEtran}
\usepackage{graphicx}
\usepackage{amsmath}
\usepackage{algorithm}
\usepackage{algorithmic}

\begin{document}

\title{Subgraph-based Graph Attention Network for Lightpath Interference Identification in Optical Networks}

\author{
\IEEEauthorblockN{Authors}
\IEEEauthorblockA{Institution\\
Email: <EMAIL>}
}

\maketitle

\begin{abstract}
Optical network management requires efficient identification of lightpath interference to maintain service quality...
\end{abstract}

\section{Introduction}
Optical networks carry increasing traffic volumes, making efficient Quality of Transmission (QoT) management crucial...

\section{Methodology}

\subsection{Problem Formulation}
Given an optical network represented as graph $G = (V, E)$ with $V$ nodes and $E$ links...

\subsection{Subgraph Construction Strategy}
Rather than processing the entire network graph, we extract relevant subgraphs that contain the essential information for interference prediction. The subgraph construction algorithm is detailed in Fig.~\ref{fig:algorithm}.

% 算法图放在这里 - 跨双栏
\begin{figure*}[htbp]
\centering
\includegraphics[width=\textwidth]{Fig2_algorithm.pdf}
\caption{Algorithm pseudocode for the complete subgraph GAT procedure, detailing the three main phases: subgraph extraction, feature engineering, and GAT-based classification.}
\label{fig:algorithm}
\end{figure*}

The construction process begins by identifying all nodes involved in both the new lightpath and target lightpath...

\subsection{Multi-head Graph Attention Network}
The extracted subgraph and node features are processed by a two-layer GAT with 4 attention heads per layer, as illustrated in Fig.~\ref{fig:architecture}.

% 架构图放在这里 - 跨双栏
\begin{figure*}[htbp]
\centering
\includegraphics[width=0.9\textwidth]{Fig3_architecture.pdf}
\caption{System architecture overview showing the processing flow from 14-node network input through subgraph construction, 7D feature engineering, multi-head GAT processing, to final binary classification.}
\label{fig:architecture}
\end{figure*}

The attention mechanism enables the model to focus on the most relevant neighboring nodes...

\section{Experimental Evaluation}

\subsection{Experimental Setup}
Experiments are conducted on a 14-node Japanese network topology with 29 bidirectional links...

\subsection{Performance Results}
Table~\ref{tab:performance} presents the experimental results comparing our subgraph GAT approach with two full-graph baselines.

% 表格放在这里
\begin{table}[htbp]
\centering
\caption{Performance Comparison of Different Methods}
\label{tab:performance}
\begin{tabular}{lccccc}
\hline
Method & \begin{tabular}[c]{@{}c@{}}Test Acc.\\ (\%)\end{tabular} & \begin{tabular}[c]{@{}c@{}}F1 Score\\ (\%)\end{tabular} & Params & \begin{tabular}[c]{@{}c@{}}Time\\ (s)\end{tabular} \\
\hline
\textbf{Subgraph GAT (Ours)} & \textbf{93.00} & \textbf{92.99} & 21,986 & 35.57 \\
Full Graph GCN & 68.67 & 55.91 & 1,282 & 6.12 \\
Full Graph GAT & 64.00 & 59.43 & 17,666 & 35.04 \\
\hline
\end{tabular}
\end{table}

Our subgraph-based approach significantly outperforms both full-graph baselines, achieving 93.00\% test accuracy compared to 68.67\% for GCN and 64.00\% for GAT.

Fig.~\ref{fig:performance} provides a detailed comparison of the key performance metrics across the three approaches, showing consistent superiority of the subgraph approach across all evaluation criteria.

% 性能对比图放在这里 - 单栏
\begin{figure}[htbp]
\centering
\includegraphics[width=\columnwidth]{Fig1_performance.pdf}
\caption{Performance comparison showing test accuracy and F1 scores for the three methods evaluated on 300 test samples. Our subgraph GAT approach significantly outperforms both full-graph baselines.}
\label{fig:performance}
\end{figure}

\subsection{Computational Analysis}
Despite having more parameters than the full-graph GCN (21,986 vs 1,282), our subgraph approach processes significantly smaller graph structures...

\section{Conclusions}
This paper presents a subgraph-based GAT approach for lightpath interference identification in optical networks...

\section*{Acknowledgment}
The authors would like to thank...

\begin{thebibliography}{1}
\bibitem{poggiolini2012}
P. Poggiolini, ``The GN model of non-linear propagation in uncompensated coherent optical systems,'' \emph{J. Lightwave Technol.}, vol. 30, no. 24, pp. 3857--3879, 2012.
\end{thebibliography}

\end{document}