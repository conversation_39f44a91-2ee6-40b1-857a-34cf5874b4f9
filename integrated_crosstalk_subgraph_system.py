#!/usr/bin/env python3
"""
完整集成的串扰感知动态子图GAT系统
结合串扰物理模型、快速QoT更新和子图GAT预测
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import dgl
import numpy as np
import networkx as nx
import time
import json
from typing import Dict, List, Set, Tuple
from collections import defaultdict
from sklearn.metrics import r2_score, mean_squared_error

# 导入我们的串扰感知模块
from crosstalk_aware_qot_updater import FastCrosstalkAnalyzer, RapidQoTUpdater

class CrosstalkAwareSubgraphGAT(nn.Module):
    """串扰感知的子图GAT模型"""
    
    def __init__(self, input_dim, hidden_dim=128, num_heads=8, num_layers=3, dropout=0.1):
        super().__init__()
        
        self.num_layers = num_layers
        self.hidden_dim = hidden_dim
        
        # GAT层
        self.gat_layers = nn.ModuleList()
        self.norm_layers = nn.ModuleList()
        
        # 输入层
        self.gat_layers.append(
            dgl.nn.GATConv(input_dim, hidden_dim, num_heads,
                          feat_drop=dropout, attn_drop=dropout, 
                          allow_zero_in_degree=True)
        )
        self.norm_layers.append(nn.LayerNorm(hidden_dim * num_heads))
        
        # 隐藏层
        for _ in range(num_layers - 2):
            self.gat_layers.append(
                dgl.nn.GATConv(hidden_dim * num_heads, hidden_dim, num_heads,
                              feat_drop=dropout, attn_drop=dropout,
                              allow_zero_in_degree=True)
            )
            self.norm_layers.append(nn.LayerNorm(hidden_dim * num_heads))
        
        # 输出层
        if num_layers > 1:
            self.gat_layers.append(
                dgl.nn.GATConv(hidden_dim * num_heads, hidden_dim, 1,
                              feat_drop=dropout, attn_drop=dropout,
                              allow_zero_in_degree=True)
            )
            self.norm_layers.append(nn.LayerNorm(hidden_dim))
        
        # 串扰感知的预测头
        self.crosstalk_predictor = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 4, 1)
        )
        
        # QoT预测头
        self.qot_predictor = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 4, 3)  # SNR, OSNR, BER
        )
        
        # 影响强度预测头
        self.influence_predictor = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 4, 1),
            nn.Sigmoid()
        )
        
    def forward(self, g, x):
        h = x
        
        # GAT层传播
        for i, (gat_layer, norm_layer) in enumerate(zip(self.gat_layers, self.norm_layers)):
            if i < len(self.gat_layers) - 1:
                h = gat_layer(g, h).flatten(1)
            else:
                h = gat_layer(g, h).squeeze(1)
            h = norm_layer(h)
            h = F.relu(h)
        
        # 图级表示
        g.ndata['h'] = h
        graph_repr = dgl.readout_nodes(g, 'h', op='mean')
        
        # 多任务预测
        crosstalk_pred = self.crosstalk_predictor(graph_repr).squeeze(-1)
        qot_pred = self.qot_predictor(graph_repr)
        influence_pred = self.influence_predictor(graph_repr).squeeze(-1)
        
        return {
            'crosstalk_penalty': crosstalk_pred,
            'qot_prediction': {
                'snr': qot_pred[:, 0],
                'osnr': qot_pred[:, 1], 
                'ber': torch.sigmoid(qot_pred[:, 2]) * 1e-12  # BER范围限制
            },
            'influence_strength': influence_pred
        }

class IntegratedCrosstalkSubgraphSystem:
    """集成的串扰感知子图系统"""
    
    def __init__(self, network_graph: nx.Graph):
        self.network = network_graph
        self.crosstalk_analyzer = FastCrosstalkAnalyzer()
        self.qot_updater = RapidQoTUpdater()
        self.gat_model = None
        self.feature_scaler = None
        
        # 系统状态
        self.current_lightpaths = []
        self.system_metrics = {
            'total_updates': 0,
            'avg_update_time_ms': 0,
            'prediction_accuracy': 0,
            'crosstalk_detection_rate': 0
        }
        
    def process_new_lightpath_event(self, new_lightpath: Dict) -> Dict:
        """处理新增光路事件 - 完整流程"""
        
        start_time = time.time()
        
        print(f"🔄 处理新增光路: {new_lightpath.get('id', 'unknown')}")
        
        # 1. 快速串扰分析和QoT更新
        qot_update_result = self.qot_updater.rapid_qot_update_for_new_lightpath(
            new_lightpath, self.current_lightpaths
        )
        
        # 2. 基于串扰结果识别影响子图
        influence_subgraph_result = self._identify_crosstalk_influence_subgraph(
            new_lightpath, qot_update_result
        )
        
        # 3. 子图GAT预测（如果模型已训练）
        gat_prediction_result = None
        if self.gat_model is not None:
            gat_prediction_result = self._predict_with_subgraph_gat(
                influence_subgraph_result
            )
        
        # 4. 更新系统状态
        self._update_system_state(new_lightpath, qot_update_result)
        
        total_time = time.time() - start_time
        
        # 5. 整合结果
        integrated_result = {
            'event_info': {
                'type': 'new_lightpath',
                'lightpath': new_lightpath,
                'processing_time_ms': total_time * 1000
            },
            'crosstalk_analysis': qot_update_result,
            'influence_subgraph': influence_subgraph_result,
            'gat_prediction': gat_prediction_result,
            'system_performance': {
                'total_time_ms': total_time * 1000,
                'crosstalk_analysis_time_ms': qot_update_result['performance_metrics']['analysis_time_ms'],
                'subgraph_identification_time_ms': influence_subgraph_result['processing_time_ms'],
                'gat_prediction_time_ms': gat_prediction_result['prediction_time_ms'] if gat_prediction_result else 0
            },
            'impact_summary': self._generate_impact_summary(qot_update_result, influence_subgraph_result)
        }
        
        # 更新系统指标
        self._update_system_metrics(integrated_result)
        
        print(f"✅ 处理完成，总耗时: {total_time*1000:.2f} ms")
        
        return integrated_result
    
    def _identify_crosstalk_influence_subgraph(self, new_lightpath: Dict, 
                                             qot_update_result: Dict) -> Dict:
        """基于串扰结果识别影响子图"""
        
        start_time = time.time()
        
        # 从串扰分析结果中提取影响信息
        affected_lightpaths = qot_update_result['affected_lightpaths']
        
        # 构建影响节点集合
        influence_nodes = set(new_lightpath.get('path', []))
        
        # 添加受影响光路的节点
        for affected_lp in affected_lightpaths:
            # 从原始光路中找到对应的路径信息
            for original_lp in self.current_lightpaths:
                if original_lp.get('id') == affected_lp['lightpath_id']:
                    influence_nodes.update(original_lp.get('path', []))
                    break
        
        # 构建子图
        if len(influence_nodes) > 1:
            subgraph = self.network.subgraph(influence_nodes)
        else:
            subgraph = nx.Graph()
            subgraph.add_nodes_from(influence_nodes)
        
        # 提取子图特征
        subgraph_features = self._extract_enhanced_subgraph_features(
            subgraph, new_lightpath, affected_lightpaths
        )
        
        processing_time = time.time() - start_time
        
        return {
            'influence_nodes': list(influence_nodes),
            'subgraph': subgraph,
            'subgraph_features': subgraph_features,
            'affected_lightpath_count': len(affected_lightpaths),
            'processing_time_ms': processing_time * 1000,
            'compression_ratio': len(influence_nodes) / max(len(self.network.nodes()), 1),
            'crosstalk_based_selection': True
        }
    
    def _extract_enhanced_subgraph_features(self, subgraph: nx.Graph, 
                                          new_lightpath: Dict,
                                          affected_lightpaths: List[Dict]) -> Dict:
        """提取增强的子图特征（串扰感知）"""
        
        # 基础拓扑特征
        basic_features = {
            'node_count': len(subgraph.nodes()),
            'edge_count': len(subgraph.edges()),
            'density': nx.density(subgraph) if len(subgraph.nodes()) > 1 else 0,
            'avg_degree': np.mean([subgraph.degree(n) for n in subgraph.nodes()]) if subgraph.nodes() else 0
        }
        
        # 新光路特征
        new_lightpath_features = {
            'new_wavelength': new_lightpath.get('wavelength', 0),
            'new_power_dbm': new_lightpath.get('power', -15.0),
            'new_path_length': len(new_lightpath.get('path', [])) - 1
        }
        
        # 串扰相关特征
        if affected_lightpaths:
            crosstalk_penalties = [lp.get('crosstalk_info', {}).get('penalty_db', 0) 
                                 for lp in affected_lightpaths]
            overlap_ratios = [lp.get('crosstalk_info', {}).get('overlap_info', {}).get('overlap_ratio', 0)
                            for lp in affected_lightpaths]
            
            crosstalk_features = {
                'total_affected_lightpaths': len(affected_lightpaths),
                'avg_crosstalk_penalty': np.mean(crosstalk_penalties),
                'max_crosstalk_penalty': np.max(crosstalk_penalties),
                'std_crosstalk_penalty': np.std(crosstalk_penalties),
                'avg_overlap_ratio': np.mean(overlap_ratios),
                'high_impact_count': len([p for p in crosstalk_penalties if p > 1.0]),
                'co_channel_count': len([lp for lp in affected_lightpaths 
                                       if lp.get('crosstalk_info', {}).get('type') == 'co-channel']),
                'adjacent_channel_count': len([lp for lp in affected_lightpaths 
                                             if lp.get('crosstalk_info', {}).get('type') == 'adjacent-channel'])
            }
        else:
            crosstalk_features = {
                'total_affected_lightpaths': 0,
                'avg_crosstalk_penalty': 0,
                'max_crosstalk_penalty': 0,
                'std_crosstalk_penalty': 0,
                'avg_overlap_ratio': 0,
                'high_impact_count': 0,
                'co_channel_count': 0,
                'adjacent_channel_count': 0
            }
        
        # QoT变化特征
        if affected_lightpaths:
            snr_changes = [lp.get('qot_degradation', {}).get('snr_loss_db', 0) 
                          for lp in affected_lightpaths]
            qot_features = {
                'avg_snr_degradation': np.mean(snr_changes),
                'max_snr_degradation': np.max(snr_changes),
                'severe_degradation_count': len([s for s in snr_changes if s > 2.0])
            }
        else:
            qot_features = {
                'avg_snr_degradation': 0,
                'max_snr_degradation': 0,
                'severe_degradation_count': 0
            }
        
        return {**basic_features, **new_lightpath_features, **crosstalk_features, **qot_features}
    
    def _predict_with_subgraph_gat(self, influence_subgraph_result: Dict) -> Dict:
        """使用子图GAT进行预测"""
        
        start_time = time.time()
        
        try:
            # 构建DGL图
            subgraph = influence_subgraph_result['subgraph']
            if len(subgraph.nodes()) < 2:
                return {
                    'prediction_available': False,
                    'reason': 'Insufficient nodes for GAT prediction',
                    'prediction_time_ms': 0
                }
            
            # 创建DGL图
            edges = list(subgraph.edges())
            if not edges:
                return {
                    'prediction_available': False,
                    'reason': 'No edges in subgraph',
                    'prediction_time_ms': 0
                }
            
            u, v = zip(*edges)
            g = dgl.graph((u, v), num_nodes=len(subgraph.nodes()))
            g = dgl.to_bidirected(g)
            
            # 构建节点特征
            node_features = self._build_node_features_for_gat(
                subgraph, influence_subgraph_result['subgraph_features']
            )
            
            # GAT预测
            self.gat_model.eval()
            with torch.no_grad():
                predictions = self.gat_model(g, node_features)
            
            prediction_time = time.time() - start_time
            
            return {
                'prediction_available': True,
                'predictions': {
                    'crosstalk_penalty': predictions['crosstalk_penalty'].item(),
                    'qot_prediction': {
                        'snr': predictions['qot_prediction']['snr'].item(),
                        'osnr': predictions['qot_prediction']['osnr'].item(),
                        'ber': predictions['qot_prediction']['ber'].item()
                    },
                    'influence_strength': predictions['influence_strength'].item()
                },
                'prediction_time_ms': prediction_time * 1000,
                'model_confidence': self._calculate_prediction_confidence(predictions)
            }
            
        except Exception as e:
            return {
                'prediction_available': False,
                'reason': f'GAT prediction error: {str(e)}',
                'prediction_time_ms': (time.time() - start_time) * 1000
            }
    
    def _build_node_features_for_gat(self, subgraph: nx.Graph, subgraph_features: Dict) -> torch.Tensor:
        """为GAT构建节点特征"""
        
        node_features = []
        
        for node in subgraph.nodes():
            # 基础节点特征
            degree = subgraph.degree(node)
            
            # 从子图特征中提取相关信息
            features = [
                float(node),  # 节点ID
                degree,  # 度数
                subgraph_features.get('new_wavelength', 0),
                subgraph_features.get('new_power_dbm', -15.0),
                subgraph_features.get('avg_crosstalk_penalty', 0),
                subgraph_features.get('avg_overlap_ratio', 0),
                subgraph_features.get('total_affected_lightpaths', 0),
                subgraph_features.get('density', 0)
            ]
            
            node_features.append(features)
        
        return torch.tensor(node_features, dtype=torch.float32)
    
    def _calculate_prediction_confidence(self, predictions: Dict) -> float:
        """计算预测置信度"""
        
        # 基于预测值的合理性计算置信度
        crosstalk = predictions['crosstalk_penalty'].item()
        snr = predictions['qot_prediction']['snr'].item()
        influence = predictions['influence_strength'].item()
        
        # 合理性检查
        confidence = 1.0
        
        # 串扰惩罚合理性
        if crosstalk < 0 or crosstalk > 10:
            confidence *= 0.7
        
        # SNR合理性
        if snr < 10 or snr > 35:
            confidence *= 0.8
        
        # 影响强度合理性
        if influence < 0 or influence > 1:
            confidence *= 0.6
        
        return confidence
    
    def _update_system_state(self, new_lightpath: Dict, qot_update_result: Dict):
        """更新系统状态"""
        
        # 添加新光路到当前光路列表
        self.current_lightpaths.append(new_lightpath)
        
        # 更新受影响光路的QoT
        for affected_lp in qot_update_result['affected_lightpaths']:
            for i, current_lp in enumerate(self.current_lightpaths):
                if current_lp.get('id') == affected_lp['lightpath_id']:
                    self.current_lightpaths[i]['qot'] = affected_lp['updated_qot']
                    break
    
    def _generate_impact_summary(self, qot_update_result: Dict, 
                                influence_subgraph_result: Dict) -> Dict:
        """生成影响总结"""
        
        return {
            'total_lightpaths_analyzed': len(self.current_lightpaths),
            'affected_lightpaths': qot_update_result['crosstalk_summary']['affected_count'],
            'influence_subgraph_size': len(influence_subgraph_result['influence_nodes']),
            'compression_achieved': influence_subgraph_result['compression_ratio'],
            'max_qot_degradation_db': qot_update_result['crosstalk_summary']['max_penalty_db'],
            'avg_qot_degradation_db': qot_update_result['crosstalk_summary']['avg_penalty_db'],
            'co_channel_conflicts': qot_update_result['crosstalk_summary']['co_channel_conflicts'],
            'adjacent_channel_conflicts': qot_update_result['crosstalk_summary']['adjacent_channel_conflicts'],
            'processing_efficiency': {
                'subgraph_vs_fullnetwork_speedup': 1 / max(influence_subgraph_result['compression_ratio'], 0.01),
                'analysis_speed_lightpaths_per_second': qot_update_result['performance_metrics']['lightpaths_per_second']
            }
        }
    
    def _update_system_metrics(self, integrated_result: Dict):
        """更新系统指标"""
        
        self.system_metrics['total_updates'] += 1
        
        # 更新平均处理时间
        current_time = integrated_result['system_performance']['total_time_ms']
        self.system_metrics['avg_update_time_ms'] = (
            (self.system_metrics['avg_update_time_ms'] * (self.system_metrics['total_updates'] - 1) + current_time) /
            self.system_metrics['total_updates']
        )
        
        # 更新串扰检测率
        total_analyzed = integrated_result['impact_summary']['total_lightpaths_analyzed']
        affected = integrated_result['impact_summary']['affected_lightpaths']
        if total_analyzed > 0:
            detection_rate = affected / total_analyzed
            self.system_metrics['crosstalk_detection_rate'] = (
                (self.system_metrics['crosstalk_detection_rate'] * (self.system_metrics['total_updates'] - 1) + detection_rate) /
                self.system_metrics['total_updates']
            )
    
    def get_system_status(self) -> Dict:
        """获取系统状态"""
        
        return {
            'current_lightpath_count': len(self.current_lightpaths),
            'network_nodes': len(self.network.nodes()),
            'network_edges': len(self.network.edges()),
            'system_metrics': self.system_metrics,
            'gat_model_loaded': self.gat_model is not None,
            'cache_status': {
                'crosstalk_cache_size': len(self.crosstalk_analyzer.crosstalk_cache),
                'path_overlap_cache_size': len(self.crosstalk_analyzer.path_overlap_cache),
                'wavelength_map_size': len(self.crosstalk_analyzer.wavelength_map)
            }
        }

def main():
    """测试集成系统"""
    
    print("🚀 测试集成的串扰感知动态子图GAT系统")
    print("=" * 70)
    
    # 创建测试网络
    G = nx.erdos_renyi_graph(20, 0.3)
    
    # 创建集成系统
    system = IntegratedCrosstalkSubgraphSystem(G)
    
    # 添加一些初始光路
    initial_lightpaths = []
    for i in range(10):
        lp = {
            'id': f'initial_lp_{i:03d}',
            'path': [str(j) for j in range(i % 5, (i % 5) + 3)],
            'wavelength': 40 + (i % 8),
            'power': -15.0 + np.random.uniform(-2, 2),
            'qot': {
                'snr': 25.0 + np.random.uniform(-2, 2),
                'osnr': 26.0 + np.random.uniform(-2, 2),
                'ber': 1e-15 * np.random.uniform(0.5, 2.0)
            }
        }
        initial_lightpaths.append(lp)
    
    system.current_lightpaths = initial_lightpaths
    
    print(f"初始网络状态: {len(initial_lightpaths)} 条光路")
    
    # 测试新增光路
    new_lightpath = {
        'id': 'test_new_lp_001',
        'path': ['0', '1', '2', '3'],
        'wavelength': 42,  # 与一些现有光路可能冲突
        'power': -12.0
    }
    
    print(f"\n🔄 测试新增光路: {new_lightpath['id']}")
    
    # 处理新增光路事件
    result = system.process_new_lightpath_event(new_lightpath)
    
    # 显示结果
    print(f"\n📊 处理结果:")
    print(f"   总处理时间: {result['system_performance']['total_time_ms']:.2f} ms")
    print(f"   串扰分析时间: {result['system_performance']['crosstalk_analysis_time_ms']:.2f} ms")
    print(f"   子图识别时间: {result['system_performance']['subgraph_identification_time_ms']:.2f} ms")
    
    impact = result['impact_summary']
    print(f"\n🎯 影响分析:")
    print(f"   受影响光路: {impact['affected_lightpaths']}/{impact['total_lightpaths_analyzed']}")
    print(f"   影响子图大小: {impact['influence_subgraph_size']} 节点")
    print(f"   压缩比: {impact['compression_achieved']:.3f}")
    print(f"   最大QoT恶化: {impact['max_qot_degradation_db']:.3f} dB")
    print(f"   同信道冲突: {impact['co_channel_conflicts']}")
    print(f"   邻近信道冲突: {impact['adjacent_channel_conflicts']}")
    
    # 系统状态
    status = system.get_system_status()
    print(f"\n⚙️  系统状态:")
    print(f"   当前光路数: {status['current_lightpath_count']}")
    print(f"   平均处理时间: {status['system_metrics']['avg_update_time_ms']:.2f} ms")
    print(f"   串扰检测率: {status['system_metrics']['crosstalk_detection_rate']:.3f}")
    
    # 保存详细结果
    with open('integrated_system_test_results.json', 'w') as f:
        json.dump(result, f, indent=2, default=str)
    
    print(f"\n✅ 集成系统测试完成！")
    print(f"📁 详细结果已保存到 integrated_system_test_results.json")

if __name__ == "__main__":
    main()
