"""
动态子图GNN模型
基于真实物理数据训练的图神经网络
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import GCNConv, GATConv, global_mean_pool
from torch_geometric.data import Data, DataLoader
import numpy as np
import pickle
from typing import Dict, List, Tuple
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score
import matplotlib.pyplot as plt

class DynamicSubgraphGNN(nn.Module):
    """动态子图GNN模型"""
    
    def __init__(self, node_features: int = 2, edge_features: int = 2, 
                 hidden_dim: int = 64, num_layers: int = 3):
        """
        初始化GNN模型
        
        Args:
            node_features: 节点特征维度
            edge_features: 边特征维度  
            hidden_dim: 隐藏层维度
            num_layers: GNN层数
        """
        super(DynamicSubgraphGNN, self).__init__()
        
        self.node_features = node_features
        self.edge_features = edge_features
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        
        # 节点特征编码器
        self.node_encoder = nn.Linear(node_features, hidden_dim)
        
        # 边特征编码器
        self.edge_encoder = nn.Linear(edge_features, hidden_dim)
        
        # GNN层（使用GAT进行注意力机制）
        self.gnn_layers = nn.ModuleList()
        for i in range(num_layers):
            if i == 0:
                self.gnn_layers.append(GATConv(hidden_dim, hidden_dim, heads=4, concat=False))
            else:
                self.gnn_layers.append(GATConv(hidden_dim, hidden_dim, heads=4, concat=False))
        
        # 图级别预测器
        self.graph_predictor = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim // 4, 1)
        )
        
        print(f"✅ DynamicSubgraphGNN initialized")
        print(f"   Node features: {node_features}")
        print(f"   Edge features: {edge_features}")
        print(f"   Hidden dim: {hidden_dim}")
        print(f"   Layers: {num_layers}")
    
    def forward(self, x, edge_index, edge_attr, batch=None):
        """
        前向传播
        
        Args:
            x: 节点特征 [num_nodes, node_features]
            edge_index: 边索引 [2, num_edges]
            edge_attr: 边特征 [num_edges, edge_features]
            batch: 批次索引
            
        Returns:
            图级别预测结果
        """
        # 编码节点特征
        x = self.node_encoder(x)
        x = F.relu(x)
        
        # GNN层
        for i, gnn_layer in enumerate(self.gnn_layers):
            x_new = gnn_layer(x, edge_index)
            x_new = F.relu(x_new)
            
            # 残差连接
            if i > 0:
                x = x + x_new
            else:
                x = x_new
        
        # 图级别池化
        if batch is not None:
            x = global_mean_pool(x, batch)
        else:
            x = torch.mean(x, dim=0, keepdim=True)
        
        # 预测QoT降级
        output = self.graph_predictor(x)
        
        return output

class SubgraphDataset:
    """子图数据集类"""
    
    def __init__(self, dataset_file: str):
        """加载数据集"""
        print(f"📂 Loading dataset from {dataset_file}")
        
        with open(dataset_file, 'rb') as f:
            self.data = pickle.load(f)
        
        self.samples = self.data['training_samples']
        self.network_info = self.data['network_info']
        
        print(f"✅ Dataset loaded: {len(self.samples)} samples")
    
    def to_pytorch_geometric(self) -> List[Data]:
        """转换为PyTorch Geometric格式"""
        print("🔄 Converting to PyTorch Geometric format...")
        
        data_list = []
        
        for sample in self.samples:
            subgraph = sample['subgraph_features']
            
            # 节点特征
            node_features = torch.tensor(subgraph['node_features'], dtype=torch.float)
            
            # 边索引和特征
            if subgraph['edge_indices']:
                edge_index = torch.tensor(subgraph['edge_indices'], dtype=torch.long).t().contiguous()
                edge_attr = torch.tensor(subgraph['edge_features'], dtype=torch.float)
            else:
                # 处理没有边的情况
                edge_index = torch.empty((2, 0), dtype=torch.long)
                edge_attr = torch.empty((0, 2), dtype=torch.float)
            
            # 目标值（QoT降级）
            target = torch.tensor([subgraph['target_degradation']], dtype=torch.float)
            
            # 创建Data对象
            data = Data(
                x=node_features,
                edge_index=edge_index,
                edge_attr=edge_attr,
                y=target
            )
            
            data_list.append(data)
        
        print(f"✅ Converted {len(data_list)} samples")
        return data_list

class DynamicSubgraphTrainer:
    """动态子图GNN训练器"""
    
    def __init__(self, model: DynamicSubgraphGNN, device: str = 'cpu'):
        """
        初始化训练器
        
        Args:
            model: GNN模型
            device: 计算设备
        """
        self.model = model.to(device)
        self.device = device
        self.optimizer = torch.optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-5)
        self.criterion = nn.MSELoss()
        
        # 训练历史
        self.train_losses = []
        self.val_losses = []
        self.train_r2s = []
        self.val_r2s = []
        
        print(f"✅ Trainer initialized on {device}")
    
    def train_epoch(self, train_loader: DataLoader) -> float:
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        predictions = []
        targets = []
        
        for batch in train_loader:
            batch = batch.to(self.device)
            
            self.optimizer.zero_grad()
            
            # 前向传播
            output = self.model(batch.x, batch.edge_index, batch.edge_attr, batch.batch)
            loss = self.criterion(output.squeeze(), batch.y)
            
            # 反向传播
            loss.backward()
            self.optimizer.step()
            
            total_loss += loss.item()
            predictions.extend(output.squeeze().detach().cpu().numpy())
            targets.extend(batch.y.detach().cpu().numpy())
        
        # 计算R²
        r2 = r2_score(targets, predictions)
        
        return total_loss / len(train_loader), r2
    
    def validate(self, val_loader: DataLoader) -> Tuple[float, float]:
        """验证模型"""
        self.model.eval()
        total_loss = 0
        predictions = []
        targets = []
        
        with torch.no_grad():
            for batch in val_loader:
                batch = batch.to(self.device)
                
                output = self.model(batch.x, batch.edge_index, batch.edge_attr, batch.batch)
                loss = self.criterion(output.squeeze(), batch.y)
                
                total_loss += loss.item()
                predictions.extend(output.squeeze().cpu().numpy())
                targets.extend(batch.y.cpu().numpy())
        
        # 计算R²
        r2 = r2_score(targets, predictions)
        
        return total_loss / len(val_loader), r2
    
    def train(self, train_loader: DataLoader, val_loader: DataLoader, 
              epochs: int = 100, patience: int = 20) -> Dict:
        """
        训练模型
        
        Args:
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
            epochs: 训练轮数
            patience: 早停耐心值
            
        Returns:
            训练历史
        """
        print(f"🚀 Starting training for {epochs} epochs")
        
        best_val_loss = float('inf')
        patience_counter = 0
        
        for epoch in range(epochs):
            # 训练
            train_loss, train_r2 = self.train_epoch(train_loader)
            
            # 验证
            val_loss, val_r2 = self.validate(val_loader)
            
            # 记录历史
            self.train_losses.append(train_loss)
            self.val_losses.append(val_loss)
            self.train_r2s.append(train_r2)
            self.val_r2s.append(val_r2)
            
            # 打印进度
            if epoch % 10 == 0:
                print(f"Epoch {epoch:3d}: Train Loss={train_loss:.4f}, Val Loss={val_loss:.4f}, "
                      f"Train R²={train_r2:.3f}, Val R²={val_r2:.3f}")
            
            # 早停检查
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                # 保存最佳模型
                torch.save(self.model.state_dict(), 'best_dynamic_subgraph_model.pth')
            else:
                patience_counter += 1
                
                if patience_counter >= patience:
                    print(f"Early stopping at epoch {epoch}")
                    break
        
        print(f"✅ Training completed!")
        print(f"   Best validation loss: {best_val_loss:.4f}")
        print(f"   Final train R²: {self.train_r2s[-1]:.3f}")
        print(f"   Final val R²: {self.val_r2s[-1]:.3f}")
        
        return {
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'train_r2s': self.train_r2s,
            'val_r2s': self.val_r2s,
            'best_val_loss': best_val_loss
        }
    
    def plot_training_curves(self, save_path: str = 'training_curves.png'):
        """绘制训练曲线"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
        
        # 损失曲线
        ax1.plot(self.train_losses, label='Training Loss')
        ax1.plot(self.val_losses, label='Validation Loss')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.set_title('Training Progress')
        ax1.legend()
        ax1.set_yscale('log')
        
        # R²曲线
        ax2.plot(self.train_r2s, label='Training R²')
        ax2.plot(self.val_r2s, label='Validation R²')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('R² Score')
        ax2.set_title('Model Performance')
        ax2.legend()
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"📊 Training curves saved to {save_path}")

def train_dynamic_subgraph_model():
    """训练动态子图模型的主函数"""
    print("🎯 Training Dynamic Subgraph GNN")
    print("=" * 50)
    
    # 检查设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️  Using device: {device}")
    
    # 加载数据集
    dataset = SubgraphDataset('physics_based_dataset.pkl')
    data_list = dataset.to_pytorch_geometric()
    
    # 过滤有效数据
    valid_data = []
    for data in data_list:
        if data.x.size(0) > 0 and not torch.isnan(data.y).any():
            valid_data.append(data)
    
    print(f"📊 Valid samples: {len(valid_data)}")
    
    if len(valid_data) < 10:
        print("❌ Not enough valid samples for training")
        return
    
    # 分割数据集
    train_data, val_data = train_test_split(valid_data, test_size=0.2, random_state=42)
    
    # 创建数据加载器
    train_loader = DataLoader(train_data, batch_size=16, shuffle=True)
    val_loader = DataLoader(val_data, batch_size=16, shuffle=False)
    
    print(f"📈 Training samples: {len(train_data)}")
    print(f"📉 Validation samples: {len(val_data)}")
    
    # 创建模型
    model = DynamicSubgraphGNN(
        node_features=2,
        edge_features=2,
        hidden_dim=64,
        num_layers=3
    )
    
    # 创建训练器
    trainer = DynamicSubgraphTrainer(model, device)
    
    # 训练模型
    history = trainer.train(train_loader, val_loader, epochs=100, patience=20)
    
    # 绘制训练曲线
    trainer.plot_training_curves('dynamic_subgraph_training_curves.png')
    
    # 保存训练历史
    with open('dynamic_subgraph_training_history.pkl', 'wb') as f:
        pickle.dump(history, f)
    
    print(f"✅ Training completed and results saved!")
    
    return model, history

if __name__ == "__main__":
    train_dynamic_subgraph_model() 