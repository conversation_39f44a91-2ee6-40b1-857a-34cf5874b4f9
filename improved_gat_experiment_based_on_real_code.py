#!/usr/bin/env python3
"""
基于用户真实GAT代码改进的光网络QoT预测实验
使用真实的日本网络拓扑和特征设计
"""

import time
import numpy as np
import networkx as nx
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
import dgl
from sklearn.preprocessing import MinMaxScaler
from sklearn.model_selection import train_test_split
from dgl.nn.pytorch.conv import GATConv
import dgl.function as fn
from dgl.dataloading import GraphDataLoader
import copy
import json
from datetime import datetime
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

class MLPPredictor(nn.Module):
    """基于用户代码的MLP预测器 - 用于边预测任务"""
    def __init__(self, num_layers_linear, in_features, num_hidden_linear, num_classes, activation):
        super().__init__()
        self.gat_layers = nn.ModuleList()
        self.activation = activation
        self.num_layers_linear = num_layers_linear
        
        if num_layers_linear == 1:
            self.gat_layers.append(torch.nn.Linear(in_features * 2, num_classes))
        elif num_layers_linear == 2:
            self.gat_layers.append(torch.nn.Linear(in_features * 2, num_hidden_linear))
            self.gat_layers.append(torch.nn.Linear(num_hidden_linear, num_classes))
        else:
            self.gat_layers.append(torch.nn.Linear(in_features * 2, num_hidden_linear))
            for l in range(1, num_layers_linear - 1):
                self.gat_layers.append(torch.nn.Linear(num_hidden_linear, num_hidden_linear))
            self.gat_layers.append(torch.nn.Linear(num_hidden_linear, num_classes))

    def apply_edges(self, edges):
        h_u = edges.src['h']
        h_v = edges.dst['h']
        h = torch.cat([h_u, h_v], 1)
        for l in range(0, self.num_layers_linear - 1):
            h = self.activation(self.gat_layers[l](h))
        score = self.gat_layers[-1](h)  # 移除最后的激活函数用于回归
        return {'score': score}

    def forward(self, graph, h):
        with graph.local_scope():
            graph.ndata['h'] = h
            graph.apply_edges(self.apply_edges)
            return graph.edata['score']

class ImprovedGAT(nn.Module):
    """基于用户代码改进的GAT模型"""
    def __init__(self, num_layers, num_layers_linear, in_dim, num_hidden, 
                 num_hidden_linear, num_classes, heads, activation, 
                 feat_drop, attn_drop, negative_slope, residual):
        super(ImprovedGAT, self).__init__()
        self.num_layers = num_layers
        self.num_layers_linear = num_layers_linear
        self.gat_layers = nn.ModuleList()
        self.activation = activation
        
        # input projection (no residual)
        self.gat_layers.append(GATConv(
            in_dim, num_hidden, heads[0],
            feat_drop, attn_drop, negative_slope, False, self.activation))
        
        # hidden layers
        for l in range(1, num_layers):
            self.gat_layers.append(GATConv(
                num_hidden * heads[l - 1], num_hidden, heads[l],
                feat_drop, attn_drop, negative_slope, residual, self.activation))
        
        # output projection
        self.gat_layers.append(GATConv(
            num_hidden * heads[-2], num_hidden, heads[-1],
            feat_drop, attn_drop, negative_slope, residual, None))
        
        # 边预测器
        self.pred = MLPPredictor(num_layers_linear, num_hidden * heads[-1], 
                                num_hidden_linear, num_classes, activation)

    def forward(self, g, inputs):
        h = inputs
        for l in range(self.num_layers):
            h = self.gat_layers[l](g, h).flatten(1)
        
        # output projection
        logits = self.gat_layers[self.num_layers](g, h).mean(1)
        h = logits
        
        return self.pred(g, h)

class JapanNetworkDataGenerator:
    """基于用户代码的日本网络数据生成器"""
    
    def __init__(self):
        self.channel_num = 80
        self.wavelength_start = 191.3e12
        self.wavelength_spacing = 50e9
        self.wavelength = np.zeros((1, self.channel_num))
        
        # 生成波长序列
        for i in range(self.channel_num):
            self.wavelength[0, i] = self.wavelength_start + i * self.wavelength_spacing
            
        # 创建日本网络拓扑（基于用户代码注释）
        self.create_japan_network()
        
    def create_japan_network(self):
        """创建真实的日本网络拓扑"""
        # 基于用户代码注释中的连接信息
        u = torch.tensor([0,0,1,2,3,3,4,4,4,5,6,6,7,8,11,9,8,13,11,11,13,10])
        v = torch.tensor([1,2,3,4,4,5,5,8,6,7,7,11,9,11,9,10,13,11,10,12,12,12])
        
        # 链路长度矩阵（来自用户代码）
        self.link_length = np.array([
            [0,160,240,0,0,0,0,0,0,0,0,0,0,0],
            [160,0,0,240,0,0,0,0,0,0,0,0,0,0],
            [240,0,0,0,240,0,0,0,0,0,0,0,0,0],
            [0,240,0,0,80,40,0,0,0,0,0,0,0,0],
            [0,0,240,80,0,40,80,0,240,0,0,0,0,0],
            [0,0,0,40,40,0,0,160,0,0,0,0,0,0],
            [0,0,0,0,80,0,0,80,0,0,0,240,0,0],
            [0,0,0,0,0,160,80,0,0,160,0,0,0,0],
            [0,0,0,0,240,0,0,0,0,0,0,240,0,240],
            [0,0,0,0,0,0,0,160,0,0,40,40,0,0],
            [0,0,0,0,0,0,0,0,0,40,0,40,320,0],
            [0,0,0,0,0,0,240,0,240,40,40,0,320,240],
            [0,0,0,0,0,0,0,0,0,0,320,320,0,160],
            [0,0,0,0,0,0,0,0,240,0,0,240,160,0]
        ])
        
        # 创建DGL图
        self.g = dgl.graph((u, v))
        self.g = self.g.int()
        self.g = dgl.to_bidirected(self.g)
        
        # 归一化链路长度
        self.link_scalar = self._max_min_scalar(self.link_length, single=True)
        
        print(f"🌐 创建日本网络拓扑:")
        print(f"   节点数: {self.g.num_nodes()}")
        print(f"   边数: {self.g.num_edges()}")
        print(f"   波长数: {self.channel_num}")
    
    def _max_min_scalar(self, data, single=False, wavelength=None):
        """基于用户代码的归一化函数"""
        if single:  # 对于单个矩阵
            max_value = np.max(data)
            min_value = np.min(data)
            if max_value == min_value:
                return np.zeros_like(data)
            return (data - min_value) / (max_value - min_value)
        else:  # 对于复合矩阵
            # 这里简化处理，实际应该按用户代码实现
            return data
    
    def generate_realistic_network_scenarios(self, num_scenarios: int = 1000) -> List:
        """生成真实的网络场景数据"""
        print(f"🔄 生成 {num_scenarios} 个网络场景...")
        
        scenarios = []
        
        for i in range(num_scenarios):
            if i % 100 == 0:
                print(f"   进度: {i}/{num_scenarios}")
            
            # 生成节点特征（发射功率）
            node_power = np.random.uniform(-3, 3, (self.g.num_nodes(), 1))
            
            # 生成波长分配矩阵
            wavelength_allocation = self._generate_wavelength_allocation()
            
            # 生成OSNR目标值
            osnr_targets = self._generate_osnr_targets()
            
            # 创建图副本
            g_copy = copy.deepcopy(self.g)
            
            # 设置初始节点特征（只有功率）
            g_copy.ndata["nfeat"] = torch.from_numpy(node_power).type(torch.float32)
            
            # 设置边特征
            e_featW = torch.zeros((g_copy.num_edges(), self.channel_num))
            e_featL = torch.zeros((g_copy.num_edges(), 1))
            
            # 填充边特征
            for j in range(len(wavelength_allocation)):
                for k in range(len(wavelength_allocation)):
                    if g_copy.has_edges_between(j, k):
                        edge_id = g_copy.edge_ids(j, k)
                        e_featW[edge_id, :] = torch.from_numpy(wavelength_allocation[j, k])
                        e_featL[edge_id, 0] = self.link_scalar[j, k]
            
            g_copy.edata["efeatW"] = e_featW
            g_copy.edata["efeatL"] = e_featL
            
            # 更新节点特征（聚合边特征）- 这里会改变节点特征维度
            # 保存原始功率特征
            original_power = g_copy.ndata["nfeat"].clone()
            
            # 聚合波长特征
            g_copy.update_all(fn.e_add_v('efeatW', 'nfeat', 'm'), fn.sum('m', 'nfeat'))
            wavelength_aggregated = g_copy.ndata["nfeat"].clone()
            
            # 重置为原始功率特征
            g_copy.ndata["nfeat"] = original_power
            
            # 聚合链路长度特征  
            g_copy.update_all(fn.e_mul_v('efeatL', 'nfeat', 'm'), fn.sum('m', 'nfeat'))
            length_aggregated = g_copy.ndata["nfeat"].clone()
            
            # 合并所有特征：原始功率 + 波长聚合 + 长度聚合
            final_features = torch.cat([
                original_power,           # 1维
                wavelength_aggregated,    # 80维
                length_aggregated         # 1维
            ], dim=1)  # 总共 1+80+1 = 82维
            
            g_copy.ndata["nfeat"] = final_features
            
            scenarios.append([g_copy, torch.from_numpy(osnr_targets).type(torch.float32)])
        
        print(f"✅ 场景生成完成! 总数: {len(scenarios)}")
        return scenarios
    
    def _generate_wavelength_allocation(self):
        """生成波长分配矩阵"""
        n_nodes = self.g.num_nodes()
        allocation = np.zeros((n_nodes, n_nodes, self.channel_num))
        
        # 随机分配波长（简化）
        for i in range(n_nodes):
            for j in range(n_nodes):
                if self.link_length[i, j] > 0:  # 如果有链路连接
                    # 随机选择一些波长进行分配
                    num_channels_used = np.random.randint(1, min(10, self.channel_num))
                    used_channels = np.random.choice(self.channel_num, num_channels_used, replace=False)
                    for ch in used_channels:
                        allocation[i, j, ch] = 1.0 * self.wavelength[0, ch] / 1e12  # 归一化
        
        return allocation
    
    def _generate_osnr_targets(self):
        """生成OSNR目标值（边级别的QoT预测）"""
        # 为每条边生成OSNR目标值
        n_edges = self.g.num_edges()
        osnr_targets = np.random.uniform(15, 40, (n_edges, self.channel_num))
        return osnr_targets

def train_improved_gat_model():
    """训练改进的GAT模型"""
    print("🌟 基于真实代码的改进GAT实验")
    print("=" * 60)
    
    # 设置设备
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🔧 使用设备: {device}")
    
    # 生成数据
    data_generator = JapanNetworkDataGenerator()
    scenarios = data_generator.generate_realistic_network_scenarios(num_scenarios=800)
    
    # 划分数据集
    train_size = 0.8
    train_scenarios, test_scenarios = train_test_split(scenarios, train_size=train_size, random_state=42)
    
    print(f"📊 数据划分: 训练{len(train_scenarios)}, 测试{len(test_scenarios)}")
    
    # 创建数据加载器
    batch_size = 16
    train_loader = GraphDataLoader(dataset=train_scenarios, batch_size=batch_size, shuffle=True)
    test_loader = GraphDataLoader(dataset=test_scenarios, batch_size=batch_size, shuffle=False)
    
    # 模型超参数（基于用户代码）
    num_heads = 3
    num_out_heads = 1
    num_layers = 2
    num_layers_linear = 3
    heads = ([num_heads] * num_layers) + [num_out_heads]
    in_feats = 82  # 节点特征维度（功率1维 + 波长聚合80维 + 长度聚合1维）
    num_hidden = 64
    num_hidden_linear = 128
    num_out = 80  # 输出波长数
    
    # 创建模型
    model = ImprovedGAT(
        num_layers=num_layers,
        num_layers_linear=num_layers_linear,
        in_dim=in_feats,
        num_hidden=num_hidden,
        num_hidden_linear=num_hidden_linear,
        num_classes=num_out,
        heads=heads,
        activation=F.elu,
        feat_drop=0.0,
        attn_drop=0.0,
        negative_slope=0.0,
        residual=False
    ).to(device)
    
    print(f"🧠 模型参数: {sum(p.numel() for p in model.parameters())} 个")
    
    # 训练设置
    loss_fcn = torch.nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=1e-3, weight_decay=1e-5)
    
    # 训练过程
    epoch_num = 100
    train_losses = []
    test_losses = []
    best_test_loss = float('inf')
    
    print(f"🚀 开始训练...")
    
    for epoch in range(epoch_num):
        model.train()
        train_loss_epoch = 0
        train_batches = 0
        
        start_time = time.time()
        
        for batched_graph, labels in train_loader:
            batched_graph = batched_graph.to(device)
            labels = labels.to(device)
            
            # 前向传播
            features = batched_graph.ndata["nfeat"]
            logits = model(batched_graph, features)
            
            # 重塑标签以匹配边预测输出
            labels = labels.view(-1, labels.shape[-1])
            
            loss = loss_fcn(logits, labels)
            
            # 反向传播
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            train_loss_epoch += loss.item()
            train_batches += 1
        
        avg_train_loss = train_loss_epoch / train_batches
        train_losses.append(avg_train_loss)
        
        # 测试评估
        model.eval()
        test_loss_epoch = 0
        test_batches = 0
        
        with torch.no_grad():
            for batched_graph, labels in test_loader:
                batched_graph = batched_graph.to(device)
                labels = labels.to(device)
                
                features = batched_graph.ndata["nfeat"]
                logits = model(batched_graph, features)
                labels = labels.view(-1, labels.shape[-1])
                
                loss = loss_fcn(logits, labels)
                test_loss_epoch += loss.item()
                test_batches += 1
        
        avg_test_loss = test_loss_epoch / test_batches
        test_losses.append(avg_test_loss)
        
        epoch_time = time.time() - start_time
        
        if epoch % 10 == 0:
            print(f"Epoch {epoch:3d}: Train Loss={avg_train_loss:.4f}, "
                  f"Test Loss={avg_test_loss:.4f}, Time={epoch_time:.2f}s")
        
        # 保存最佳模型
        if avg_test_loss < best_test_loss:
            best_test_loss = avg_test_loss
            torch.save(model.state_dict(), 'best_improved_gat_model.pth')
    
    print(f"✅ 训练完成! 最佳测试损失: {best_test_loss:.4f}")
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results = {
        'experiment_info': {
            'timestamp': timestamp,
            'device': device,
            'model_parameters': sum(p.numel() for p in model.parameters()),
            'training_samples': len(train_scenarios),
            'test_samples': len(test_scenarios),
            'model_type': 'ImprovedGAT_EdgePrediction'
        },
        'model_config': {
            'num_layers': num_layers,
            'num_layers_linear': num_layers_linear,
            'in_feats': in_feats,
            'num_hidden': num_hidden,
            'num_hidden_linear': num_hidden_linear,
            'num_heads': num_heads,
            'num_out': num_out
        },
        'results': {
            'best_test_loss': float(best_test_loss),
            'final_train_loss': float(train_losses[-1]),
            'final_test_loss': float(test_losses[-1])
        },
        'training_history': {
            'train_losses': train_losses,
            'test_losses': test_losses
        }
    }
    
    results_file = f'improved_gat_results_{timestamp}.json'
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"📁 结果保存至: {results_file}")
    print(f"📁 模型保存至: best_improved_gat_model.pth")
    
    return results

if __name__ == "__main__":
    results = train_improved_gat_model() 