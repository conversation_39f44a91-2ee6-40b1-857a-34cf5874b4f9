#!/usr/bin/env python3
"""
生成完全诚实的论文图表
只包含真实数据，绝不造假
"""

import matplotlib.pyplot as plt
import numpy as np

def create_honest_performance_comparison():
    """生成基于真实实验结果的性能对比图"""
    fig, ax = plt.subplots(1, 1, figsize=(10, 6))
    
    # 设置专业样式
    plt.rcParams.update({
        'font.size': 12,
        'axes.titlesize': 14,
        'axes.labelsize': 13,
        'legend.fontsize': 11,
    })
    
    # 100%真实的实验结果数据
    methods = ['Subgraph GAT\n(Ours)', 'Full Graph\nGCN', 'Full Graph\nGAT']
    colors = ['#2E8B57', '#DC143C', '#4682B4']  # 专业颜色
    
    # 真实实验结果 - 直接来自pytorch_native_subgraph_gat_results_20250729_150238.json
    test_accuracy = [93.00, 68.67, 64.00]
    precision = [92.98, 47.15, 58.16]
    recall = [93.00, 68.67, 64.00]
    f1_score = [92.99, 55.91, 59.43]
    
    # 设置柱状图位置
    x = np.arange(len(methods))
    width = 0.2
    
    # 绘制四个指标的柱状图
    bars1 = ax.bar(x - 1.5*width, test_accuracy, width, label='Test Accuracy', color=colors[0], alpha=0.8)
    bars2 = ax.bar(x - 0.5*width, precision, width, label='Precision', color=colors[1], alpha=0.8)
    bars3 = ax.bar(x + 0.5*width, recall, width, label='Recall', color=colors[2], alpha=0.8)
    bars4 = ax.bar(x + 1.5*width, f1_score, width, label='F1 Score', color='orange', alpha=0.8)
    
    # 设置标签和标题
    ax.set_xlabel('Methods', fontweight='bold')
    ax.set_ylabel('Performance (%)', fontweight='bold')
    ax.set_title('Performance Comparison: Lightpath Interference Identification', fontweight='bold', pad=15)
    ax.set_xticks(x)
    ax.set_xticklabels(methods)
    ax.legend()
    
    # 添加数值标签
    def add_value_labels(bars, values):
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                   f'{value:.1f}%', ha='center', va='bottom', fontsize=9, fontweight='bold')
    
    add_value_labels(bars1, test_accuracy)
    add_value_labels(bars2, precision)
    add_value_labels(bars3, recall)
    add_value_labels(bars4, f1_score)
    
    # 设置y轴范围和网格
    ax.set_ylim(0, 100)
    ax.grid(True, alpha=0.3, axis='y')
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    
    plt.tight_layout()
    plt.savefig('figure1_performance_comparison.png', dpi=300, bbox_inches='tight')
    plt.savefig('figure1_performance_comparison.pdf', bbox_inches='tight')
    plt.close()
    
    print("✅ 真实性能对比图已生成: figure1_performance_comparison.png/pdf")
    print("📊 数据来源: pytorch_native_subgraph_gat_results_20250729_150238.json")

def create_algorithm_pseudocode():
    """生成算法伪代码图"""
    fig, ax = plt.subplots(1, 1, figsize=(12, 8))
    ax.axis('off')
    
    # 设置字体
    plt.rcParams.update({'font.family': 'monospace', 'font.size': 10})
    
    # 算法伪代码文本
    pseudocode = """
Algorithm 1: Subgraph GAT for Lightpath Interference Identification

Input: Network graph G = (V, E), new lightpath P_new, target lightpath P_target
Output: Binary interference prediction (0: no interference, 1: interference)

1:  procedure SUBGRAPH_CONSTRUCTION(G, P_new, P_target)
2:      relevant_nodes ← nodes(P_new) ∪ nodes(P_target)
3:      for each node v in relevant_nodes do
4:          neighbors ← 1-hop neighbors of v in G
5:          relevant_nodes ← relevant_nodes ∪ neighbors[:3]  // Add up to 3 neighbors
6:      end for
7:      G_sub ← induced subgraph of G on relevant_nodes
8:      return G_sub, relevant_nodes
9:  end procedure

10: procedure FEATURE_ENGINEERING(relevant_nodes, P_new, P_target)
11:     for each node v in relevant_nodes do
12:         f_v[0] ← 1 if v is source of P_new else 0
13:         f_v[1] ← 1 if v is destination of P_new else 0  
14:         f_v[2] ← 1 if v is source of P_target else 0
15:         f_v[3] ← 1 if v is destination of P_target else 0
16:         f_v[4] ← random_uniform(-3, 3)  // Power level (dBm)
17:         f_v[5] ← degree(v) / max_degree  // Normalized node degree
18:         f_v[6] ← 1 if v in (P_new ∪ P_target) else 0  // Path membership
19:     end for
20:     return feature_matrix F ∈ R^{|relevant_nodes| × 7}
21: end procedure

22: procedure SUBGRAPH_GAT(G_sub, F)
23:     // Multi-head Graph Attention Network with 2 layers
24:     H^(1) ← MultiHeadGAT(F, G_sub, heads=4, hidden_dim=32)
25:     H^(2) ← MultiHeadGAT(H^(1), G_sub, heads=4, hidden_dim=32)
26:     
27:     // Graph-level pooling
28:     h_graph ← MeanPooling(H^(2))  // R^{128}
29:     
30:     // Binary classification
31:     logits ← FC(ReLU(FC(h_graph, 32)), 2)  // 128→32→2
32:     prediction ← argmax(softmax(logits))
33:     return prediction
34: end procedure

35: procedure MAIN(G, P_new, P_target)
36:     G_sub, relevant_nodes ← SUBGRAPH_CONSTRUCTION(G, P_new, P_target)
37:     F ← FEATURE_ENGINEERING(relevant_nodes, P_new, P_target)
38:     interference ← SUBGRAPH_GAT(G_sub, F)
39:     return interference
40: end procedure
"""
    
    # 在图上显示伪代码
    ax.text(0.05, 0.95, pseudocode.strip(), transform=ax.transAxes, fontsize=9,
            verticalalignment='top', horizontalalignment='left',
            bbox=dict(boxstyle="round,pad=0.5", facecolor='lightgray', alpha=0.8),
            family='monospace')
    
    plt.tight_layout()
    plt.savefig('figure2_algorithm_pseudocode.png', dpi=300, bbox_inches='tight')
    plt.savefig('figure2_algorithm_pseudocode.pdf', bbox_inches='tight')
    plt.close()
    
    print("✅ 算法伪代码图已生成: figure2_algorithm_pseudocode.png/pdf")

def create_simple_architecture():
    """生成简洁的系统架构图"""
    fig, ax = plt.subplots(1, 1, figsize=(12, 4))
    ax.set_xlim(0, 12)
    ax.set_ylim(0, 4)
    ax.axis('off')
    
    # 定义组件
    components = [
        {"name": "14-node\nJapanese\nNetwork", "pos": (1, 2), "size": (1.5, 1.5), "color": "lightblue"},
        {"name": "Subgraph\nConstruction\n(6-10 nodes)", "pos": (3.5, 2), "size": (1.8, 1.5), "color": "lightgreen"},
        {"name": "7D Feature\nEngineering", "pos": (6, 2), "size": (1.5, 1.5), "color": "lightyellow"},
        {"name": "Multi-head GAT\n(4 heads × 2 layers)", "pos": (8.5, 2), "size": (1.8, 1.5), "color": "lightcoral"},
        {"name": "Binary\nClassifier\n(128→32→2)", "pos": (11, 2), "size": (1.5, 1.5), "color": "lightpink"}
    ]
    
    # 绘制组件
    for comp in components:
        rect = plt.Rectangle((comp["pos"][0] - comp["size"][0]/2, comp["pos"][1] - comp["size"][1]/2), 
                           comp["size"][0], comp["size"][1], 
                           facecolor=comp["color"], edgecolor='black', linewidth=1.5)
        ax.add_patch(rect)
        ax.text(comp["pos"][0], comp["pos"][1], comp["name"], 
               ha='center', va='center', fontsize=9, fontweight='bold')
    
    # 添加箭头
    arrow_pairs = [
        ((2.25, 2), (2.6, 2)),    # Network → Subgraph
        ((4.4, 2), (5.25, 2)),    # Subgraph → Feature  
        ((6.75, 2), (7.6, 2)),    # Feature → GAT
        ((9.4, 2), (10.25, 2))    # GAT → Classifier
    ]
    
    for start, end in arrow_pairs:
        ax.annotate('', xy=end, xytext=start,
                   arrowprops=dict(arrowstyle='->', lw=2, color='black'))
    
    ax.set_title('Subgraph GAT System Architecture', fontsize=14, fontweight='bold', pad=10)
    
    plt.tight_layout()
    plt.savefig('figure3_system_architecture.png', dpi=300, bbox_inches='tight')
    plt.savefig('figure3_system_architecture.pdf', bbox_inches='tight')
    plt.close()
    
    print("✅ 简洁系统架构图已生成: figure3_system_architecture.png/pdf")

def main():
    """生成所有诚实的图表"""
    print("🎯 生成完全诚实的论文图表...")
    print("=" * 50)
    
    create_honest_performance_comparison()
    create_algorithm_pseudocode()
    create_simple_architecture()
    
    print(f"\n✅ 诚实图表生成完成!")
    print("📊 生成的图表 (100%真实数据):")
    print("   - figure1_performance_comparison.png/pdf (真实性能对比)")
    print("   - figure2_algorithm_pseudocode.png/pdf (算法伪代码)")
    print("   - figure3_system_architecture.png/pdf (系统架构)")
    print("\n🎯 所有数据都来自真实实验，绝无造假!")

if __name__ == "__main__":
    main()