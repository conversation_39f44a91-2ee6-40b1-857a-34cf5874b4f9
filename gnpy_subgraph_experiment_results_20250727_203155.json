{"timestamp": "20250727_203155", "experiment_type": "gnpy_based_honest_comparison", "data_info": {"train_samples": 800, "test_samples": 200, "osnr_range": [10.0, 10.0], "path_length_range": [200.19496728629497, 800.0], "gnpy_params": {"fiber_loss": 0.2, "fiber_length_range": [50, 500], "edfa_gain_range": [15, 25], "edfa_nf": 4.5, "wavelength_range": [1530, 1565], "power_range": [-10, 5], "nonlinear_coefficient": 0.0013, "dispersion": 17, "effective_area": 8e-11}}, "baseline_results": {"Linear Regression": {"r2": 1.0, "mse": 0.0, "mae": 0.0, "predictions": [10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0]}, "Random Forest": {"r2": 1.0, "mse": 0.0, "mae": 0.0, "predictions": [10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0]}, "SVM": {"r2": 1.0, "mse": 0.0, "mae": 0.0, "predictions": [10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0]}}, "subgraph_gnn_results": {"r2": 0.0, "mse": 0.031049514189362526, "mae": 0.13753916323184967, "predictions": [9.977505683898926, 9.974331855773926, 10.077284812927246, 9.622441291809082, 9.980327606201172, 10.022664070129395, 9.915413856506348, 9.85942554473877, 10.067282676696777, 9.482818603515625, 9.894375801086426, 9.866214752197266, 9.925399780273438, 9.99124813079834, 10.115050315856934, 9.834386825561523, 9.705434799194336, 9.809041023254395, 9.61751651763916, 10.061814308166504, 9.897360801696777, 9.874459266662598, 10.122532844543457, 9.889863014221191, 10.507612228393555, 10.033862113952637, 10.264368057250977, 9.843470573425293, 9.737451553344727, 10.053305625915527, 9.83219051361084, 9.81387996673584, 9.861239433288574, 10.006250381469727, 9.774504661560059, 9.90923023223877, 10.016534805297852, 9.924054145812988, 10.277669906616211, 9.989212989807129, 9.947925567626953, 10.088665962219238, 9.955760955810547, 10.182883262634277, 9.831942558288574, 9.70658016204834, 10.204115867614746, 10.051444053649902, 9.961276054382324, 9.883399963378906, 10.154607772827148, 9.859572410583496, 10.005898475646973, 10.122841835021973, 9.942578315734863, 9.924893379211426, 9.928921699523926, 10.13927173614502, 9.982921600341797, 10.04022216796875, 9.827658653259277, 10.012438774108887, 9.816412925720215, 9.789225578308105, 9.650973320007324, 9.977030754089355, 9.895803451538086, 9.951958656311035, 10.15157413482666, 10.203879356384277, 10.157822608947754, 9.866240501403809, 9.953390121459961, 10.01241683959961, 9.859139442443848, 10.231493949890137, 10.129040718078613, 10.197931289672852, 9.74437427520752, 9.940255165100098, 9.825697898864746, 10.22764778137207, 10.083569526672363, 9.964522361755371, 9.872954368591309, 9.751755714416504, 9.984098434448242, 9.96721076965332, 9.988377571105957, 9.902985572814941, 9.709761619567871, 9.924392700195312, 9.846292495727539, 9.963704109191895, 9.628161430358887, 10.09001636505127, 9.826303482055664, 9.727392196655273, 9.875594139099121, 9.850985527038574, 9.809670448303223, 10.00403881072998, 9.970816612243652, 10.118077278137207, 9.983616828918457, 10.525066375732422, 9.975005149841309, 9.760422706604004, 9.790170669555664, 9.916802406311035, 9.90926456451416, 9.780163764953613, 10.000751495361328, 10.002927780151367, 9.96981430053711, 10.20467472076416, 9.967415809631348, 10.028748512268066, 9.997688293457031, 10.106852531433105, 9.890357971191406, 9.936711311340332, 9.745965003967285, 9.96823787689209, 9.884276390075684, 10.213549613952637, 9.813714027404785, 9.881766319274902, 9.938679695129395, 9.995821952819824, 10.088531494140625, 9.879548072814941, 9.859480857849121, 10.015320777893066, 9.88635540008545, 9.813657760620117, 10.056464195251465, 9.937790870666504, 10.293209075927734, 10.15021800994873, 9.546402931213379, 9.771145820617676, 9.708719253540039, 9.961825370788574, 9.834823608398438, 10.022053718566895, 9.911005973815918, 10.002881050109863, 9.758845329284668, 9.999274253845215, 9.702322006225586, 10.035540580749512, 10.031003952026367, 9.676859855651855, 9.953801155090332, 10.053107261657715, 9.808075904846191, 10.177613258361816, 9.746465682983398, 9.77116870880127, 9.792757034301758, 9.722352981567383, 9.727433204650879, 9.767790794372559, 9.847050666809082, 9.772008895874023, 9.777918815612793, 10.037751197814941, 9.866704940795898, 9.815840721130371, 10.29627513885498, 10.19372844696045, 9.769087791442871, 10.08685302734375, 9.759413719177246, 9.858637809753418, 10.053936958312988, 10.026744842529297, 10.024622917175293, 9.881829261779785, 10.241496086120605, 9.893041610717773, 9.985758781433105, 9.892273902893066, 10.141961097717285, 9.970370292663574, 10.003860473632812, 10.009858131408691, 9.887036323547363, 9.618285179138184, 9.958337783813477, 9.848462104797363, 9.721138954162598, 9.795987129211426, 9.96084976196289, 9.490862846374512, 9.69395637512207, 10.087444305419922, 10.112588882446289, 9.953571319580078]}, "summary": {"best_baseline_method": "Linear Regression", "best_baseline_r2": 1.0, "subgraph_gnn_r2": 0.0, "improvement_percent": -100.0}}