import torch
import torch.nn as nn
from torch.utils.data import DataLoader, TensorDataset
import matplotlib.pyplot as plt
import numpy as np

# 1. 定义GNN模型
class PathQoTModel(nn.Module):
    def __init__(self, vocab_size, embedding_dim, hidden_dim, max_len):
        super(PathQoTModel, self).__init__()
        # 节点嵌入层：将每个节点的ID转换为一个密集向量
        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx=0)
        
        # GRU层：处理路径中节点的序列信息
        self.gru = nn.GRU(embedding_dim, hidden_dim, batch_first=True)
        
        # 回归器：结合路径信息和输入功率来预测SNR
        self.regressor = nn.Sequential(
            nn.Linear(hidden_dim + 1, hidden_dim // 2), # +1 for input power
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim // 2, 1)
        )
        
    def forward(self, path_ids, power):
        # path_ids shape: (batch_size, max_len)
        # power shape: (batch_size, 1)
        
        # 1. 嵌入路径
        embedded_path = self.embedding(path_ids) # (batch_size, max_len, embedding_dim)
        
        # 2. 通过GRU处理
        # 我们只需要最后一个时间步的隐藏状态
        _, h_n = self.gru(embedded_path) # h_n shape: (1, batch_size, hidden_dim)
        
        # 调整形状以进行拼接
        path_feature = h_n.squeeze(0) # (batch_size, hidden_dim)
        
        # 3. 拼接路径特征和功率
        combined_features = torch.cat([path_feature, power], dim=1)
        
        # 4. 预测SNR
        predicted_snr = self.regressor(combined_features)
        return predicted_snr

# 2. 准备数据加载器
def create_dataloaders(data_file, batch_size):
    print(f"正在从 '{data_file}' 加载数据...")
    data = torch.load(data_file)
    
    def get_tensors(dataset):
        paths = torch.stack([d['path'] for d in dataset])
        powers = torch.stack([d['power'] for d in dataset])
        snrs = torch.stack([d['snr'] for d in dataset])
        return TensorDataset(paths, powers, snrs)

    train_dataset = get_tensors(data['train_data'])
    val_dataset = get_tensors(data['val_data'])
    
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    
    print("数据加载器已创建。")
    return train_loader, val_loader, data['node_to_id'], data['max_len']

# 3. 训练和评估循环
def train_and_evaluate(model, train_loader, val_loader, epochs, learning_rate):
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)
    
    train_losses, val_losses = [], []
    best_val_loss = float('inf')
    
    for epoch in range(epochs):
        model.train()
        total_train_loss = 0
        for paths, powers, snrs in train_loader:
            optimizer.zero_grad()
            predictions = model(paths, powers)
            loss = criterion(predictions, snrs)
            loss.backward()
            optimizer.step()
            total_train_loss += loss.item()
            
        avg_train_loss = total_train_loss / len(train_loader)
        train_losses.append(avg_train_loss)
        
        model.eval()
        total_val_loss = 0
        with torch.no_grad():
            for paths, powers, snrs in val_loader:
                predictions = model(paths, powers)
                loss = criterion(predictions, snrs)
                total_val_loss += loss.item()
        
        avg_val_loss = total_val_loss / len(val_loader)
        val_losses.append(avg_val_loss)
        
        print(f"Epoch {epoch+1}/{epochs} | "
              f"Train Loss (MSE): {avg_train_loss:.4f} | "
              f"Val Loss (MSE): {avg_val_loss:.4f} | "
              f"Val RMSE: {np.sqrt(avg_val_loss):.4f}")
        
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            torch.save(model.state_dict(), 'best_gnn_qot_model.pth')
            print("  -> 模型已保存，因为验证损失降低。")
            
    return train_losses, val_losses

# 4. 绘图函数
def plot_losses(train_losses, val_losses):
    plt.figure(figsize=(10, 5))
    plt.plot(train_losses, label='Training Loss')
    plt.plot(val_losses, label='Validation Loss')
    plt.title('Training and Validation Losses')
    plt.xlabel('Epochs')
    plt.ylabel('MSE Loss')
    plt.legend()
    plt.grid(True)
    plt.savefig('gnn_training_curves.png')
    print("训练曲线图已保存为 'gnn_training_curves.png'")

# 5. 主执行函数
if __name__ == '__main__':
    # 超参数
    DATA_FILE = 'preprocessed_qot_data.pt'
    BATCH_SIZE = 32
    EMBEDDING_DIM = 64
    HIDDEN_DIM = 128
    EPOCHS = 100
    LEARNING_RATE = 0.001

    # 准备数据
    train_loader, val_loader, node_to_id, max_len = create_dataloaders(DATA_FILE, BATCH_SIZE)
    
    # 初始化模型
    vocab_size = len(node_to_id)
    model = PathQoTModel(vocab_size, EMBEDDING_DIM, HIDDEN_DIM, max_len)
    print("\n模型结构:")
    print(model)
    
    # 训练模型
    print("\n开始训练...")
    train_losses, val_losses = train_and_evaluate(model, train_loader, val_loader, EPOCHS, LEARNING_RATE)
    
    # 绘制结果
    plot_losses(train_losses, val_losses)
