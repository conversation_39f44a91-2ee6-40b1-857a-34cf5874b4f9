import json
import torch
import numpy as np
from sklearn.model_selection import train_test_split
from tqdm import tqdm
import os
import networkx as nx
from pathlib import Path

# 引入GNPY库，确保与验证脚本使用相同的网络加载方式
from gnpy.tools.json_io import load_network, load_equipment
from gnpy.core.elements import Transceiver

def create_node_mapping(network_data):
    """从网络拓扑数据中提取所有元素节点并创建ID映射。"""
    nodes = [element['uid'] for element in network_data['elements']]
    node_to_id = {node: i + 1 for i, node in enumerate(sorted(list(set(nodes))))}
    node_to_id['<PAD>'] = 0
    return node_to_id

def preprocess_data_consistent(dataset_path, network_path, equipment_path, output_path, max_len=None):
    """
    加载原始数据，进行预处理，并将其保存为PyTorch张量文件。
    这个版本使用gnpy.load_network来确保路径的一致性。
    """
    # 1. 加载网络文件并创建节点映射和图
    print("正在加载网络拓扑并创建节点映射和图 (使用GNPY官方加载器)...")
    try:
        equipment = load_equipment(Path(equipment_path))
        network_graph = load_network(Path(network_path), equipment)
    except Exception as e:
        print(f"FATAL: GNPy无法加载网络或设备文件: {e}")
        return

    with open(network_path, 'r') as f:
        network_data = json.load(f)
    node_to_id = create_node_mapping(network_data)
    id_to_node = {i: node for node, i in node_to_id.items()}
    uid_to_node_obj = {n.uid: n for n in network_graph.nodes()}

    print(f"图已创建，包含 {network_graph.number_of_nodes()} 个节点和 {network_graph.number_of_edges()} 条边。")
    print(f"节点词汇表已创建，大小为: {len(node_to_id)}")

    # 2. 加载QoT数据集
    print(f"正在加载QoT数据集 '{dataset_path}'...")
    with open(dataset_path, 'r') as f:
        qot_dataset = json.load(f)

    # 3. 处理数据
    print("正在处理数据集...")
    all_paths = []

    print("正在计算所有样本的最短路径...")
    for sample in tqdm(qot_dataset, desc="计算路径"):
        try:
            source_uid = sample['source'].replace(' ', '_')
            dest_uid = sample['destination'].replace(' ', '_')
            source_node = uid_to_node_obj.get(source_uid)
            dest_node = uid_to_node_obj.get(dest_uid)

            if not source_node or not dest_node:
                all_paths.append(None)
                continue

            path_objects = nx.shortest_path(network_graph, source=source_node, target=dest_node)
            path_uids = [node.uid for node in path_objects]
            all_paths.append(path_uids)
        except (nx.NetworkXNoPath, KeyError):
            all_paths.append(None)

    # 后续处理逻辑...
    valid_paths = [p for p in all_paths if p is not None]
    if not valid_paths:
        print("错误: 没有找到任何有效的路径。")
        return

    if max_len is None:
        max_len = max(len(p) for p in valid_paths)
        print(f"计算出的最大路径长度为: {max_len}")

    processed_samples = []
    for i, sample in enumerate(tqdm(qot_dataset, desc="预处理样本")):
        path_uids = all_paths[i]
        if path_uids is None:
            continue

        qot_result = sample.get('qot_result', {})
        snr = qot_result.get('snr_db')
        power = sample.get('input_power_dbm')

        if snr is None or power is None:
            continue

        encoded_path = [node_to_id.get(uid) for uid in path_uids]
        
        if None in encoded_path:
            continue

        padding_len = max_len - len(encoded_path)
        if padding_len > 0:
            encoded_path.extend([node_to_id['<PAD>']] * padding_len)
        elif padding_len < 0:
            encoded_path = encoded_path[:max_len]

        processed_samples.append({
            'path': torch.tensor(encoded_path, dtype=torch.long),
            'power': torch.tensor([power], dtype=torch.float),
            'snr': torch.tensor([snr], dtype=torch.float)
        })

    # 4. 划分数据集
    print("正在划分训练集、验证集和测试集...")
    train_val_samples, test_samples = train_test_split(processed_samples, test_size=0.1, random_state=42)
    train_samples, val_samples = train_test_split(train_val_samples, test_size=0.11, random_state=42)

    # 5. 保存处理后的数据
    print(f"正在保存处理后的数据到 '{output_path}'...")
    torch.save({
        'train_data': train_samples,
        'val_data': val_samples,
        'test_data': test_samples,
        'node_to_id': node_to_id,
        'id_to_node': id_to_node,
        'max_len': max_len
    }, output_path)

    print("\n预处理完成！")
    print(f"总有效样本数: {len(processed_samples)}")
    print(f"训练集大小: {len(train_samples)}")
    print(f"验证集大小: {len(val_samples)}")
    print(f"测试集大小: {len(test_samples)}")

if __name__ == '__main__':
    QOT_DATASET_PATH = 'clean_qot_dataset.json'
    NETWORK_TOPOLOGY_PATH = 'gnpy_network_japan_ntt.json'
    EQUIPMENT_PATH = 'default_equipment.json'
    PREPROCESSED_OUTPUT_PATH = 'preprocessed_qot_data.pt'

    preprocess_data_consistent(
        dataset_path=QOT_DATASET_PATH,
        network_path=NETWORK_TOPOLOGY_PATH,
        equipment_path=EQUIPMENT_PATH,
        output_path=PREPROCESSED_OUTPUT_PATH
    )