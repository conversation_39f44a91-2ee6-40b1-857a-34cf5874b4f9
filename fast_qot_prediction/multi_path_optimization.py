#!/usr/bin/env python3
"""
多路径QoT比较和路由优化
基于真实拓扑和复杂物理层建模的智能路由选择
"""

import numpy as np
import networkx as nx
import json
from pathlib import Path
import matplotlib.pyplot as plt
import heapq
import sys
sys.path.append('.')

from advanced_drl_with_real_topology import RealNetworkTopology, ComplexPhysicalModel

class MultiPathOptimizer:
    """多路径QoT优化器"""
    
    def __init__(self):
        self.topology_manager = RealNetworkTopology()
        self.physical_model = ComplexPhysicalModel()
        self.topology = self.topology_manager.G
        
        print(f"初始化多路径优化器")
        print(f"网络拓扑: NSFNet ({self.topology.number_of_nodes()}节点, {self.topology.number_of_edges()}边)")
    
    def find_k_shortest_paths(self, src, dst, k=5):
        """寻找K条最短路径"""
        
        def path_length(path):
            """计算路径总长度"""
            length = 0
            for i in range(len(path) - 1):
                length += self.topology[path[i]][path[i+1]]["distance_km"]
            return length
        
        try:
            # 先尝试寻找最短路径
            if not nx.has_path(self.topology, src, dst):
                print(f"节点{src}到{dst}之间没有路径")
                return []
            
            # 使用更大的cutoff来寻找路径，基于网络直径
            network_diameter = nx.diameter(self.topology) if nx.is_connected(self.topology) else 10
            cutoff = min(network_diameter + 2, 8)  # 最多8跳
            
            # 使用networkx寻找所有简单路径
            all_paths = list(nx.all_simple_paths(self.topology, src, dst, cutoff=cutoff))
            
            # 如果仍然没有找到路径，使用最短路径
            if not all_paths:
                shortest = nx.shortest_path(self.topology, src, dst)
                all_paths = [shortest]
                print(f"使用最短路径: {shortest} (长度: {len(shortest)-1}跳)")
            
            # 按长度排序并取前k条
            all_paths.sort(key=path_length)
            k_paths = all_paths[:k]
            
            # 构建详细路径信息
            detailed_paths = []
            for path_nodes in k_paths:
                path_edges = []
                total_distance = 0
                
                for i in range(len(path_nodes) - 1):
                    s, d = path_nodes[i], path_nodes[i+1]
                    edge_data = self.topology[s][d].copy()
                    
                    # 添加EDFA参数
                    distance = edge_data["distance_km"]
                    n_spans = max(1, distance // 80)
                    
                    edge_data.update({
                        "spans": n_spans,
                        "span_length_km": distance / n_spans,
                        "edfa_gain_db": 20,
                        "edfa_noise_figure_db": 5,
                        "roadm_insertion_loss_db": 4,  # 使用优化后的4dB插损
                        "fiber_nonlinear_coeff_per_w_per_m": 1.3e-3,
                        "fiber_dispersion_ps_nm_km": 17.0,
                        "fiber_effective_area_um2": 80,
                        "pmd_coeff_ps_per_sqrt_km": 0.1,
                        "wss_isolation_db": 50
                    })
                    
                    path_edges.append(edge_data)
                    total_distance += distance
                
                detailed_paths.append({
                    "path_nodes": path_nodes,
                    "path_edges": path_edges,
                    "total_distance": total_distance,
                    "hops": len(path_nodes) - 1
                })
            
            return detailed_paths
            
        except nx.NetworkXNoPath:
            print(f"没有找到从节点{src}到节点{dst}的路径")
            return []
    
    def calculate_path_qot(self, path_info, channel_config, network_load=0.3):
        """计算路径的QoT性能"""
        
        try:
            # 构建光路请求
            lightpath_request = {
                "path_edges": path_info["path_edges"],
                "channel": channel_config
            }
            
            # 模拟网络状态
            network_state = {
                "active_channels": [channel_config],
                "network_load": network_load
            }
            
            # 计算综合OSNR
            osnr_result = self.physical_model.calculate_comprehensive_osnr(
                lightpath_request, network_state)
            
            # 计算QoT评分 (综合考虑OSNR、距离、跳数等)
            osnr_db = osnr_result["osnr_db"]
            osnr_score = min(osnr_db / 30.0, 1.0)  # 归一化到[0,1]
            
            distance_penalty = min(path_info["total_distance"] / 3000.0, 1.0)
            hop_penalty = min(path_info["hops"] / 5.0, 1.0)
            
            qot_score = osnr_score * 0.7 - distance_penalty * 0.2 - hop_penalty * 0.1
            
            return {
                "osnr_db": osnr_db,
                "osnr_score": osnr_score,
                "distance_penalty": distance_penalty,
                "hop_penalty": hop_penalty,
                "qot_score": qot_score,
                "feasible": osnr_db >= 12.0,  # 降低阈值到12dB，更现实
                "margin_db": osnr_db - 12.0,
                "noise_breakdown": osnr_result["noise_breakdown"],
                "impairments": osnr_result["impairments"]
            }
            
        except Exception as e:
            print(f"路径QoT计算错误: {e}")
            return {
                "osnr_db": -100,
                "qot_score": -1,
                "feasible": False,
                "error": str(e)
            }
    
    def optimize_routing(self, src, dst, channel_config, k=5):
        """路由优化 - 选择最佳路径"""
        
        print(f"\n=== 路由优化: {src} -> {dst} ===")
        
        # 获取节点名称
        src_name = self.topology.nodes[src]["name"] if src in self.topology.nodes else f"Node{src}"
        dst_name = self.topology.nodes[dst]["name"] if dst in self.topology.nodes else f"Node{dst}"
        
        print(f"源节点: {src_name} (ID: {src})")
        print(f"目的节点: {dst_name} (ID: {dst})")
        print(f"信道: {channel_config['frequency']/1e12:.3f} THz, {channel_config['power_mw']} mW")
        
        # 寻找候选路径
        candidate_paths = self.find_k_shortest_paths(src, dst, k)
        
        if not candidate_paths:
            # 再次检查连通性
            if nx.has_path(self.topology, src, dst):
                return {
                    "status": "algorithm_error",
                    "message": f"路径搜索算法错误：节点{src}到{dst}理论上连通但未找到路径"
                }
            else:
                return {
                    "status": "no_path", 
                    "message": f"节点{src}到{dst}之间不存在路径"
                }
        
        print(f"找到 {len(candidate_paths)} 条候选路径")
        
        # 评估每条路径
        path_evaluations = []
        
        for i, path_info in enumerate(candidate_paths):
            qot_result = self.calculate_path_qot(path_info, channel_config)
            
            # 构建路径名称
            path_names = [self.topology.nodes[node]["name"] for node in path_info["path_nodes"]]
            path_name = " -> ".join(path_names)
            
            evaluation = {
                "path_id": i,
                "path_name": path_name,
                "path_nodes": path_info["path_nodes"],
                "distance_km": path_info["total_distance"],
                "hops": path_info["hops"],
                **qot_result
            }
            
            path_evaluations.append(evaluation)
            
            print(f"\n路径 {i+1}: {path_name}")
            print(f"  距离: {path_info['total_distance']:.0f} km, 跳数: {path_info['hops']}")
            print(f"  OSNR: {qot_result['osnr_db']:.2f} dB")
            print(f"  QoT评分: {qot_result['qot_score']:.3f}")
            print(f"  可行性: {'✓' if qot_result['feasible'] else '✗'}")
        
        # 选择最佳路径
        feasible_paths = [p for p in path_evaluations if p["feasible"]]
        
        if feasible_paths:
            # 按QoT评分排序
            best_path = max(feasible_paths, key=lambda x: x["qot_score"])
            optimization_result = {
                "status": "success",
                "best_path": best_path,
                "all_paths": path_evaluations,
                "n_feasible": len(feasible_paths),
                "n_total": len(path_evaluations)
            }
        else:
            # 没有可行路径，选择OSNR最高的
            best_path = max(path_evaluations, key=lambda x: x["osnr_db"])
            optimization_result = {
                "status": "no_feasible_path",
                "best_path": best_path,
                "all_paths": path_evaluations,
                "n_feasible": 0,
                "n_total": len(path_evaluations)
            }
        
        print(f"\n=== 优化结果 ===")
        print(f"状态: {optimization_result['status']}")
        print(f"最佳路径: {best_path['path_name']}")
        print(f"最佳OSNR: {best_path['osnr_db']:.2f} dB")
        print(f"可行路径数: {optimization_result['n_feasible']}/{optimization_result['n_total']}")
        
        return optimization_result
    
    def compare_routing_strategies(self, test_pairs, channel_config):
        """比较不同路由策略的性能"""
        
        print("\n" + "="*60)
        print("路由策略性能比较")
        print("="*60)
        
        strategies = {
            "shortest_path": "最短路径",
            "qot_optimized": "QoT优化",
            "osnr_maximized": "OSNR最大化"
        }
        
        comparison_results = {
            "strategies": strategies,
            "test_cases": [],
            "summary": {}
        }
        
        for src, dst in test_pairs:
            src_name = self.topology.nodes[src]["name"]
            dst_name = self.topology.nodes[dst]["name"]
            
            print(f"\n测试案例: {src_name} -> {dst_name}")
            
            # 获取候选路径
            candidate_paths = self.find_k_shortest_paths(src, dst, k=5)
            
            if not candidate_paths:
                continue
            
            # 评估所有路径
            path_evaluations = []
            for path_info in candidate_paths:
                qot_result = self.calculate_path_qot(path_info, channel_config)
                path_evaluations.append({
                    "path_info": path_info,
                    "qot_result": qot_result
                })
            
            # 不同策略选择的路径
            strategy_results = {}
            
            # 1. 最短路径策略
            shortest = min(path_evaluations, key=lambda x: x["path_info"]["total_distance"])
            strategy_results["shortest_path"] = {
                "path_info": shortest["path_info"],
                "performance": shortest["qot_result"]
            }
            
            # 2. QoT优化策略
            qot_optimal = max(path_evaluations, key=lambda x: x["qot_result"]["qot_score"])
            strategy_results["qot_optimized"] = {
                "path_info": qot_optimal["path_info"], 
                "performance": qot_optimal["qot_result"]
            }
            
            # 3. OSNR最大化策略
            osnr_optimal = max(path_evaluations, key=lambda x: x["qot_result"]["osnr_db"])
            strategy_results["osnr_maximized"] = {
                "path_info": osnr_optimal["path_info"],
                "performance": osnr_optimal["qot_result"]
            }
            
            # 打印比较结果
            print(f"{'策略':<12} {'距离(km)':<10} {'跳数':<6} {'OSNR(dB)':<10} {'QoT评分':<10} {'可行性'}")
            print("-" * 70)
            
            for strategy_id, strategy_name in strategies.items():
                result = strategy_results[strategy_id]
                path_info = result["path_info"]
                perf = result["performance"]
                
                print(f"{strategy_name:<12} "
                      f"{path_info['total_distance']:<10.0f} "
                      f"{path_info['hops']:<6} "
                      f"{perf['osnr_db']:<10.2f} "
                      f"{perf['qot_score']:<10.3f} "
                      f"{'✓' if perf['feasible'] else '✗'}")
            
            comparison_results["test_cases"].append({
                "src": src,
                "dst": dst,
                "src_name": src_name,
                "dst_name": dst_name,
                "strategy_results": strategy_results
            })
        
        # 生成总结统计
        strategy_stats = {strategy: {"wins": 0, "feasible": 0, "avg_osnr": 0, "avg_qot": 0} 
                         for strategy in strategies.keys()}
        
        for case in comparison_results["test_cases"]:
            best_qot_strategy = max(case["strategy_results"].keys(), 
                                  key=lambda s: case["strategy_results"][s]["performance"]["qot_score"])
            strategy_stats[best_qot_strategy]["wins"] += 1
            
            for strategy, result in case["strategy_results"].items():
                perf = result["performance"]
                if perf["feasible"]:
                    strategy_stats[strategy]["feasible"] += 1
                strategy_stats[strategy]["avg_osnr"] += perf["osnr_db"]
                strategy_stats[strategy]["avg_qot"] += perf["qot_score"]
        
        # 计算平均值
        n_cases = len(comparison_results["test_cases"])
        for strategy in strategy_stats:
            strategy_stats[strategy]["avg_osnr"] /= n_cases
            strategy_stats[strategy]["avg_qot"] /= n_cases
        
        comparison_results["summary"] = strategy_stats
        
        print(f"\n=== 策略性能总结 ===")
        print(f"{'策略':<12} {'获胜次数':<10} {'可行案例':<10} {'平均OSNR':<12} {'平均QoT'}")
        print("-" * 60)
        
        for strategy_id, strategy_name in strategies.items():
            stats = strategy_stats[strategy_id]
            print(f"{strategy_name:<12} "
                  f"{stats['wins']:<10} "
                  f"{stats['feasible']:<10} "
                  f"{stats['avg_osnr']:<12.2f} "
                  f"{stats['avg_qot']:<10.3f}")
        
        return comparison_results

def demonstrate_multi_path_optimization():
    """演示多路径优化功能"""
    
    print("="*60)
    print("多路径QoT比较和路由优化演示")
    print("="*60)
    
    # 初始化优化器
    optimizer = MultiPathOptimizer()
    
    # 测试信道配置
    channel_config = {
        "frequency": 193.4e12,  # 1550nm
        "power_mw": 1.0
    }
    
    # 1. 单个路由优化示例
    print("\n1. 单个路由优化示例")
    result = optimizer.optimize_routing(
        src=0,  # Seattle
        dst=11, # Princeton  
        channel_config=channel_config,
        k=3
    )
    
    # 2. 多对节点路由策略比较
    print("\n2. 多对节点路由策略比较")
    test_pairs = [
        (0, 11),   # Seattle -> Princeton
        (3, 9),    # San Diego -> Pittsburgh  
        (5, 10),   # Houston -> Ithaca
        (1, 13),   # Salt Lake City -> College Park
    ]
    
    comparison = optimizer.compare_routing_strategies(test_pairs, channel_config)
    
    # 3. 保存结果
    results = {
        "single_optimization": result,
        "strategy_comparison": comparison,
        "channel_config": channel_config
    }
    
    save_path = Path('/home/<USER>/DYcode/fast_qot_prediction/data/multi_path_optimization_results.json')
    save_path.parent.mkdir(exist_ok=True)
    
    # 处理不可序列化的对象
    def make_serializable(obj):
        if isinstance(obj, dict):
            return {k: make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [make_serializable(item) for item in obj]
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, (np.int64, np.int32)):
            return int(obj)
        elif isinstance(obj, (np.float64, np.float32)):
            return float(obj)
        elif isinstance(obj, (np.bool_, bool)):
            return bool(obj)
        elif hasattr(obj, '__dict__'):
            return str(obj)
        else:
            return obj
    
    serializable_results = make_serializable(results)
    
    with open(save_path, 'w', encoding='utf-8') as f:
        json.dump(serializable_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n结果已保存到: {save_path}")
    
    # 4. 性能总结
    print(f"\n" + "="*60)
    print("多路径优化总结")
    print("="*60)
    
    if result["status"] == "success":
        best_path = result["best_path"]
        print(f"✓ 单路由优化成功")
        print(f"  最佳路径: {best_path['path_name']}")
        print(f"  OSNR: {best_path['osnr_db']:.2f} dB")
        print(f"  距离: {best_path['distance_km']:.0f} km")
    
    summary = comparison["summary"]
    best_strategy = max(summary.keys(), key=lambda s: summary[s]["wins"])
    best_strategy_name = comparison["strategies"][best_strategy]
    
    print(f"✓ 策略比较完成")
    print(f"  最佳策略: {best_strategy_name}")
    print(f"  获胜次数: {summary[best_strategy]['wins']}/{len(test_pairs)}")
    print(f"  平均OSNR: {summary[best_strategy]['avg_osnr']:.2f} dB")
    
    print(f"\n多路径优化和路由策略比较功能已实现！")
    
    return optimizer, results

if __name__ == "__main__":
    optimizer, results = demonstrate_multi_path_optimization()