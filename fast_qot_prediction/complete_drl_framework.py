#!/usr/bin/env python3
"""
完整的DRL训练和评估框架
集成真实拓扑、复杂物理层建模和高级DRL智能体
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import random
import json
from pathlib import Path
from collections import deque, namedtuple
import matplotlib.pyplot as plt
import time
import sys
sys.path.append('.')

from advanced_drl_with_real_topology import RealNetworkTopology, ComplexPhysicalModel, AdvancedDRLAgent

# 经验回放记忆
Experience = namedtuple('Experience', ['state', 'action', 'reward', 'next_state', 'done'])

class ReplayBuffer:
    """经验回放缓冲区"""
    
    def __init__(self, capacity=10000):
        self.buffer = deque(maxlen=capacity)
    
    def push(self, *args):
        self.buffer.append(Experience(*args))
    
    def sample(self, batch_size):
        return random.sample(self.buffer, batch_size)
    
    def __len__(self):
        return len(self.buffer)

class OpticalNetworkEnvironment:
    """光网络环境模拟器"""
    
    def __init__(self, topology_name='NSFNet'):
        self.topology_manager = RealNetworkTopology()
        self.physical_model = ComplexPhysicalModel()
        self.topology = self.topology_manager.G  # 直接使用NSFNet图
        
        # 环境参数
        self.max_channels_per_link = 80  # C波段最大信道数
        self.osnr_threshold = 12.0  # 降低到12 dB，更现实
        self.current_channels = {}  # 每条链路上的激活信道
        self.current_step = 0
        self.max_steps = 100  # 减少每回合步数
        
        # 状态空间维度
        self.state_dim = 50
        self.action_dim = 3  # 接受、拒绝、重路由
        
        self.reset()
    
    def reset(self):
        """重置环境"""
        self.current_channels = {}
        self.current_step = 0
        
        # 初始化每条链路的信道状态
        for edge in self.topology.edges():
            self.current_channels[edge] = []
        
        return self._get_state()
    
    def _generate_lightpath_request(self):
        """生成随机光路请求"""
        nodes = list(self.topology.nodes())
        
        # 随机选择源和目的节点
        src = random.choice(nodes)
        dst = random.choice([n for n in nodes if n != src])
        
        # 寻找最短路径
        try:
            import networkx as nx
            path_nodes = nx.shortest_path(self.topology, src, dst)
        except nx.NetworkXNoPath:
            # 如果没有路径，随机选择邻接节点
            neighbors = list(self.topology.neighbors(src))
            if neighbors:
                dst = random.choice(neighbors)
                path_nodes = [src, dst]
            else:
                path_nodes = [src]
        
        # 构建路径边信息
        path_edges = []
        for i in range(len(path_nodes) - 1):
            s, d = path_nodes[i], path_nodes[i+1]
            edge_data = self.topology[s][d].copy()
            
            # 添加EDFA参数
            n_spans = max(1, edge_data["distance_km"] // 80)
            edge_data.update({
                "spans": n_spans,
                "span_length_km": edge_data["distance_km"] / n_spans,
                "edfa_gain_db": 20,
                "edfa_noise_figure_db": 5,
                "roadm_insertion_loss_db": 6,
                "fiber_nonlinear_coeff_per_w_per_m": 1.3e-3,
                "fiber_dispersion_ps_nm_km": 17.0,
                "fiber_effective_area_um2": 80,
                "pmd_coeff_ps_per_sqrt_km": 0.1,
                "wss_isolation_db": 50
            })
            
            path_edges.append(edge_data)
        
        # 随机选择频率和功率
        frequency = 193.4e12 + random.randint(-10, 10) * 50e9  # ±500GHz范围
        power_mw = random.uniform(0.5, 2.0)  # 0.5-2.0 mW
        
        channel = {
            "frequency": frequency,
            "power_mw": power_mw
        }
        
        return {
            "path_nodes": path_nodes,
            "path_edges": path_edges,
            "channel": channel,
            "src": src,
            "dst": dst
        }
    
    def _get_state(self):
        """获取当前环境状态"""
        state = np.zeros(self.state_dim)
        
        # 网络负载统计 (维度0-9)
        total_links = len(self.topology.edges())
        total_channels = sum(len(channels) for channels in self.current_channels.values())
        
        state[0] = total_channels / (total_links * 80)  # 全网信道利用率
        state[1] = self.current_step / self.max_steps    # 时间进度
        
        # 链路负载分布 (维度2-9)
        link_loads = []
        for edge in self.topology.edges():
            link_load = len(self.current_channels[edge]) / 80
            link_loads.append(link_load)
        
        if link_loads:
            state[2] = np.mean(link_loads)      # 平均链路负载
            state[3] = np.std(link_loads)       # 负载标准差
            state[4] = np.max(link_loads)       # 最大链路负载
            state[5] = np.min(link_loads)       # 最小链路负载
            state[6] = len([l for l in link_loads if l > 0.8]) / len(link_loads)  # 高负载链路比例
        
        # 网络连通性和拓扑特征 (维度10-19)
        state[10] = self.topology.number_of_nodes() / 50     # 归一化节点数
        state[11] = self.topology.number_of_edges() / 100    # 归一化边数
        
        # 其他特征用随机值填充 (实际应该是历史性能、预测等)
        state[20:] = np.random.normal(0, 0.1, self.state_dim - 20)
        
        return state.astype(np.float32)
    
    def step(self, lightpath_request, action):
        """执行动作并返回奖励"""
        reward = 0
        done = False
        
        try:
            # 获取路径上所有激活信道
            all_channels = []
            for i in range(len(lightpath_request["path_nodes"]) - 1):
                s, d = lightpath_request["path_nodes"][i], lightpath_request["path_nodes"][i+1]
                edge = (s, d) if (s, d) in self.current_channels else (d, s)
                if edge in self.current_channels:
                    all_channels.extend(self.current_channels[edge])
            
            # 添加请求信道
            all_channels.append(lightpath_request["channel"])
            
            # 计算OSNR
            network_state = {
                "active_channels": all_channels,
                "network_load": len(all_channels) / 80
            }
            
            osnr_result = self.physical_model.calculate_comprehensive_osnr(
                lightpath_request, network_state)
            
            osnr_db = osnr_result["osnr_db"]
            feasible = osnr_db >= self.osnr_threshold
            
            if action == 0:  # 拒绝
                if not feasible:
                    reward = 2.0  # 提高正确拒绝的奖励
                else:
                    # 错误拒绝可行请求的惩罚基于OSNR余量
                    margin = osnr_db - self.osnr_threshold
                    reward = -1.0 - margin * 0.2  # 余量越大惩罚越重
                    
            elif action == 1:  # 接受
                if feasible:
                    # 改进的奖励函数：基础奖励 + OSNR余量奖励 + 效率奖励
                    margin = osnr_db - self.osnr_threshold
                    base_reward = 3.0
                    margin_reward = margin * 0.3  # OSNR余量奖励
                    
                    # 路径效率奖励（短路径更好）
                    path_length = len(lightpath_request["path_nodes"]) - 1
                    efficiency_reward = max(0, (5 - path_length) * 0.2)
                    
                    reward = base_reward + margin_reward + efficiency_reward
                    
                    # 将信道添加到网络中
                    for i in range(len(lightpath_request["path_nodes"]) - 1):
                        s, d = lightpath_request["path_nodes"][i], lightpath_request["path_nodes"][i+1]
                        edge = (s, d) if (s, d) in self.current_channels else (d, s)
                        if edge in self.current_channels:
                            self.current_channels[edge].append(lightpath_request["channel"])
                else:
                    # 错误接受不可行请求的严重惩罚
                    margin = osnr_db - self.osnr_threshold
                    reward = -3.0 + margin * 0.1  # 基础惩罚，但OSNR越接近阈值惩罚越轻
                    
            elif action == 2:  # 重路由
                if not feasible:
                    reward = 1.0  # 对不可行请求的重路由给予奖励
                else:
                    # 对可行请求的重路由要看余量
                    margin = osnr_db - self.osnr_threshold
                    if margin < 2.0:  # 余量小于2dB，重路由有价值
                        reward = 0.5
                    else:  # 余量充足，重路由浪费
                        reward = -0.3
            
            # 网络负载相关的额外奖励/惩罚
            total_channels = sum(len(channels) for channels in self.current_channels.values())
            load_ratio = total_channels / (len(self.topology.edges()) * 10)  # 简化负载计算
            
            if load_ratio > 0.8:  # 高负载时更保守
                if action == 1:  # 接受时额外检查
                    reward -= 0.5
            elif load_ratio < 0.3:  # 低负载时更积极
                if action == 1 and feasible:
                    reward += 0.3
            
        except Exception as e:
            reward = -1.0  # 计算错误惩罚
            print(f"Environment step error: {e}")
        
        self.current_step += 1
        if self.current_step >= self.max_steps:
            done = True
        
        next_state = self._get_state()
        
        return next_state, reward, done, {
            "osnr_db": osnr_db if 'osnr_db' in locals() else 0,
            "feasible": feasible if 'feasible' in locals() else False,
            "path_length": len(lightpath_request["path_nodes"])
        }

class DRLTrainer:
    """DRL训练器"""
    
    def __init__(self, state_dim=50, action_dim=3, lr=5e-4):  # 降低学习率
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 网络初始化
        self.q_network = AdvancedDRLAgent(state_dim, action_dim).to(self.device)
        self.target_network = AdvancedDRLAgent(state_dim, action_dim).to(self.device)
        self.target_network.load_state_dict(self.q_network.state_dict())
        
        # 优化器
        self.optimizer = optim.Adam(self.q_network.parameters(), lr=lr)
        
        # 优化后的超参数
        self.epsilon = 1.0
        self.epsilon_min = 0.05  # 提高最小探索率
        self.epsilon_decay = 0.998  # 更缓慢的衰减
        self.gamma = 0.95  # 稍微降低折扣因子
        self.target_update_freq = 50  # 更频繁的目标网络更新
        self.batch_size = 64  # 增大批次大小
        
        # 增大经验回放容量
        self.replay_buffer = ReplayBuffer(capacity=100000)
        
        # 训练统计
        self.training_stats = {
            "episode_rewards": [],
            "epsilon_history": [],
            "loss_history": [],
            "success_rate": []
        }
    
    def select_action(self, state, training=True):
        """选择动作 (epsilon-greedy策略)"""
        if training and random.random() < self.epsilon:
            return random.randint(0, 2)
        
        with torch.no_grad():
            state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
            q_values = self.q_network(state_tensor)
            return q_values.argmax().item()
    
    def train_step(self):
        """单步训练"""
        if len(self.replay_buffer) < self.batch_size:
            return 0
        
        # 采样经验
        experiences = self.replay_buffer.sample(self.batch_size)
        batch = Experience(*zip(*experiences))
        
        # 转换为张量 - 修复数据类型问题
        states = np.array(batch.state, dtype=np.float32)
        next_states = np.array(batch.next_state, dtype=np.float32)
        
        state_batch = torch.from_numpy(states).to(self.device)
        action_batch = torch.LongTensor(batch.action).to(self.device)
        reward_batch = torch.FloatTensor(batch.reward).to(self.device)
        next_state_batch = torch.from_numpy(next_states).to(self.device)
        done_batch = torch.BoolTensor(batch.done).to(self.device)
        
        # 当前Q值
        current_q_values = self.q_network(state_batch).gather(1, action_batch.unsqueeze(1))
        
        # 目标Q值
        next_q_values = self.target_network(next_state_batch).max(1)[0].detach()
        target_q_values = reward_batch + (self.gamma * next_q_values * ~done_batch)
        
        # 计算损失
        loss = nn.MSELoss()(current_q_values.squeeze(), target_q_values)
        
        # 反向传播
        self.optimizer.zero_grad()
        loss.backward()
        self.optimizer.step()
        
        return loss.item()
    
    def train(self, env, num_episodes=1000, save_freq=100):
        """训练DRL智能体"""
        
        print(f"开始DRL训练，总回合数: {num_episodes}")
        print(f"设备: {self.device}")
        
        training_start_time = time.time()
        
        for episode in range(num_episodes):
            state = env.reset()
            episode_reward = 0
            episode_success = 0
            episode_requests = 0
            
            for step in range(env.max_steps):
                # 生成光路请求
                lightpath_request = env._generate_lightpath_request()
                
                # 选择动作
                action = self.select_action(state)
                
                # 执行动作
                next_state, reward, done, info = env.step(lightpath_request, action)
                
                # 存储经验
                self.replay_buffer.push(state, action, reward, next_state, done)
                
                # 训练
                loss = self.train_step()
                
                # 更新统计
                episode_reward += reward
                episode_requests += 1
                if info["feasible"] and action == 1:  # 正确接受
                    episode_success += 1
                elif not info["feasible"] and action == 0:  # 正确拒绝
                    episode_success += 1
                
                state = next_state
                
                if done:
                    break
            
            # 更新目标网络
            if episode % self.target_update_freq == 0:
                self.target_network.load_state_dict(self.q_network.state_dict())
            
            # 更新epsilon
            if self.epsilon > self.epsilon_min:
                self.epsilon *= self.epsilon_decay
            
            # 记录统计信息
            success_rate = episode_success / episode_requests if episode_requests > 0 else 0
            self.training_stats["episode_rewards"].append(episode_reward)
            self.training_stats["epsilon_history"].append(self.epsilon)
            self.training_stats["success_rate"].append(success_rate)
            if 'loss' in locals():
                self.training_stats["loss_history"].append(loss)
            
            # 打印进度
            if episode % 100 == 0:
                avg_reward = np.mean(self.training_stats["episode_rewards"][-100:])
                avg_success = np.mean(self.training_stats["success_rate"][-100:])
                
                print(f"回合 {episode:4d}: 平均奖励 = {avg_reward:7.2f}, "
                      f"成功率 = {avg_success:.3f}, epsilon = {self.epsilon:.3f}")
            
            # 保存模型
            if episode % save_freq == 0 and episode > 0:
                self.save_model(f"drl_model_episode_{episode}.pth")
        
        training_time = time.time() - training_start_time
        print(f"\n训练完成！总用时: {training_time:.1f}s")
        
        return self.training_stats
    
    def save_model(self, filename):
        """保存模型"""
        save_path = Path(f'/home/<USER>/DYcode/fast_qot_prediction/models/{filename}')
        save_path.parent.mkdir(exist_ok=True)
        
        torch.save({
            'q_network_state_dict': self.q_network.state_dict(),
            'target_network_state_dict': self.target_network.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'training_stats': self.training_stats,
            'epsilon': self.epsilon
        }, save_path)
        
        print(f"模型已保存到: {save_path}")
    
    def load_model(self, filename):
        """加载模型"""
        load_path = Path(f'/home/<USER>/DYcode/fast_qot_prediction/models/{filename}')
        
        if load_path.exists():
            checkpoint = torch.load(load_path, map_location=self.device)
            
            self.q_network.load_state_dict(checkpoint['q_network_state_dict'])
            self.target_network.load_state_dict(checkpoint['target_network_state_dict'])
            self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            self.training_stats = checkpoint['training_stats']
            self.epsilon = checkpoint['epsilon']
            
            print(f"模型已从 {load_path} 加载")
        else:
            print(f"模型文件 {load_path} 不存在")

def plot_training_results(stats, save_path=None):
    """绘制训练结果"""
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    
    # 回合奖励
    episodes = range(len(stats["episode_rewards"]))
    ax1.plot(episodes, stats["episode_rewards"], alpha=0.6, color='blue')
    
    # 移动平均
    if len(stats["episode_rewards"]) >= 100:
        moving_avg = []
        for i in range(99, len(stats["episode_rewards"])):
            moving_avg.append(np.mean(stats["episode_rewards"][i-99:i+1]))
        ax1.plot(range(99, len(stats["episode_rewards"])), moving_avg, 
                color='red', linewidth=2, label='100回合移动平均')
        ax1.legend()
    
    ax1.set_xlabel('回合')
    ax1.set_ylabel('回合奖励')
    ax1.set_title('训练奖励曲线')
    ax1.grid(True, alpha=0.3)
    
    # Epsilon衰减
    ax2.plot(episodes, stats["epsilon_history"], color='green')
    ax2.set_xlabel('回合')
    ax2.set_ylabel('Epsilon')
    ax2.set_title('探索率衰减')
    ax2.grid(True, alpha=0.3)
    
    # 成功率
    ax3.plot(episodes, stats["success_rate"], alpha=0.6, color='purple')
    if len(stats["success_rate"]) >= 100:
        moving_avg_success = []
        for i in range(99, len(stats["success_rate"])):
            moving_avg_success.append(np.mean(stats["success_rate"][i-99:i+1]))
        ax3.plot(range(99, len(stats["success_rate"])), moving_avg_success,
                color='orange', linewidth=2, label='100回合移动平均')
        ax3.legend()
    
    ax3.set_xlabel('回合')
    ax3.set_ylabel('成功率')
    ax3.set_title('决策成功率')
    ax3.grid(True, alpha=0.3)
    
    # 损失
    if stats["loss_history"]:
        ax4.plot(stats["loss_history"], color='red', alpha=0.7)
        ax4.set_xlabel('训练步数')
        ax4.set_ylabel('损失')
        ax4.set_title('训练损失')
        ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"训练结果图表已保存到: {save_path}")
    
    plt.show()

def main():
    """主函数 - 完整的DRL训练流程"""
    
    print("=" * 60)
    print("光网络QoT预测 - 完整DRL训练框架")
    print("=" * 60)
    
    # 1. 初始化环境
    print("\n1. 初始化环境...")
    env = OpticalNetworkEnvironment(topology_name='NSFNet')
    print(f"   网络拓扑: NSFNet ({env.topology.number_of_nodes()}节点, {env.topology.number_of_edges()}边)")
    print(f"   状态维度: {env.state_dim}")
    print(f"   动作维度: {env.action_dim}")
    
    # 2. 初始化DRL训练器
    print("\n2. 初始化DRL训练器...")
    trainer = DRLTrainer(state_dim=env.state_dim, action_dim=env.action_dim)
    print(f"   模型参数: {sum(p.numel() for p in trainer.q_network.parameters()):,}")
    print(f"   设备: {trainer.device}")
    
    # 3. 开始训练
    print("\n3. 开始DRL训练...")
    num_episodes = 200  # 先用200回合测试改进效果
    
    training_stats = trainer.train(env, num_episodes=num_episodes, save_freq=100)
    
    # 4. 保存最终模型
    print("\n4. 保存最终模型...")
    trainer.save_model("final_drl_model.pth")
    
    # 5. 绘制训练结果
    print("\n5. 生成训练结果可视化...")
    plot_save_path = Path('/home/<USER>/DYcode/fast_qot_prediction/data/training_results.png')
    plot_save_path.parent.mkdir(exist_ok=True)
    plot_training_results(training_stats, plot_save_path)
    
    # 6. 保存训练统计
    stats_path = Path('/home/<USER>/DYcode/fast_qot_prediction/data/training_stats.json')
    with open(stats_path, 'w') as f:
        # 转换numpy数组为列表以便JSON序列化
        json_stats = {}
        for key, value in training_stats.items():
            if isinstance(value, list):
                json_stats[key] = [float(v) if np.isfinite(v) else 0 for v in value]
            else:
                json_stats[key] = value
        json.dump(json_stats, f, indent=2)
    
    print(f"训练统计已保存到: {stats_path}")
    
    # 7. 性能总结
    print("\n" + "=" * 60)
    print("训练总结")
    print("=" * 60)
    
    final_avg_reward = np.mean(training_stats["episode_rewards"][-100:])
    final_success_rate = np.mean(training_stats["success_rate"][-100:])
    
    print(f"最终平均奖励 (最后100回合): {final_avg_reward:.2f}")
    print(f"最终成功率 (最后100回合): {final_success_rate:.3f}")
    print(f"最终探索率: {trainer.epsilon:.4f}")
    print(f"经验回放容量: {len(trainer.replay_buffer)}")
    
    if final_success_rate > 0.8:
        print("🎉 训练成功！智能体已学会有效的QoT决策策略")
    elif final_success_rate > 0.6:
        print("✅ 训练较好，智能体具备一定的决策能力")
    else:
        print("⚠️ 训练效果有限，可能需要调整超参数或增加训练回合")
    
    print("\n完整DRL训练框架演示完成！")
    
    return trainer, training_stats

if __name__ == "__main__":
    trainer, stats = main()