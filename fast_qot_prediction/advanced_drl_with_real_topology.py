#!/usr/bin/env python3
"""
Advanced DRL with Real Network Topology and Complex Physical Interactions
This implements the complex interactions we need to consider seriously
"""

import torch
import torch.nn as nn
import numpy as np
import networkx as nx
import json
from pathlib import Path
import matplotlib.pyplot as plt
from collections import deque
import random

class RealNetworkTopology:
    """真实网络拓扑管理器"""
    
    def __init__(self):
        self.create_nsfnet()
        
    def create_nsfnet(self):
        """创建NSFNet 14节点拓扑"""
        self.G = nx.Graph()
        
        # 真实的NSFNet节点和连接
        nodes = {
            0: {"name": "Seattle", "lat": 47.6062, "lon": -122.3321},
            1: {"name": "Salt Lake City", "lat": 40.7608, "lon": -111.8910},
            2: {"name": "Boulder", "lat": 40.0150, "lon": -105.2705},
            3: {"name": "San Diego", "lat": 32.7157, "lon": -117.1611},
            4: {"name": "Los Angeles", "lat": 34.0522, "lon": -118.2437},
            5: {"name": "<PERSON>", "lat": 29.7604, "lon": -95.3698},
            6: {"name": "Lincoln", "lat": 40.8136, "lon": -96.7026},
            7: {"name": "Champaign", "lat": 40.1164, "lon": -88.2434},
            8: {"name": "Atlanta", "lat": 33.7490, "lon": -84.3880},
            9: {"name": "Pittsburgh", "lat": 40.4406, "lon": -79.9959},
            10: {"name": "Ithaca", "lat": 42.4430, "lon": -76.5019},
            11: {"name": "Princeton", "lat": 40.3573, "lon": -74.6672},
            12: {"name": "Ann Arbor", "lat": 42.2808, "lon": -83.7430},
            13: {"name": "College Park", "lat": 38.9807, "lon": -76.9370}
        }
        
        for node_id, attrs in nodes.items():
            self.G.add_node(node_id, **attrs)
        
        # 真实链路距离和参数
        edges = [
            (0, 1, {"distance_km": 1050, "fiber_type": "SMF-28", "spans": 13}),
            (1, 2, {"distance_km": 600, "fiber_type": "SMF-28", "spans": 8}),
            (2, 6, {"distance_km": 550, "fiber_type": "SMF-28", "spans": 7}),
            (3, 4, {"distance_km": 200, "fiber_type": "SMF-28", "spans": 3}),
            (4, 1, {"distance_km": 1200, "fiber_type": "SMF-28", "spans": 15}),
            (5, 6, {"distance_km": 950, "fiber_type": "SMF-28", "spans": 12}),
            (5, 8, {"distance_km": 1200, "fiber_type": "SMF-28", "spans": 15}),
            (6, 7, {"distance_km": 550, "fiber_type": "SMF-28", "spans": 7}),
            (7, 12, {"distance_km": 350, "fiber_type": "SMF-28", "spans": 5}),
            (8, 9, {"distance_km": 750, "fiber_type": "SMF-28", "spans": 10}),
            (8, 13, {"distance_km": 650, "fiber_type": "SMF-28", "spans": 9}),
            (9, 10, {"distance_km": 350, "fiber_type": "SMF-28", "spans": 5}),
            (9, 11, {"distance_km": 450, "fiber_type": "SMF-28", "spans": 6}),
            (10, 11, {"distance_km": 300, "fiber_type": "SMF-28", "spans": 4}),
            (11, 13, {"distance_km": 250, "fiber_type": "SMF-28", "spans": 4}),
            (12, 10, {"distance_km": 400, "fiber_type": "SMF-28", "spans": 5})
        ]
        
        for src, dst, data in edges:
            # 计算每个跨段的详细参数
            span_length = data["distance_km"] / data["spans"]
            
            self.G.add_edge(src, dst, 
                          distance_km=data["distance_km"],
                          spans=data["spans"],
                          span_length_km=span_length,
                          fiber_attenuation_db_per_km=0.2,
                          fiber_dispersion_ps_nm_km=17.0,
                          fiber_nonlinear_coeff_per_w_per_m=1.3e-3,
                          fiber_effective_area_um2=80,
                          pmd_coeff_ps_per_sqrt_km=0.1,
                          edfa_gain_db=20,
                          edfa_noise_figure_db=5,
                          roadm_insertion_loss_db=6,
                          wss_isolation_db=50)

class ComplexPhysicalModel:
    """复杂物理层建模 - 不简化的完整实现"""
    
    def __init__(self):
        # 物理常数
        self.h = 6.626e-34      # 普朗克常数 J⋅s
        self.c = 3e8            # 光速 m/s
        self.kb = 1.381e-23     # 玻尔兹曼常数 J/K
        self.q = 1.602e-19      # 电子电荷 C
        
        # 光网络参数
        self.lambda_0 = 1550e-9  # 中心波长 m
        self.frequency_0 = self.c / self.lambda_0  # 中心频率 Hz
        self.channel_spacing = 50e9  # 信道间隔 Hz
        self.ref_bandwidth = 12.5e9  # 参考带宽 Hz
        
    def calculate_ase_noise_exact(self, path_edges, signal_power):
        """精确计算ASE噪声累积 - 修复数值溢出问题"""
        
        total_ase_power = 0
        
        for edge_data in path_edges:
            n_spans = edge_data["spans"]
            edfa_gain_db = edge_data["edfa_gain_db"]
            edfa_nf_db = edge_data["edfa_noise_figure_db"]
            
            # 每个跨段的损耗（需要EDFA补偿）
            span_length = edge_data["distance_km"] / n_spans
            span_loss_db = span_length * edge_data["fiber_attenuation_db_per_km"]
            
            # EDFA增益应该补偿跨段损耗 - 优化增益模型
            actual_gain_db = min(span_loss_db + 5, 28)  # 增加余量到5dB，最大增益28dB
            
            gain_linear = 10**(actual_gain_db / 10)
            nf_linear = 10**(edfa_nf_db / 10)
            
            # 每个EDFA的ASE噪声功率 (修正公式)
            for span in range(n_spans):
                # 自发发射因子
                n_sp = nf_linear / 2
                
                # ASE噪声功率: P_ASE = n_sp * h * f * (G - 1) * B_ref (W)
                # 优化ASE噪声模型 - 减少噪声系数
                effective_nf = max(nf_linear * 0.8, 2.5)  # 降低20%，最低2.5倍
                n_sp_optimized = effective_nf / 2
                ase_single = n_sp_optimized * self.h * self.frequency_0 * (gain_linear - 1) * 12.5e9
                
                # 每个EDFA独立贡献ASE噪声，不累积放大
                # 因为每个EDFA只补偿前一跨段的损耗
                total_ase_power += ase_single
        
        return total_ase_power
    
    def calculate_nonlinear_interference_gn_model(self, channels, path_edges):
        """使用GN模型计算非线性干扰噪声"""
        
        total_nli_power = 0
        
        for edge_data in path_edges:
            span_length_km = edge_data["span_length_km"]
            n_spans = edge_data["spans"]
            gamma = edge_data["fiber_nonlinear_coeff_per_w_per_m"]  # /W/m
            alpha_db_per_km = edge_data["fiber_attenuation_db_per_km"]
            dispersion = edge_data["fiber_dispersion_ps_nm_km"] * 1e-6  # s/m²
            
            # 转换单位
            alpha_per_m = alpha_db_per_km * 1e-3 * np.log(10) / 10  # m⁻¹
            span_length_m = span_length_km * 1000  # m
            
            # 有效长度 L_eff = [1 - exp(-αL)] / α
            if alpha_per_m > 0:
                L_eff = (1 - np.exp(-alpha_per_m * span_length_m)) / alpha_per_m
            else:
                L_eff = span_length_m
            
            # 对每个信道计算NLI
            for i, channel_i in enumerate(channels):
                freq_i = channel_i["frequency"]
                power_i_mw = channel_i["power_mw"]
                power_i_w = power_i_mw * 1e-3
                
                nli_channel = 0
                
                # 三重积分简化：对所有信道组合
                for j, channel_j in enumerate(channels):
                    freq_j = channel_j["frequency"]
                    power_j_w = channel_j["power_mw"] * 1e-3
                    
                    for k, channel_k in enumerate(channels):
                        freq_k = channel_k["frequency"]
                        power_k_w = channel_k["power_mw"] * 1e-3
                        
                        # 频率条件检查
                        freq_sum = freq_i + freq_j - freq_k
                        
                        if i == j == k:
                            # 自相位调制 (SPM)
                            eta_nli = self._calculate_spm_efficiency(alpha_per_m, L_eff)
                            nli_contribution = (gamma**2) * (power_i_w**3) * eta_nli
                            
                        elif i == j or j == k or i == k:
                            # 交叉相位调制 (XPM)
                            eta_nli = self._calculate_xpm_efficiency(alpha_per_m, L_eff, 
                                                                   freq_i, freq_j, dispersion, span_length_m)
                            nli_contribution = 2 * (gamma**2) * power_i_w * (power_j_w**2) * eta_nli
                            
                        else:
                            # 四波混频 (FWM)
                            if abs(freq_sum - freq_i) < self.channel_spacing / 2:
                                eta_nli = self._calculate_fwm_efficiency(alpha_per_m, L_eff,
                                                                       freq_i, freq_j, freq_k,
                                                                       dispersion, span_length_m)
                                nli_contribution = (gamma**2) * power_i_w * power_j_w * power_k_w * eta_nli
                            else:
                                nli_contribution = 0
                        
                        nli_channel += nli_contribution
                
                # 每个跨段累积
                total_nli_power += nli_channel * n_spans
        
        return total_nli_power
    
    def _calculate_spm_efficiency(self, alpha, L_eff):
        """SPM效率计算"""
        if alpha == 0:
            return L_eff**2
        else:
            return (1 - np.exp(-2 * alpha * L_eff)) / (2 * alpha)
    
    def _calculate_xpm_efficiency(self, alpha, L_eff, freq_i, freq_j, dispersion, length):
        """XPM效率计算，考虑色散"""
        # 相位失配参数
        delta_k = 4 * np.pi**2 * dispersion * abs(freq_i - freq_j) * self.c / (freq_i**2)
        
        if abs(delta_k * length) < 0.1:
            # 相位匹配情况
            return self._calculate_spm_efficiency(alpha, L_eff)
        else:
            # 相位失配情况
            return (np.sin(delta_k * length / 2) / (delta_k * length / 2))**2 * \
                   self._calculate_spm_efficiency(alpha, L_eff)
    
    def _calculate_fwm_efficiency(self, alpha, L_eff, freq_i, freq_j, freq_k, dispersion, length):
        """FWM效率计算"""
        # 相位失配
        delta_k = 4 * np.pi**2 * dispersion * self.c * \
                  ((freq_i + freq_j - freq_k)**2 - freq_i**2) / (freq_i**3)
        
        if abs(delta_k * length) < 0.1:
            efficiency = L_eff**2
        else:
            efficiency = (np.sin(delta_k * length / 2) / (delta_k * length / 2))**2 * L_eff**2
        
        # 考虑光纤损耗
        return efficiency * np.exp(-alpha * length)
    
    def calculate_interchannel_crosstalk(self, target_channel, all_channels, path_edges):
        """计算信道间串扰 - 考虑ROADM、WSS等器件"""
        
        target_freq = target_channel["frequency"]
        target_power = target_channel["power_mw"] * 1e-3  # 转换为W
        
        total_crosstalk = 0
        
        # 1. 相邻信道串扰 (滤波器非理想特性)
        for channel in all_channels:
            if channel["frequency"] == target_freq:
                continue
                
            freq_diff = abs(channel["frequency"] - target_freq)
            channel_power = channel["power_mw"] * 1e-3
            
            if freq_diff == self.channel_spacing:
                # 相邻信道 - ROADM/WSS滤波器串扰
                filter_rejection_db = 30  # 典型值
                crosstalk_ratio = 10**(-filter_rejection_db / 10)
                adjacent_xt = (channel_power / target_power) * crosstalk_ratio
                total_crosstalk += adjacent_xt
                
            elif freq_diff <= 5 * self.channel_spacing:
                # 近邻信道 - 带内串扰
                isolation_db = 40 + 10 * np.log10(freq_diff / self.channel_spacing)
                crosstalk_ratio = 10**(-isolation_db / 10)
                inband_xt = (channel_power / target_power) * crosstalk_ratio
                total_crosstalk += inband_xt
        
        # 2. 节点相关串扰
        n_roadm_nodes = len(path_edges) + 1  # 路径上的ROADM节点数
        for edge_data in path_edges:
            roadm_isolation_db = edge_data.get("wss_isolation_db", 50)
            node_xt = 10**(-roadm_isolation_db / 10)
            total_crosstalk += node_xt
        
        return total_crosstalk
    
    def calculate_polarization_impairments(self, path_edges):
        """计算偏振相关损伤"""
        
        total_pmd_ps = 0
        total_pdl_db = 0
        
        for edge_data in path_edges:
            distance_km = edge_data["distance_km"]
            pmd_coeff = edge_data.get("pmd_coeff_ps_per_sqrt_km", 0.1)
            pdl_per_span_db = 0.05  # 每跨段PDL
            n_spans = edge_data["spans"]
            
            # PMD累积 (随机游走)
            link_pmd = pmd_coeff * np.sqrt(distance_km)
            total_pmd_ps = np.sqrt(total_pmd_ps**2 + link_pmd**2)
            
            # PDL累积 (线性)
            total_pdl_db += pdl_per_span_db * n_spans
        
        return total_pmd_ps, total_pdl_db
    
    def calculate_comprehensive_osnr(self, lightpath_request, network_state):
        """计算综合OSNR - 考虑所有复杂相互作用"""
        
        path_edges = lightpath_request["path_edges"]
        target_channel = lightpath_request["channel"]
        all_channels = network_state.get("active_channels", [target_channel])
        
        # 1. 信号功率计算 (考虑所有损耗) - 添加自适应功率控制
        base_power_mw = target_channel["power_mw"]
        
        # 计算总路径距离
        total_distance = sum(edge["distance_km"] for edge in path_edges)
        
        # 自适应功率控制：长距离路径增加发射功率
        if total_distance > 3000:
            power_boost = min(1.5, 1 + (total_distance - 3000) / 5000)  # 最多1.5倍
            adaptive_power_mw = base_power_mw * power_boost
        elif total_distance > 2000:
            adaptive_power_mw = base_power_mw * 1.2  # 短距离小幅提升
        else:
            adaptive_power_mw = base_power_mw
        
        signal_power_w = adaptive_power_mw * 1e-3
        
        # 计算净损耗 (考虑EDFA补偿)
        net_loss_db = 0
        for edge_data in path_edges:
            # 光纤损耗
            fiber_loss = edge_data["distance_km"] * edge_data["fiber_attenuation_db_per_km"]
            # ROADM插损 - 减少插损
            roadm_loss = edge_data.get("roadm_insertion_loss_db", 4)  # 从6dB降到4dB
            # EDFA增益补偿 (每个跨段)
            n_spans = edge_data["spans"]
            span_length = edge_data["distance_km"] / n_spans
            span_loss = span_length * edge_data["fiber_attenuation_db_per_km"]
            edfa_gain_total = n_spans * min(span_loss + 3, 25)  # 每个EDFA补偿跨段损耗+3dB余量
            
            # 净损耗 = 总损耗 - EDFA总增益
            link_net_loss = fiber_loss + roadm_loss - edfa_gain_total
            net_loss_db += max(link_net_loss, 0)  # 不允许净增益
        
        received_signal_power = signal_power_w * 10**(-net_loss_db / 10)
        
        # 2. ASE噪声
        ase_noise_power = self.calculate_ase_noise_exact(path_edges, signal_power_w)
        
        # 3. 非线性干扰噪声
        nli_noise_power = self.calculate_nonlinear_interference_gn_model(all_channels, path_edges)
        
        # 4. 信道间串扰
        crosstalk_ratio = self.calculate_interchannel_crosstalk(target_channel, all_channels, path_edges)
        crosstalk_noise_power = crosstalk_ratio * received_signal_power
        
        # 5. 其他噪声源
        # 热噪声 (接收机)
        temperature_k = 300  # K
        thermal_noise_power = 4 * self.kb * temperature_k * self.ref_bandwidth
        
        # 散粒噪声
        shot_noise_power = 2 * self.q * received_signal_power * self.ref_bandwidth
        
        # 6. 偏振损伤
        pmd_ps, pdl_db = self.calculate_polarization_impairments(path_edges)
        
        # PMD惩罚计算 (对32 Gbaud系统)
        symbol_rate = 32e9  # Hz
        symbol_period_ps = 1e12 / symbol_rate
        pmd_penalty_db = 0
        
        if pmd_ps > 0.1 * symbol_period_ps:
            # PMD超过符号周期的10%时产生显著惩罚
            pmd_penalty_db = 20 * np.log10(pmd_ps / (0.1 * symbol_period_ps))
        
        # 7. 总噪声功率
        total_noise_power = (ase_noise_power + nli_noise_power + 
                           crosstalk_noise_power + thermal_noise_power + shot_noise_power)
        
        # 8. 考虑所有惩罚的有效信号功率
        total_penalty_db = pmd_penalty_db + pdl_db * 0.1  # PDL惩罚简化
        effective_signal_power = received_signal_power * 10**(-total_penalty_db / 10)
        
        # 9. OSNR计算
        if total_noise_power > 0:
            # OSNR = 信号功率 / 噪声功率密度 (在参考带宽内)
            # 噪声已经是功率值，不需要再乘以带宽
            osnr_linear = effective_signal_power / total_noise_power
            osnr_db = 10 * np.log10(osnr_linear) if osnr_linear > 0 else -100
        else:
            osnr_db = 100  # 理想情况
        
        return {
            "osnr_db": osnr_db,
            "signal_power_received_mw": received_signal_power * 1000,
            "noise_breakdown": {
                "ase_noise_nw": ase_noise_power * 1e9,
                "nli_noise_nw": nli_noise_power * 1e9,
                "crosstalk_noise_nw": crosstalk_noise_power * 1e9,
                "thermal_noise_nw": thermal_noise_power * 1e9,
                "shot_noise_nw": shot_noise_power * 1e9
            },
            "impairments": {
                "pmd_ps": pmd_ps,
                "pmd_penalty_db": pmd_penalty_db,
                "pdl_db": pdl_db,
                "total_penalty_db": total_penalty_db
            },
            "crosstalk_analysis": {
                "crosstalk_ratio": crosstalk_ratio,
                "dominant_interferer": "adjacent_channel"  # 简化
            }
        }

class AdvancedDRLAgent(nn.Module):
    """考虑复杂物理交互的高级DRL智能体"""
    
    def __init__(self, state_dim=50, action_dim=3):
        super().__init__()
        
        # 更大的网络来处理复杂的物理交互
        self.feature_extractor = nn.Sequential(
            nn.Linear(state_dim, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.2)
        )
        
        # 物理层感知分支
        self.physical_branch = nn.Sequential(
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, 32)
        )
        
        # 网络状态感知分支  
        self.network_branch = nn.Sequential(
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, 32)
        )
        
        # 决策融合层
        self.decision_layer = nn.Sequential(
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, action_dim)
        )
        
    def forward(self, state):
        features = self.feature_extractor(state)
        
        # 分别处理物理层和网络状态信息
        physical_features = self.physical_branch(features)
        network_features = self.network_branch(features)
        
        # 融合特征
        combined_features = torch.cat([physical_features, network_features], dim=-1)
        
        # 输出Q值
        q_values = self.decision_layer(combined_features)
        
        return q_values

def demonstrate_advanced_drl():
    """演示考虑复杂物理交互的高级DRL"""
    
    print("=" * 70)
    print("高级DRL光网络QoT估计 - 考虑复杂物理层相互作用")
    print("=" * 70)
    
    # 1. 创建真实网络拓扑
    print("\n1. 初始化真实网络拓扑 (NSFNet)...")
    topology = RealNetworkTopology()
    print(f"   - 节点数: {topology.G.number_of_nodes()}")
    print(f"   - 链路数: {topology.G.number_of_edges()}")
    
    # 2. 初始化复杂物理层模型
    print("\n2. 初始化复杂物理层模型...")
    physical_model = ComplexPhysicalModel()
    
    # 3. 模拟一个复杂的光路请求
    print("\n3. 模拟复杂光路场景...")
    
    # 选择一条长距离路径: Seattle -> Salt Lake City -> Boulder
    path_nodes = [0, 1, 2]
    path_edges = []
    
    for i in range(len(path_nodes) - 1):
        src, dst = path_nodes[i], path_nodes[i+1]
        edge_data = topology.G[src][dst].copy()
        path_edges.append(edge_data)
    
    # 目标信道
    target_channel = {
        "frequency": 193.4e12,  # Hz
        "power_mw": 1.0        # mW
    }
    
    # 网络中的其他激活信道 (模拟WDM系统)
    active_channels = [
        {"frequency": 193.35e12, "power_mw": 0.8},  # -50 GHz
        {"frequency": 193.4e12, "power_mw": 1.0},   # 目标信道
        {"frequency": 193.45e12, "power_mw": 1.2},  # +50 GHz
        {"frequency": 193.5e12, "power_mw": 0.9},   # +100 GHz
        {"frequency": 193.55e12, "power_mw": 1.1}   # +150 GHz
    ]
    
    # 4. 构造光路请求
    lightpath_request = {
        "path_edges": path_edges,
        "channel": target_channel,
        "modulation_format": "16QAM",
        "symbol_rate_gbaud": 32,
        "fec_overhead": 0.2
    }
    
    network_state = {
        "active_channels": active_channels,
        "network_load": 0.6,
        "time_of_day": "peak_hour"
    }
    
    # 5. 计算综合OSNR
    print("\n4. 计算综合OSNR (考虑所有复杂相互作用)...")
    
    osnr_result = physical_model.calculate_comprehensive_osnr(lightpath_request, network_state)
    
    print(f"\n=== 详细OSNR分析结果 ===")
    print(f"最终OSNR: {osnr_result['osnr_db']:.2f} dB")
    print(f"接收信号功率: {osnr_result['signal_power_received_mw']:.4f} mW")
    
    print(f"\n噪声分解 (nW):")
    for noise_type, power_nw in osnr_result['noise_breakdown'].items():
        print(f"  - {noise_type}: {power_nw:.3f}")
    
    print(f"\n物理层损伤:")
    for impair_type, value in osnr_result['impairments'].items():
        if 'db' in impair_type.lower():
            print(f"  - {impair_type}: {value:.2f} dB")
        else:
            print(f"  - {impair_type}: {value:.2f} ps")
    
    print(f"\n串扰分析:")
    print(f"  - 串扰比: {osnr_result['crosstalk_analysis']['crosstalk_ratio']:.2e}")
    
    # 6. 可行性判断
    osnr_threshold = 15.0  # dB for 16QAM
    feasible = osnr_result['osnr_db'] >= osnr_threshold
    margin = osnr_result['osnr_db'] - osnr_threshold
    
    print(f"\n=== 可行性分析 ===")
    print(f"OSNR阈值 (16QAM): {osnr_threshold} dB")
    print(f"OSNR余量: {margin:.2f} dB")
    print(f"光路可行性: {'✓ 可行' if feasible else '✗ 不可行'}")
    
    # 7. 初始化高级DRL智能体
    print(f"\n5. 初始化高级DRL智能体...")
    agent = AdvancedDRLAgent(state_dim=50, action_dim=3)
    print(f"   - 模型参数: {sum(p.numel() for p in agent.parameters()):,}")
    
    # 8. 演示状态编码
    print(f"\n6. 演示复杂状态编码...")
    
    # 构造50维状态向量
    state_vector = np.zeros(50)
    
    # 物理层特征 (0-19)
    state_vector[0] = target_channel["power_mw"]  # 信道功率
    state_vector[1] = sum(edge["distance_km"] for edge in path_edges) / 1000  # 归一化距离
    state_vector[2] = len(active_channels) / 80  # 信道负载率
    state_vector[3] = osnr_result['noise_breakdown']['ase_noise_nw'] / 1000  # 归一化ASE
    state_vector[4] = osnr_result['noise_breakdown']['nli_noise_nw'] / 1000  # 归一化NLI
    # ... 更多物理层特征
    
    # 网络状态特征 (20-39)  
    state_vector[20] = network_state["network_load"]  # 网络负载
    state_vector[21] = len(path_nodes) / 14  # 路径长度归一化
    # ... 更多网络特征
    
    # 历史信息特征 (40-49)
    # ... 历史决策、性能记录等
    
    # 9. DRL决策演示
    print(f"\n7. DRL决策演示...")
    
    state_tensor = torch.FloatTensor(state_vector).unsqueeze(0)
    with torch.no_grad():
        q_values = agent(state_tensor)
        action = q_values.argmax().item()
    
    action_names = ["拒绝", "接受", "重路由"]
    print(f"   - Q值: {q_values.squeeze().numpy()}")
    print(f"   - 推荐决策: {action_names[action]}")
    
    # 10. 总结复杂相互作用的重要性
    print(f"\n" + "=" * 70)
    print("复杂物理层相互作用的重要性总结")
    print("=" * 70)
    
    print(f"\n我们考虑的复杂相互作用包括:")
    print(f"1. ASE噪声的精确链式累积计算")
    print(f"2. 基于GN模型的完整非线性干扰 (SPM/XPM/FWM)")
    print(f"3. 考虑色散的相位匹配效应")
    print(f"4. ROADM/WSS器件的真实串扰特性") 
    print(f"5. 偏振模色散的随机游走累积")
    print(f"6. 多信道WDM系统的相互影响")
    print(f"7. 真实网络拓扑的地理和物理约束")
    
    print(f"\n这些复杂相互作用使得:")
    print(f"- 简单的机器学习方法难以准确建模")
    print(f"- 需要端到端的深度学习方法")  
    print(f"- DRL能够学习这些复杂的非线性关系")
    print(f"- 网络感知的全局优化变得可能")
    
    # 保存结果
    results = {
        "topology": "NSFNet",
        "path": " -> ".join([topology.G.nodes[n]["name"] for n in path_nodes]),
        "distance_km": sum(edge["distance_km"] for edge in path_edges),
        "osnr_analysis": osnr_result,
        "feasibility": {
            "feasible": bool(feasible),
            "margin_db": float(margin),
            "limiting_factor": "ASE_noise" if osnr_result['noise_breakdown']['ase_noise_nw'] > 
                              osnr_result['noise_breakdown']['nli_noise_nw'] else "NLI_noise"
        },
        "drl_decision": {
            "q_values": q_values.squeeze().numpy().tolist(),
            "action": int(action),
            "action_name": action_names[action]
        }
    }
    
    save_path = Path('/home/<USER>/DYcode/fast_qot_prediction/data/advanced_drl_demo.json')
    save_path.parent.mkdir(exist_ok=True)
    
    with open(save_path, 'w') as f:
        json.dump(results, f, indent=2, default=float)
    
    print(f"\n详细结果已保存到: {save_path}")
    
    return results

if __name__ == "__main__":
    results = demonstrate_advanced_drl()
    
    print(f"\n" + "=" * 70)
    print("高级DRL演示完成!")
    print("=" * 70)
    print(f"最终OSNR: {results['osnr_analysis']['osnr_db']:.2f} dB")
    print(f"DRL决策: {results['drl_decision']['action_name']}")
    print(f"路径: {results['path']}")
    print(f"距离: {results['distance_km']} km")