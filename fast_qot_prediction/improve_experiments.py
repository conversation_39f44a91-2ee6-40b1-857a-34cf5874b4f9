#!/usr/bin/env python3
"""
改进实验设计和参数优化
解决现有实验结果不理想的问题
"""

import numpy as np
import torch
import torch.nn as nn
import json
from pathlib import Path
import matplotlib.pyplot as plt
import sys
sys.path.append('.')

from advanced_drl_with_real_topology import RealNetworkTopology, ComplexPhysicalModel, AdvancedDRLAgent

class ExperimentAnalyzer:
    """实验结果分析器"""
    
    def __init__(self):
        self.topology_manager = RealNetworkTopology()
        self.physical_model = ComplexPhysicalModel()
        self.topology = self.topology_manager.G
    
    def analyze_current_problems(self):
        """分析当前实验存在的问题"""
        
        print("=" * 60)
        print("当前实验问题诊断")
        print("=" * 60)
        
        problems = []
        solutions = []
        
        # 1. 分析DRL训练结果
        try:
            with open('data/training_stats.json', 'r') as f:
                stats = json.load(f)
            
            rewards = stats['episode_rewards']
            success_rates = stats['success_rate']
            
            avg_success = sum(success_rates) / len(success_rates)
            max_success = max(success_rates)
            reward_variance = np.var(rewards)
            
            print(f"\n1. DRL训练问题分析:")
            print(f"   平均成功率: {avg_success:.3f} (目标: >0.8)")
            print(f"   最高成功率: {max_success:.3f}")
            print(f"   奖励方差: {reward_variance:.2f} (过高表示不稳定)")
            
            if avg_success < 0.6:
                problems.append("DRL成功率过低")
                solutions.append("增加训练回合数到1000+，调整奖励函数")
            
            if reward_variance > 150:
                problems.append("训练不稳定，收敛性差")
                solutions.append("降低学习率，增加经验回放容量")
                
        except FileNotFoundError:
            problems.append("缺少训练统计文件")
            solutions.append("重新运行完整训练")
        
        # 2. 分析物理层建模
        print(f"\n2. 物理层建模问题分析:")
        
        # 测试几个典型场景的OSNR
        test_scenarios = [
            {"distance": 800, "channels": 1, "power": 1.0},
            {"distance": 1600, "channels": 5, "power": 1.0},
            {"distance": 2400, "channels": 10, "power": 1.0}
        ]
        
        osnr_results = []
        for scenario in test_scenarios:
            osnr = self._test_osnr_scenario(scenario)
            osnr_results.append(osnr)
            print(f"   场景 {scenario}: OSNR = {osnr:.2f} dB")
        
        if min(osnr_results) < 10:
            problems.append("物理层建模过于悲观，OSNR值偏低")
            solutions.append("调整噪声模型参数，优化EDFA增益补偿")
        
        # 3. 分析多路径优化
        print(f"\n3. 多路径优化问题分析:")
        
        try:
            with open('data/multi_path_optimization_results.json', 'r') as f:
                opt_results = json.load(f)
            
            if opt_results['single_optimization']['status'] == 'no_path':
                problems.append("路径寻找算法有问题")
                solutions.append("检查网络连通性，改进路径搜索算法")
            
            test_cases = opt_results['strategy_comparison']['test_cases']
            if len(test_cases) < 3:
                problems.append("有效测试案例太少")
                solutions.append("增加更多节点对，改进路径可达性检查")
                
        except FileNotFoundError:
            problems.append("缺少多路径优化结果文件")
            solutions.append("重新运行多路径优化")
        
        # 4. 总结问题和解决方案
        print(f"\n" + "=" * 60)
        print("问题总结和改进建议")
        print("=" * 60)
        
        print(f"\n发现的主要问题:")
        for i, problem in enumerate(problems, 1):
            print(f"{i}. {problem}")
        
        print(f"\n建议的解决方案:")
        for i, solution in enumerate(solutions, 1):
            print(f"{i}. {solution}")
        
        return problems, solutions
    
    def _test_osnr_scenario(self, scenario):
        """测试OSNR场景"""
        try:
            # 构造测试路径
            test_path_edges = [{
                "spans": scenario["distance"] // 80,
                "distance_km": scenario["distance"],
                "span_length_km": 80,
                "edfa_gain_db": 20,
                "edfa_noise_figure_db": 5,
                "fiber_attenuation_db_per_km": 0.2,
                "roadm_insertion_loss_db": 6,
                "fiber_nonlinear_coeff_per_w_per_m": 1.3e-3,
                "fiber_dispersion_ps_nm_km": 17.0,
                "fiber_effective_area_um2": 80,
                "pmd_coeff_ps_per_sqrt_km": 0.1,
                "wss_isolation_db": 50
            }]
            
            # 构造信道配置
            channels = []
            for i in range(scenario["channels"]):
                channels.append({
                    "frequency": 193.4e12 + i * 50e9,
                    "power_mw": scenario["power"]
                })
            
            lightpath_request = {
                "path_edges": test_path_edges,
                "channel": channels[0]
            }
            
            network_state = {
                "active_channels": channels,
                "network_load": scenario["channels"] / 80
            }
            
            result = self.physical_model.calculate_comprehensive_osnr(
                lightpath_request, network_state)
            
            return result["osnr_db"]
            
        except Exception as e:
            print(f"OSNR测试错误: {e}")
            return -100
    
    def propose_improvements(self):
        """提出具体的改进措施"""
        
        print("\n" + "=" * 60)
        print("实验改进方案")
        print("=" * 60)
        
        improvements = {
            "drl_training": {
                "title": "DRL训练改进",
                "changes": [
                    "增加训练回合数: 100 -> 1000",
                    "调整学习率: 1e-3 -> 5e-4",
                    "增大经验回放: 10000 -> 50000",
                    "改进奖励函数: 添加OSNR余量奖励",
                    "调整epsilon衰减: 更缓慢的探索衰减"
                ]
            },
            "physical_modeling": {
                "title": "物理层建模改进", 
                "changes": [
                    "优化EDFA增益模型: 更准确的增益-损耗平衡",
                    "调整噪声系数: 从5dB降到4dB",
                    "改进非线性模型: 使用更精确的GN模型参数",
                    "添加自适应功率控制: 根据距离调整发射功率",
                    "优化PMD建模: 使用更现实的PMD系数"
                ]
            },
            "path_optimization": {
                "title": "路径优化改进",
                "changes": [
                    "改进路径搜索: 使用Yen算法寻找K最短路径",
                    "增强连通性检查: 预检查节点对可达性",
                    "优化QoT评分函数: 更合理的权重分配", 
                    "添加动态阈值: 根据网络状态调整OSNR阈值",
                    "支持更多拓扑: 扩展到其他真实网络"
                ]
            },
            "evaluation": {
                "title": "评估方法改进",
                "changes": [
                    "增加基准比较: 与传统方法对比",
                    "添加统计显著性测试: 验证改进的有效性",
                    "扩大测试规模: 更多节点对和场景",
                    "添加鲁棒性测试: 不同网络负载和故障场景",
                    "实时性能评估: 测试实际部署的可行性"
                ]
            }
        }
        
        for category, info in improvements.items():
            print(f"\n{info['title']}:")
            for i, change in enumerate(info['changes'], 1):
                print(f"  {i}. {change}")
        
        return improvements
    
    def create_improvement_roadmap(self):
        """创建改进路线图"""
        
        roadmap = {
            "Phase1_基础改进": {
                "时间": "1-2天",
                "目标": "解决最关键的问题",
                "任务": [
                    "修复路径搜索算法",
                    "调整EDFA增益模型", 
                    "增加训练回合数到500",
                    "改进奖励函数设计"
                ],
                "预期": "DRL成功率提升到60%+"
            },
            "Phase2_系统优化": {
                "时间": "3-5天", 
                "目标": "全面提升系统性能",
                "任务": [
                    "重新设计物理层参数",
                    "实现1000回合完整训练",
                    "添加自适应功率控制",
                    "优化多路径算法"
                ],
                "预期": "DRL成功率达到75%+，OSNR余量提升"
            },
            "Phase3_高级功能": {
                "时间": "1周",
                "目标": "达到工程化水平",
                "任务": [
                    "添加多种网络拓扑支持",
                    "实现在线学习功能", 
                    "增加鲁棒性测试",
                    "完善评估体系"
                ],
                "预期": "系统达到实用化水平"
            }
        }
        
        print(f"\n" + "=" * 60)
        print("改进路线图")
        print("=" * 60)
        
        for phase, details in roadmap.items():
            print(f"\n{phase}:")
            print(f"  时间: {details['时间']}")
            print(f"  目标: {details['目标']}")
            print(f"  主要任务:")
            for task in details['任务']:
                print(f"    - {task}")
            print(f"  预期结果: {details['预期']}")
        
        return roadmap

def main():
    """主函数"""
    
    analyzer = ExperimentAnalyzer()
    
    # 1. 分析当前问题
    problems, solutions = analyzer.analyze_current_problems()
    
    # 2. 提出改进方案
    improvements = analyzer.propose_improvements()
    
    # 3. 创建路线图
    roadmap = analyzer.create_improvement_roadmap()
    
    # 4. 保存分析结果
    analysis_results = {
        "problems": problems,
        "solutions": solutions,
        "improvements": improvements,
        "roadmap": roadmap,
        "current_performance": {
            "drl_success_rate": 0.362,
            "avg_osnr": 19.78,
            "training_episodes": 100,
            "model_parameters": 68803
        }
    }
    
    save_path = Path('/home/<USER>/DYcode/fast_qot_prediction/data/experiment_analysis.json')
    with open(save_path, 'w', encoding='utf-8') as f:
        json.dump(analysis_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n分析结果已保存到: {save_path}")
    
    # 5. 总结
    print(f"\n" + "=" * 60)
    print("实验改进总结")
    print("=" * 60)
    
    print(f"\n当前主要问题:")
    print(f"❌ DRL成功率仅36.2% (目标: >80%)")
    print(f"❌ 训练回合数太少 (100回合)")
    print(f"❌ 路径优化算法有缺陷")
    print(f"❌ 物理层建模可能过于保守")
    
    print(f"\n优先改进项目:")
    print(f"🔧 修复路径搜索算法")
    print(f"🔧 增加训练回合数到1000+")
    print(f"🔧 优化EDFA增益和噪声模型")
    print(f"🔧 改进DRL奖励函数设计")
    
    print(f"\n预期改进效果:")
    print(f"🎯 DRL成功率提升到75%+")
    print(f"🎯 OSNR计算更加准确")
    print(f"🎯 多路径优化更加有效")
    print(f"🎯 整体系统更加实用")

if __name__ == "__main__":
    main()