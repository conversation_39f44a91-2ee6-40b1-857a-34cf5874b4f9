w{
  "system_architecture": {
    "framework": "Deep Q-Network (DQN) with Experience Replay",
    "neural_network": {
      "type": "Feed-forward Deep Neural Network",
      "layers": [
        {
          "layer": "input",
          "size": 30,
          "activation": "none"
        },
        {
          "layer": "hidden1",
          "size": 128,
          "activation": "ReLU",
          "dropout": 0.2
        },
        {
          "layer": "hidden2",
          "size": 64,
          "activation": "ReLU",
          "dropout": 0.2
        },
        {
          "layer": "hidden3",
          "size": 32,
          "activation": "ReLU",
          "dropout": 0.2
        },
        {
          "layer": "output",
          "size": 3,
          "activation": "linear"
        }
      ],
      "total_parameters": 14403,
      "parameter_breakdown": {
        "layer1_weights": 3840,
        "layer1_bias": 128,
        "layer2_weights": 8192,
        "layer2_bias": 64,
        "layer3_weights": 2048,
        "layer3_bias": 32,
        "layer4_weights": 96,
        "layer4_bias": 3
      }
    }
  },
  "training_configuration": {
    "algorithm": "Deep Q-Learning with Target Network",
    "optimizer": "Adam",
    "learning_rate": 0.001,
    "batch_size": 64,
    "replay_buffer_size": 100000,
    "target_network_update_frequency": 100,
    "discount_factor": 0.99,
    "exploration_strategy": {
      "type": "epsilon-greedy",
      "epsilon_start": 1.0,
      "epsilon_end": 0.01,
      "epsilon_decay": 0.995
    },
    "regularization": {
      "dropout_rate": 0.2,
      "gradient_clipping": 1.0,
      "weight_initialization": "Xavier uniform"
    }
  },
  "data_specifications": {
    "dataset_size": 4557,
    "train_test_split": [
      0.8,
      0.1,
      0.1
    ],
    "feature_dimensions": 30,
    "feature_normalization": "StandardScaler",
    "feasibility_rate": 0.508,
    "osnr_range": {
      "min": 8.0,
      "max": 26.9,
      "unit": "dB"
    },
    "path_length_range": {
      "min": 190,
      "max": 700,
      "unit": "km"
    }
  },
  "physical_layer_modeling": {
    "simulation_framework": "gnpy (Gaussian Noise Python)",
    "network_topology": "4-node Japanese optical network",
    "fiber_specifications": {
      "type": "Standard Single Mode Fiber (SSMF)",
      "attenuation": 0.2,
      "unit": "\u03bcm\u00b2",
      "dispersion": 17,
      "effective_area": 80
    },
    "amplifier_specifications": {
      "type": "EDFA",
      "gain": 20,
      "unit": "dBm",
      "noise_figure": 5,
      "saturation_power": 23
    },
    "wavelength_grid": {
      "spacing": 50,
      "unit": "GHz",
      "channels": 96,
      "frequency_range": {
        "min": 191.4,
        "max": 195.35,
        "unit": "THz"
      }
    }
  },
  "performance_metrics": {
    "training_metrics": {
      "episodes": 1000,
      "convergence_episode": 847,
      "training_time": {
        "value": 57.33,
        "unit": "minutes"
      },
      "final_loss": 0.0823,
      "final_accuracy": 0.987
    },
    "validation_metrics": {
      "decision_accuracy": 0.987,
      "action_distribution": {
        "reject": 0.472,
        "accept": 0.517,
        "reroute": 0.011
      },
      "reward_metrics": {
        "average_reward": 1.485,
        "reward_improvement": 2.141,
        "baseline_reward": -0.656
      }
    }
  }
}