# 深度强化学习光网络QoT保障系统

## 🌟 系统概述

这是一个基于深度强化学习的光网络QoT（传输质量）保障系统，能够实时优化光路分配以保障全网QoT安全域。

### 核心特性
- **DRL驱动**: 使用深度Q网络进行智能决策
- **物理感知**: 集成真实的光网络物理层模型
- **实时优化**: 支持在线光路分配和QoT保障
- **多目标优化**: 同时考虑QoT、频谱效率和功率效率

## 🚀 快速开始

### 最简部署（推荐）

```bash
# 1. 克隆或下载代码
git clone <your-repo> 
cd DYcode

# 2. 安装依赖
pip install torch gymnasium numpy matplotlib pandas scipy scikit-learn

# 3. 运行演示版本
python drl_qot_optimization_system_demo.py
```

### 完整部署

```bash
# 1. 创建conda环境
conda create -n drl-qot python=3.11
conda activate drl-qot

# 2. 安装PyTorch（GPU版本）
conda install pytorch torchvision torchaudio pytorch-cuda=12.1 -c pytorch -c nvidia

# 3. 安装完整依赖
pip install -r requirements.txt

# 4. 运行系统测试
python test_deployment.py

# 5. 运行完整系统
python drl_qot_optimization_system.py
```

## 📋 系统要求

### 硬件要求
- **CPU**: x64处理器
- **内存**: 8GB+ RAM
- **GPU**: 可选，NVIDIA GPU（推荐）

### 软件要求
- **Python**: 3.10-3.11
- **操作系统**: Linux/Windows/macOS

### 核心依赖
```
torch>=2.0.0              # 深度学习框架
gymnasium>=1.0.0          # 强化学习环境
numpy>=1.24.0             # 数值计算
matplotlib>=3.7.0         # 可视化
gnpy>=2.12.0              # 光网络仿真（可选）
```

## 🎯 运行模式

### 演示模式（推荐新用户）
```bash
python drl_qot_optimization_system_demo.py
```
**优点**: 无需复杂配置，快速体验算法效果

### 完整仿真模式
```bash
python drl_qot_optimization_system.py
```
**优点**: 基于真实gnpy光网络仿真，结果更准确

## 📊 系统测试

运行部署测试验证系统状态：
```bash
python test_deployment.py
```

测试内容包括：
- ✅ Python版本检查
- ✅ 核心依赖验证
- ✅ PyTorch功能测试
- ✅ 系统功能验证

## 🔧 常见问题

### Q: 如何在CPU上运行？
A: 系统自动检测GPU，如无GPU会使用CPU模式。

### Q: 内存不足怎么办？
A: 在代码中调整参数：
```python
# 减少批次大小
self.batch_size = 16

# 减少网络节点数
trainer = DRLQoTTrainer(num_nodes=5)
```

### Q: 缺少gnpy怎么办？
A: 使用演示模式：
```bash
python drl_qot_optimization_system_demo.py
```

### Q: 依赖安装失败？
A: 使用conda重建环境：
```bash
conda env create -f gnpy_environment.yml
conda activate gnpy
```

## 📈 性能优化

### CPU优化
```bash
export OMP_NUM_THREADS=4
export MKL_NUM_THREADS=4
```

### 训练加速
```python
# 快速测试
trainer.train(episodes=50)

# 小规模网络
trainer = DRLQoTTrainer(num_nodes=5)
```

## 📁 文件说明

### 核心文件
- `drl_qot_optimization_system_demo.py` - 演示版本（推荐）
- `drl_qot_optimization_system.py` - 完整版本
- `test_deployment.py` - 部署测试脚本
- `requirements.txt` - 依赖列表

### 配置文件
- `gnpy_environment.yml` - Conda环境配置
- `deployment_guide.md` - 详细部署指南

### 数据文件（可选）
- `Data/` - 网络拓扑和设备配置
- `*.pth` - 预训练模型

## 🎉 快速验证

运行以下命令验证系统：
```python
import torch
from drl_qot_optimization_system_demo import DRLQoTTrainer

print(f"✅ PyTorch: {torch.__version__}")
print(f"✅ CUDA: {torch.cuda.is_available()}")

trainer = DRLQoTTrainer(num_nodes=3)
print("✅ 系统部署成功！")
```

## 📞 技术支持

遇到问题时：
1. 首先运行 `python test_deployment.py` 诊断
2. 检查Python版本（推荐3.10-3.11）
3. 优先使用演示模式测试
4. 查看错误日志确定具体问题

---

🚀 **立即开始**: `python drl_qot_optimization_system_demo.py`

⭐ **完整功能**: `python drl_qot_optimization_system.py`

🔍 **系统诊断**: `python test_deployment.py` 