"""
大规模真实光网络数据实验
使用23000+样本重新验证子图GAT方法
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import dgl
import numpy as np
import json
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.preprocessing import StandardScaler
from sklearn.linear_model import LinearRegression, Ridge, Lasso
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.preprocessing import PolynomialFeatures
from sklearn.svm import SVR
from sklearn.neural_network import MLPRegressor
from sklearn.model_selection import train_test_split
import pandas as pd
import time
import warnings
warnings.filterwarnings('ignore')

class ScalableSubgraphGAT(nn.Module):
    """可扩展的子图GAT模型 - 针对大数据集优化"""

    def __init__(self, input_dim, hidden_dim=128, num_heads=8, num_layers=2, dropout=0.1, use_residual=True):
        super().__init__()

        self.num_layers = num_layers
        self.dropout = dropout
        self.use_residual = use_residual
        self.hidden_dim = hidden_dim

        # 多层GAT
        self.gat_layers = nn.ModuleList()
        self.norm_layers = nn.ModuleList()
        self.residual_layers = nn.ModuleList()

        # 输入层
        self.gat_layers.append(
            dgl.nn.GATConv(input_dim, hidden_dim, num_heads,
                          feat_drop=dropout, attn_drop=dropout,
                          activation=F.leaky_relu, allow_zero_in_degree=True)
        )
        self.norm_layers.append(nn.LayerNorm(hidden_dim * num_heads))

        # 输入维度转换（用于残差连接）
        if use_residual and input_dim != hidden_dim * num_heads:
            self.residual_layers.append(nn.Linear(input_dim, hidden_dim * num_heads))
        else:
            self.residual_layers.append(None)

        # 隐藏层
        for i in range(num_layers - 2):
            self.gat_layers.append(
                dgl.nn.GATConv(hidden_dim * num_heads, hidden_dim, num_heads,
                              feat_drop=dropout, attn_drop=dropout,
                              activation=F.leaky_relu, allow_zero_in_degree=True)
            )
            self.norm_layers.append(nn.LayerNorm(hidden_dim * num_heads))
            self.residual_layers.append(None)  # 同维度，不需要转换

        # 输出层
        if num_layers > 1:
            self.gat_layers.append(
                dgl.nn.GATConv(hidden_dim * num_heads, hidden_dim, 1,
                              feat_drop=dropout, attn_drop=dropout, allow_zero_in_degree=True)
            )
            self.norm_layers.append(nn.LayerNorm(hidden_dim))

            # 输出层残差连接
            if use_residual:
                self.residual_layers.append(nn.Linear(hidden_dim * num_heads, hidden_dim))
            else:
                self.residual_layers.append(None)

        # 预测头 - 使用LayerNorm替代BatchNorm
        self.predictor = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.LeakyReLU(0.2),
            nn.LayerNorm(hidden_dim // 2),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.LeakyReLU(0.2),
            nn.LayerNorm(hidden_dim // 4),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 4, hidden_dim // 8),
            nn.LeakyReLU(0.2),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 8, 1)
        )

        self.dropout_layer = nn.Dropout(dropout)

    def forward(self, g, x):
        h = x

        for i, (gat_layer, norm_layer, residual_layer) in enumerate(zip(self.gat_layers, self.norm_layers, self.residual_layers)):
            h_input = h

            if i < len(self.gat_layers) - 1:
                # 非最后一层：多头输出
                h = gat_layer(g, h).flatten(1)
            else:
                # 最后一层：单头输出
                h = gat_layer(g, h).squeeze(1)

            # 残差连接
            if self.use_residual:
                if residual_layer is not None:
                    h_input = residual_layer(h_input)
                h = h + h_input

            h = norm_layer(h)
            h = self.dropout_layer(h)

        # 图级池化 - 使用多种池化方式
        g.ndata['h'] = h
        graph_repr_mean = dgl.readout_nodes(g, 'h', op='mean')
        graph_repr_max = dgl.readout_nodes(g, 'h', op='max')
        graph_repr_sum = dgl.readout_nodes(g, 'h', op='sum')

        # 组合不同的池化结果
        graph_repr = (graph_repr_mean + graph_repr_max + graph_repr_sum) / 3.0

        return self.predictor(graph_repr).squeeze(-1)

def load_large_scale_data():
    """加载大规模数据"""
    
    print("🔄 Loading large-scale real optical network data...")
    
    datasets = {}
    
    # 1. 加载30%负载1000scenario数据
    try:
        with open('network_qot_30percent_1000scenarios.json', 'r') as f:
            data_30percent = json.load(f)
        print(f"✅ Loaded 30% load data: {len(data_30percent)} scenarios")
        datasets['30percent'] = data_30percent
    except Exception as e:
        print(f"❌ Failed to load 30% data: {e}")
    
    # 2. 加载日本网络数据
    try:
        with open('network_scenarios_japan.json', 'r') as f:
            data_japan = json.load(f)
        print(f"✅ Loaded Japan network data: {len(data_japan)} scenarios")
        datasets['japan'] = data_japan
    except Exception as e:
        print(f"❌ Failed to load Japan data: {e}")
    
    return datasets

def extract_features_from_30percent_data(data):
    """从30%负载数据中提取特征"""
    
    print(f"🔧 Extracting features from 30% load data...")
    
    graphs, features_list, targets = [], [], []
    
    for scenario in data:
        scenario_id = scenario.get('scenario_id', 0)
        network_load = scenario.get('network_load', 0.3)
        lightpaths = scenario.get('input_lightpaths', [])
        
        # 为每个lightpath创建一个子图
        for lp in lightpaths:
            source_uid = lp.get('source_uid', '')
            dest_uid = lp.get('dest_uid', '')
            channel_index = lp.get('channel_index', 0)
            power_dbm = lp.get('power_dbm', 0)
            
            # 提取节点编号
            try:
                source_id = int(source_uid.split()[-1]) if 'trx' in source_uid else 0
                dest_id = int(dest_uid.split()[-1]) if 'trx' in dest_uid else 1
            except:
                continue
            
            # 创建简单的2节点子图
            max_node = max(source_id, dest_id)
            g = dgl.graph(([source_id], [dest_id]), num_nodes=max_node + 1)
            
            # 节点特征 (10维)
            node_features = torch.zeros(max_node + 1, 10)
            
            # 源节点特征
            node_features[source_id, 0] = 1.0  # 标记为源节点
            node_features[source_id, 1] = power_dbm / 10.0  # 归一化功率
            node_features[source_id, 2] = channel_index / 100.0  # 归一化信道
            node_features[source_id, 3] = network_load  # 网络负载
            node_features[source_id, 4] = scenario_id / 1000.0  # scenario ID
            
            # 目标节点特征
            node_features[dest_id, 0] = -1.0  # 标记为目标节点
            node_features[dest_id, 1] = power_dbm / 10.0
            node_features[dest_id, 2] = channel_index / 100.0
            node_features[dest_id, 3] = network_load
            node_features[dest_id, 4] = scenario_id / 1000.0
            
            # 路径特征
            path_length = abs(dest_id - source_id)  # 简化的路径长度估计
            node_features[:, 5] = path_length / 20.0  # 归一化路径长度
            
            # 信道间隔特征
            node_features[:, 6] = (channel_index % 10) / 10.0  # 信道组
            
            # 功率级别特征
            power_level = 0 if power_dbm < -1 else 1 if power_dbm < 0 else 2
            node_features[:, 7] = power_level / 2.0
            
            # 距离特征
            node_features[:, 8] = abs(source_id - dest_id) / 20.0
            
            # 负载影响特征
            node_features[:, 9] = network_load * (channel_index / 100.0)
            
            # 目标值：从network_result中提取真实SNR
            network_result = scenario.get('network_result', {})
            if isinstance(network_result, dict) and 'lightpath_results' in network_result:
                # 寻找对应的lightpath结果
                found_snr = None
                for lp_id, lp_result in network_result['lightpath_results'].items():
                    if isinstance(lp_result, dict):
                        lp_info = lp_result.get('lightpath_info', {})
                        if (lp_info.get('source_uid') == source_uid and
                            lp_info.get('dest_uid') == dest_uid and
                            lp_info.get('channel_index') == channel_index):

                            qot = lp_result.get('qot_metrics', {})
                            snr = qot.get('snr_db', qot.get('osnr_db', None))
                            if snr is not None and snr > -50 and snr < 50:
                                found_snr = snr
                                break

                if found_snr is not None:
                    graphs.append(g)
                    features_list.append(node_features)
                    targets.append(found_snr)
    
    print(f"✅ Extracted {len(graphs)} samples from 30% load data")
    return graphs, features_list, targets

def extract_features_from_japan_data(data):
    """从日本网络数据中提取特征"""
    
    print(f"🔧 Extracting features from Japan network data...")
    
    graphs, features_list, targets = [], [], []
    
    for scenario in data:
        scenario_id = scenario.get('scenario_id', 0)
        results = scenario.get('results', [])
        
        for result in results:
            path = result.get('path', [])
            channel = result.get('channel', 0)
            power_dbm = result.get('power_dbm', 0)
            snr_db = result.get('snr_db_in_context', 20.0)
            interfering_count = result.get('interfering_channel_count', 0)
            
            # 提取路径中的节点
            nodes = []
            for element in path:
                if 'trx' in element:
                    try:
                        node_id = int(element.split()[-1])
                        nodes.append(node_id)
                    except:
                        continue
            
            if len(nodes) < 2:
                continue
            
            # 创建路径子图
            edges_src, edges_dst = [], []
            for i in range(len(nodes) - 1):
                edges_src.append(nodes[i])
                edges_dst.append(nodes[i + 1])
                # 添加反向边
                edges_src.append(nodes[i + 1])
                edges_dst.append(nodes[i])
            
            max_node = max(nodes)
            g = dgl.graph((edges_src, edges_dst), num_nodes=max_node + 1)
            
            # 节点特征 (12维)
            node_features = torch.zeros(max_node + 1, 12)
            
            for i, node_id in enumerate(nodes):
                # 节点在路径中的位置
                node_features[node_id, 0] = i / (len(nodes) - 1) if len(nodes) > 1 else 0
                
                # 是否为端点
                node_features[node_id, 1] = 1.0 if i == 0 else (-1.0 if i == len(nodes) - 1 else 0)
                
                # 功率特征
                node_features[node_id, 2] = power_dbm / 10.0
                
                # 信道特征
                node_features[node_id, 3] = channel / 100.0
                
                # 干扰信道数量
                node_features[node_id, 4] = interfering_count / 50.0
                
                # 路径长度特征
                node_features[node_id, 5] = len(nodes) / 10.0
                
                # 路径复杂度
                node_features[node_id, 6] = len(path) / 20.0
                
                # 节点度数
                degree = g.in_degrees()[node_id].float() + g.out_degrees()[node_id].float()
                node_features[node_id, 7] = degree / 10.0
                
                # scenario特征
                node_features[node_id, 8] = scenario_id / 200.0
                
                # 干扰密度
                node_features[node_id, 9] = interfering_count / len(nodes) if len(nodes) > 0 else 0
                
                # 信道组
                node_features[node_id, 10] = (channel % 20) / 20.0
                
                # 复合特征
                node_features[node_id, 11] = (power_dbm * interfering_count) / 100.0
            
            graphs.append(g)
            features_list.append(node_features)
            targets.append(snr_db)
    
    print(f"✅ Extracted {len(graphs)} samples from Japan network data")
    return graphs, features_list, targets

def create_simple_dataloader(graphs, features, targets, shuffle=True):
    """创建简单的数据加载器"""
    indices = list(range(len(graphs)))
    if shuffle:
        np.random.shuffle(indices)

    for idx in indices:
        yield graphs[idx], features[idx], targets[idx]

        yield batched_graph, batched_features, batch_targets

def train_and_evaluate_large_scale(model, graphs, features, targets, model_name, config):
    """大规模数据训练和评估"""
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    
    print(f"\n🚀 Training {model_name} on {len(graphs)} samples...")
    print(f"   Using device: {device}")
    
    # 数据分割
    indices = np.random.permutation(len(graphs))
    train_size = int(0.7 * len(indices))
    val_size = int(0.15 * len(indices))
    
    train_indices = indices[:train_size]
    val_indices = indices[train_size:train_size + val_size]
    test_indices = indices[train_size + val_size:]
    
    print(f"   Train: {len(train_indices)}, Val: {len(val_indices)}, Test: {len(test_indices)}")
    
    # 标准化目标值
    train_targets = [targets[i] for i in train_indices]
    scaler = StandardScaler()
    scaler.fit(np.array(train_targets).reshape(-1, 1))
    targets_norm = scaler.transform(np.array(targets).reshape(-1, 1)).flatten()
    
    # 优化器
    optimizer = torch.optim.AdamW(
        model.parameters(),
        lr=config['lr'],
        weight_decay=config.get('weight_decay', 1e-4)
    )
    
    scheduler = torch.optim.lr_scheduler.OneCycleLR(
        optimizer,
        max_lr=config['lr'] * 2,
        total_steps=config.get('epochs', 100),
        pct_start=0.1
    )
    
    # 训练循环
    best_val_r2 = -999
    best_test_results = None
    patience = 0
    max_patience = 15
    
    start_time = time.time()
    
    for epoch in range(config.get('epochs', 100)):
        # 训练
        model.train()
        train_loss = 0
        train_preds, train_targs = [], []
        
        # 简化训练 - 随机采样训练
        sample_size = min(1000, len(train_indices))
        sampled_indices = np.random.choice(train_indices, sample_size, replace=False)

        for idx in sampled_indices:
            g = graphs[idx].to(device)
            x = features[idx].to(device)
            y = torch.tensor(targets_norm[idx], dtype=torch.float32).to(device)

            optimizer.zero_grad()
            pred = model(g, x)
            loss = F.mse_loss(pred, y)
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
            optimizer.step()

            train_loss += loss.item()
            train_preds.append(pred.item())
            train_targs.append(y.item())
        
        scheduler.step()
        
        # 验证（采样验证）
        if epoch % 5 == 0:  # 每5轮验证一次
            model.eval()
            val_preds, val_targs = [], []

            # 采样验证
            val_sample_size = min(500, len(val_indices))
            val_sampled = np.random.choice(val_indices, val_sample_size, replace=False)

            with torch.no_grad():
                for idx in val_sampled:
                    g = graphs[idx].to(device)
                    x = features[idx].to(device)
                    y = targets_norm[idx]

                    pred = model(g, x)
                    val_preds.append(pred.item())
                    val_targs.append(y)

            val_r2 = r2_score(val_targs, val_preds) if val_preds else -999

            if val_r2 > best_val_r2:
                best_val_r2 = val_r2
                patience = 0

                # 测试
                test_preds, test_targs = [], []
                test_sample_size = min(1000, len(test_indices))
                test_sampled = np.random.choice(test_indices, test_sample_size, replace=False)

                with torch.no_grad():
                    for idx in test_sampled:
                        g = graphs[idx].to(device)
                        x = features[idx].to(device)
                        y = targets_norm[idx]

                        pred = model(g, x)
                        test_preds.append(pred.item())
                        test_targs.append(y)
                
                if test_preds:
                    test_r2 = r2_score(test_targs, test_preds)
                    test_rmse_norm = np.sqrt(mean_squared_error(test_targs, test_preds))
                    test_mae_norm = mean_absolute_error(test_targs, test_preds)
                    
                    # 反标准化
                    test_rmse = test_rmse_norm * scaler.scale_[0]
                    test_mae = test_mae_norm * scaler.scale_[0]
                    
                    best_test_results = {
                        'r2': test_r2,
                        'rmse': test_rmse,
                        'mae': test_mae,
                        'val_r2': val_r2,
                        'epoch': epoch,
                        'config': config,
                        'model_name': model_name
                    }
            else:
                patience += 1
                if patience >= max_patience:
                    print(f"   Early stopping at epoch {epoch}")
                    break
            
            if epoch % 20 == 0:
                train_r2 = r2_score(train_targs, train_preds) if train_preds else -999
                print(f"   Epoch {epoch:3d}: Train R²={train_r2:.4f}, Val R²={val_r2:.4f}")
    
    training_time = time.time() - start_time
    
    if best_test_results:
        best_test_results['training_time'] = training_time
        print(f"✅ {model_name} completed:")
        print(f"   Test R² = {best_test_results['r2']:.4f}")
        print(f"   Test RMSE = {best_test_results['rmse']:.3f} dB")
        print(f"   Test MAE = {best_test_results['mae']:.3f} dB")
        print(f"   Training time = {training_time:.1f}s")
    
    return best_test_results

def compare_traditional_methods_large_scale(graphs, features, targets, sample_size=5000):
    """大规模数据的传统方法对比"""
    
    print(f"\n🔧 Traditional methods on large scale data (sampling {sample_size} samples)...")
    
    # 随机采样以加速传统方法
    indices = np.random.choice(len(graphs), min(sample_size, len(graphs)), replace=False)
    
    # 提取图级特征
    graph_features = []
    sampled_targets = []
    
    for idx in indices:
        g = graphs[idx]
        node_feat = features[idx]
        
        # 图级统计特征
        num_nodes = g.num_nodes()
        num_edges = g.num_edges()
        avg_degree = (g.in_degrees().float() + g.out_degrees().float()).mean().item()
        
        # 节点特征统计
        node_feat_mean = node_feat.mean(dim=0)
        node_feat_max = node_feat.max(dim=0)[0]
        node_feat_std = node_feat.std(dim=0)
        
        # 组合特征
        features_combined = torch.cat([
            torch.tensor([num_nodes, num_edges, avg_degree]),
            node_feat_mean,
            node_feat_max,
            node_feat_std
        ])
        
        graph_features.append(features_combined.numpy())
        sampled_targets.append(targets[idx])
    
    X = np.array(graph_features)
    y = np.array(sampled_targets)
    
    # 数据分割
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, random_state=42
    )
    
    # 标准化
    scaler_X = StandardScaler()
    X_train_scaled = scaler_X.fit_transform(X_train)
    X_test_scaled = scaler_X.transform(X_test)
    
    results = {}
    
    # 线性回归
    lr = LinearRegression()
    lr.fit(X_train_scaled, y_train)
    pred = lr.predict(X_test_scaled)
    results['Linear Regression'] = {
        'r2': r2_score(y_test, pred),
        'rmse': np.sqrt(mean_squared_error(y_test, pred)),
        'mae': mean_absolute_error(y_test, pred)
    }
    
    # 岭回归
    ridge = Ridge(alpha=1.0)
    ridge.fit(X_train_scaled, y_train)
    pred = ridge.predict(X_test_scaled)
    results['Ridge Regression'] = {
        'r2': r2_score(y_test, pred),
        'rmse': np.sqrt(mean_squared_error(y_test, pred)),
        'mae': mean_absolute_error(y_test, pred)
    }
    
    # Lasso回归
    lasso = Lasso(alpha=0.1)
    lasso.fit(X_train_scaled, y_train)
    pred = lasso.predict(X_test_scaled)
    results['Lasso Regression'] = {
        'r2': r2_score(y_test, pred),
        'rmse': np.sqrt(mean_squared_error(y_test, pred)),
        'mae': mean_absolute_error(y_test, pred)
    }

    # 多项式回归 (2次)
    try:
        poly_features = PolynomialFeatures(degree=2, include_bias=False)
        X_train_poly = poly_features.fit_transform(X_train_scaled)
        X_test_poly = poly_features.transform(X_test_scaled)

        poly_lr = LinearRegression()
        poly_lr.fit(X_train_poly, y_train)
        pred = poly_lr.predict(X_test_poly)
        results['Polynomial Regression (2nd)'] = {
            'r2': r2_score(y_test, pred),
            'rmse': np.sqrt(mean_squared_error(y_test, pred)),
            'mae': mean_absolute_error(y_test, pred)
        }
    except Exception as e:
        print(f"   Polynomial regression failed: {e}")

    # 随机森林
    rf = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1)
    rf.fit(X_train_scaled, y_train)
    pred = rf.predict(X_test_scaled)
    results['Random Forest'] = {
        'r2': r2_score(y_test, pred),
        'rmse': np.sqrt(mean_squared_error(y_test, pred)),
        'mae': mean_absolute_error(y_test, pred)
    }

    # 梯度提升
    gb = GradientBoostingRegressor(n_estimators=100, random_state=42)
    gb.fit(X_train_scaled, y_train)
    pred = gb.predict(X_test_scaled)
    results['Gradient Boosting'] = {
        'r2': r2_score(y_test, pred),
        'rmse': np.sqrt(mean_squared_error(y_test, pred)),
        'mae': mean_absolute_error(y_test, pred)
    }

    # MLP神经网络
    try:
        mlp = MLPRegressor(hidden_layer_sizes=(100, 50), max_iter=500, random_state=42)
        mlp.fit(X_train_scaled, y_train)
        pred = mlp.predict(X_test_scaled)
        results['MLP Neural Network'] = {
            'r2': r2_score(y_test, pred),
            'rmse': np.sqrt(mean_squared_error(y_test, pred)),
            'mae': mean_absolute_error(y_test, pred)
        }
    except Exception as e:
        print(f"   MLP failed: {e}")

    for name, result in results.items():
        print(f"   {name}: R² = {result['r2']:.4f}, RMSE = {result['rmse']:.3f}, MAE = {result['mae']:.3f}")

    return results

def main():
    """主实验流程"""
    
    print("=" * 90)
    print("🚀 LARGE-SCALE REAL OPTICAL NETWORK EXPERIMENT")
    print("🌐 Using 23,000+ Real Network Samples")
    print("=" * 90)
    
    # 加载数据
    datasets = load_large_scale_data()
    
    if not datasets:
        print("❌ No datasets loaded!")
        return
    
    # 实验结果
    all_results = {}
    
    # 实验1: 30%负载数据
    if '30percent' in datasets:
        print(f"\n{'='*60}")
        print("📊 EXPERIMENT 1: 30% Load Network Data")
        print(f"{'='*60}")
        
        graphs_30, features_30, targets_30 = extract_features_from_30percent_data(datasets['30percent'])
        
        if len(graphs_30) > 0:
            print(f"Dataset: {len(graphs_30)} samples, SNR range: [{min(targets_30):.1f}, {max(targets_30):.1f}] dB")
            
            # 子图GAT模型
            model_configs = [
                {
                    'name': 'Subgraph GAT (Small)',
                    'model': ScalableSubgraphGAT(features_30[0].shape[1], hidden_dim=64, num_heads=4, num_layers=2),
                    'config': {'lr': 0.002, 'weight_decay': 1e-4, 'epochs': 50}
                },
                {
                    'name': 'Subgraph GAT (Medium)',
                    'model': ScalableSubgraphGAT(features_30[0].shape[1], hidden_dim=128, num_heads=6, num_layers=2),
                    'config': {'lr': 0.001, 'weight_decay': 5e-5, 'epochs': 80}
                }
            ]
            
            for model_info in model_configs:
                result = train_and_evaluate_large_scale(
                    model_info['model'], graphs_30, features_30, targets_30,
                    f"30% Load - {model_info['name']}", model_info['config']
                )
                if result:
                    all_results[f"30% Load - {model_info['name']}"] = result
            
            # 传统方法对比
            traditional_30 = compare_traditional_methods_large_scale(graphs_30, features_30, targets_30)
            for name, result in traditional_30.items():
                all_results[f"30% Load - {name}"] = result
    
    # 实验2: 日本网络数据
    if 'japan' in datasets:
        print(f"\n{'='*60}")
        print("🗾 EXPERIMENT 2: Japan Network Data")
        print(f"{'='*60}")
        
        graphs_japan, features_japan, targets_japan = extract_features_from_japan_data(datasets['japan'])
        
        if len(graphs_japan) > 0:
            print(f"Dataset: {len(graphs_japan)} samples, SNR range: [{min(targets_japan):.1f}, {max(targets_japan):.1f}] dB")
            
            # 子图GAT模型
            model_configs = [
                {
                    'name': 'Subgraph GAT (Small)',
                    'model': ScalableSubgraphGAT(features_japan[0].shape[1], hidden_dim=64, num_heads=4, num_layers=2),
                    'config': {'lr': 0.002, 'weight_decay': 1e-4, 'epochs': 60}
                },
                {
                    'name': 'Subgraph GAT (Large)',
                    'model': ScalableSubgraphGAT(features_japan[0].shape[1], hidden_dim=256, num_heads=8, num_layers=3),
                    'config': {'lr': 0.0005, 'weight_decay': 1e-5, 'epochs': 100}
                }
            ]
            
            for model_info in model_configs:
                result = train_and_evaluate_large_scale(
                    model_info['model'], graphs_japan, features_japan, targets_japan,
                    f"Japan - {model_info['name']}", model_info['config']
                )
                if result:
                    all_results[f"Japan - {model_info['name']}"] = result
            
            # 传统方法对比
            traditional_japan = compare_traditional_methods_large_scale(graphs_japan, features_japan, targets_japan)
            for name, result in traditional_japan.items():
                all_results[f"Japan - {name}"] = result
    
    # 最终结果总结
    print(f"\n{'='*90}")
    print("🏆 FINAL LARGE-SCALE EXPERIMENT RESULTS")
    print(f"{'='*90}")
    
    if all_results:
        # 按R²排序
        sorted_results = sorted(all_results.items(), key=lambda x: x[1]['r2'], reverse=True)
        
        print(f"{'Method':<40} {'R²':<8} {'RMSE':<8} {'MAE':<8} {'Time':<8}")
        print("-" * 80)
        
        for name, result in sorted_results:
            time_str = f"{result.get('training_time', 0):.1f}s" if 'training_time' in result else "N/A"
            print(f"{name:<40} {result['r2']:<8.4f} {result['rmse']:<8.3f} {result['mae']:<8.3f} {time_str:<8}")
        
        # 最佳方法分析
        best_method = sorted_results[0]
        print(f"\n🎯 Best Method: {best_method[0]}")
        print(f"   R² = {best_method[1]['r2']:.4f}")
        print(f"   RMSE = {best_method[1]['rmse']:.3f} dB")
        print(f"   MAE = {best_method[1]['mae']:.3f} dB")
        
        # 性能评估
        best_r2 = best_method[1]['r2']
        if best_r2 > 0.8:
            print(f"🎉 Excellent performance! Subgraph GAT is highly effective on large-scale data!")
        elif best_r2 > 0.6:
            print(f"✅ Good performance! Method shows clear effectiveness.")
        elif best_r2 > 0.4:
            print(f"⚠️  Moderate performance. Some improvement needed.")
        else:
            print(f"❌ Poor performance. Fundamental issues remain.")
        
        # 保存结果
        with open('large_scale_experiment_results.json', 'w') as f:
            json.dump(all_results, f, indent=2)
        
        print(f"\n📁 Results saved to: large_scale_experiment_results.json")
    
    else:
        print("❌ No results obtained!")

if __name__ == "__main__":
    main()