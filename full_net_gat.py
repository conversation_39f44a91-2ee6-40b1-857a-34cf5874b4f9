import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.data import DataLoader
from torch_geometric.nn import GATConv, global_mean_pool
import matplotlib.pyplot as plt
import numpy as np

# 1. 定义基于GAT的全网模型
class FullNetworkGAT(nn.Module):
    def __init__(self, num_node_features, num_channels, embedding_dim=64, gat_hidden_dim=128, num_heads=4):
        super(FullNetworkGAT, self).__init__()
        
        # GAT层用于学习节点在拓扑中的表示，使用多头注意力机制
        self.conv1 = GATConv(num_node_features, gat_hidden_dim // num_heads, heads=num_heads, dropout=0.1)
        self.conv2 = GATConv(gat_hidden_dim, gat_hidden_dim // num_heads, heads=num_heads, dropout=0.1)
        
        # 边（流量）特征的编码器
        self.edge_encoder = nn.Linear(num_channels, embedding_dim)
        
        # 路径节点和全局图表示的组合器
        self.combiner = nn.Linear(gat_hidden_dim + embedding_dim, gat_hidden_dim)
        
        # 最终的回归器，增加更多层以提高表达能力
        self.regressor = nn.Sequential(
            nn.Linear(gat_hidden_dim + 2, gat_hidden_dim), # +2 for channel and power
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(gat_hidden_dim, gat_hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(gat_hidden_dim // 2, 1)
        )

    def forward(self, data):
        x, edge_index, edge_attr = data.x, data.edge_index, data.edge_attr
        
        # 1. 通过GAT层传播节点特征，利用注意力机制学习节点间的重要性
        x = self.conv1(x, edge_index)
        x = F.elu(x)  # ELU激活函数在GAT中通常表现更好
        x = self.conv2(x, edge_index)
        
        # 2. 从全图中提取目标路径节点的表示
        # GAT的注意力机制能够自动关注对目标路径重要的网络区域
        path_nodes_features = x[data.target_path_nodes]
        
        # 3. 对路径上节点的特征进行平均池化，得到路径的整体表示
        path_embedding = global_mean_pool(path_nodes_features, data.target_path_nodes_batch)
        
        # 4. 对边（流量）特征进行编码和池化，得到网络的流量状态表示
        encoded_edge_attr = self.edge_encoder(edge_attr)
        traffic_embedding = global_mean_pool(encoded_edge_attr, data.batch[edge_index[0]])
        
        # 5. 结合路径表示和流量表示
        combined_graph_features = torch.cat([path_embedding, traffic_embedding], dim=1)
        combined_graph_features = F.elu(self.combiner(combined_graph_features))
        
        # 6. 加入目标信道和功率信息
        # 归一化信道和功率以获得更好的性能
        target_channel_norm = data.target_channel.unsqueeze(1) / 80.0
        target_power_norm = data.target_power.unsqueeze(1) / 5.0 # 假设功率在-5到5之间
        
        final_features = torch.cat([combined_graph_features, target_channel_norm, target_power_norm], dim=1)
        
        # 7. 预测OSNR
        return self.regressor(final_features)

# 2. 准备数据加载器
def create_dataloaders(data_file, batch_size):
    print(f"正在从 '{data_file}' 加载数据...")
    dataset = torch.load(data_file)
    # 使用follow_batch参数为target_path_nodes创建批次索引
    train_loader = DataLoader(dataset['train'], batch_size=batch_size, shuffle=True, follow_batch=['target_path_nodes'])
    val_loader = DataLoader(dataset['val'], batch_size=batch_size, shuffle=False, follow_batch=['target_path_nodes'])
    test_loader = DataLoader(dataset['test'], batch_size=batch_size, shuffle=False, follow_batch=['target_path_nodes'])
    print("数据加载器已创建。")
    return train_loader, val_loader, test_loader, dataset

# 3. 训练和评估循环
def train_and_evaluate(model, train_loader, val_loader, epochs, learning_rate):
    from sklearn.metrics import r2_score
    
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate, weight_decay=1e-5)
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.7, patience=15, verbose=True)
    
    train_losses, val_losses = [], []
    train_r2s, val_r2s = [], []
    best_val_loss = float('inf')
    patience_counter = 0
    early_stop_patience = 30
    
    # 检查数据范围
    print("🔍 检查数据范围...")
    sample_data = next(iter(train_loader))
    print(f"   目标OSNR范围: {sample_data.y.min().item():.2f} - {sample_data.y.max().item():.2f} dB")
    print(f"   节点特征维度: {sample_data.x.shape}")
    print(f"   边特征维度: {sample_data.edge_attr.shape}")
    
    for epoch in range(epochs):
        model.train()
        total_train_loss = 0
        train_preds, train_targets = [], []
        
        for data in train_loader:
            optimizer.zero_grad()
            predictions = model(data)
            loss = criterion(predictions, data.y.view(-1, 1))
            
            # 检查损失是否异常
            if torch.isnan(loss) or torch.isinf(loss):
                print(f"⚠️  检测到异常损失: {loss.item()}")
                continue
                
            loss.backward()
            # 梯度裁剪，对GAT更重要
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            total_train_loss += loss.item()
            train_preds.extend(predictions.view(-1).detach().cpu().numpy())
            train_targets.extend(data.y.view(-1).cpu().numpy())
        
        avg_train_loss = total_train_loss / len(train_loader)
        train_losses.append(avg_train_loss)
        
        # 验证阶段
        model.eval()
        total_val_loss = 0
        val_preds, val_targets = [], []
        
        with torch.no_grad():
            for data in val_loader:
                predictions = model(data)
                loss = criterion(predictions, data.y.view(-1, 1))
                total_val_loss += loss.item()
                val_preds.extend(predictions.view(-1).cpu().numpy())
                val_targets.extend(data.y.view(-1).cpu().numpy())
        
        avg_val_loss = total_val_loss / len(val_loader)
        val_losses.append(avg_val_loss)
        
        # 计算R²分数
        train_r2 = r2_score(train_targets, train_preds) if len(train_targets) > 0 else -1
        val_r2 = r2_score(val_targets, val_preds) if len(val_targets) > 0 else -1
        train_r2s.append(train_r2)
        val_r2s.append(val_r2)
        
        # 学习率调度
        scheduler.step(avg_val_loss)
        
        print(f"Epoch {epoch+1}/{epochs} | Train Loss: {avg_train_loss:.4f}, R²: {train_r2:.4f} | "
              f"Val Loss: {avg_val_loss:.4f}, R²: {val_r2:.4f} | RMSE: {np.sqrt(avg_val_loss):.4f} dB")
        
        # 保存最佳模型
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            patience_counter = 0
            torch.save(model.state_dict(), 'best_full_net_gat.pth')
            print(f"  -> 🎯 GAT模型已保存，验证RMSE为 {np.sqrt(best_val_loss):.4f} dB")
        else:
            patience_counter += 1
            
        # 早停机制
        if patience_counter >= early_stop_patience:
            print(f"早停触发！验证损失在 {early_stop_patience} 个epoch内没有改善。")
            break
            
    return {
        'train_losses': train_losses,
        'val_losses': val_losses, 
        'train_r2s': train_r2s,
        'val_r2s': val_r2s
    }

# 4. 测试函数
def test_model(model, test_loader):
    from sklearn.metrics import r2_score, mean_absolute_error
    
    model.eval()
    test_preds, test_targets = [], []
    
    with torch.no_grad():
        for data in test_loader:
            predictions = model(data)
            test_preds.extend(predictions.view(-1).cpu().numpy())
            test_targets.extend(data.y.view(-1).cpu().numpy())
    
    test_r2 = r2_score(test_targets, test_preds)
    test_rmse = np.sqrt(np.mean((np.array(test_targets) - np.array(test_preds))**2))
    test_mae = mean_absolute_error(test_targets, test_preds)
    
    print(f"\n🧪 测试集性能:")
    print(f"   R² Score: {test_r2:.4f}")
    print(f"   RMSE: {test_rmse:.4f} dB")
    print(f"   MAE: {test_mae:.4f} dB")
    
    return {
        'r2': test_r2,
        'rmse': test_rmse,
        'mae': test_mae,
        'predictions': test_preds,
        'targets': test_targets
    }

# 5. 绘图函数
def plot_training_curves(history):
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 损失曲线
    ax1.plot(history['train_losses'], label='训练损失', linewidth=2)
    ax1.plot(history['val_losses'], label='验证损失', linewidth=2)
    ax1.set_title('全网GAT模型 - 损失曲线', fontsize=14, fontweight='bold')
    ax1.set_xlabel('训练轮次')
    ax1.set_ylabel('MSE损失')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # R²曲线
    ax2.plot(history['train_r2s'], label='训练R²', linewidth=2)
    ax2.plot(history['val_r2s'], label='验证R²', linewidth=2)
    ax2.set_title('全网GAT模型 - R²分数曲线', fontsize=14, fontweight='bold')
    ax2.set_xlabel('训练轮次')
    ax2.set_ylabel('R²分数')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('full_net_gat_training_curves.png', dpi=300, bbox_inches='tight')
    print("📊 训练曲线图已保存为 'full_net_gat_training_curves.png'")

def plot_prediction_scatter(test_results):
    """绘制预测值vs真实值的散点图"""
    plt.figure(figsize=(10, 8))
    
    targets = test_results['targets']
    predictions = test_results['predictions']
    
    plt.scatter(targets, predictions, alpha=0.6, s=20)
    
    # 绘制理想预测线
    min_val = min(min(targets), min(predictions))
    max_val = max(max(targets), max(predictions))
    plt.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='理想预测')
    
    plt.xlabel('真实OSNR (dB)', fontsize=12)
    plt.ylabel('预测OSNR (dB)', fontsize=12)
    plt.title(f'全网GAT模型预测性能\nR² = {test_results["r2"]:.4f}, RMSE = {test_results["rmse"]:.4f} dB', 
              fontsize=14, fontweight='bold')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('full_net_gat_prediction_scatter.png', dpi=300, bbox_inches='tight')
    print("📈 预测散点图已保存为 'full_net_gat_prediction_scatter.png'")

# 6. 主执行函数
if __name__ == '__main__':
    print("🚀 启动全网GAT光网络OSNR预测系统")
    print("=" * 60)
    
    # 超参数
    DATA_FILE = 'processed_scenarios.pt'
    BATCH_SIZE = 16  # GAT通常需要较小的batch size
    EPOCHS = 300
    LEARNING_RATE = 0.001
    NUM_HEADS = 4  # GAT注意力头数
    
    print(f"📋 训练配置:")
    print(f"   数据文件: {DATA_FILE}")
    print(f"   批次大小: {BATCH_SIZE}")
    print(f"   训练轮次: {EPOCHS}")
    print(f"   学习率: {LEARNING_RATE}")
    print(f"   注意力头数: {NUM_HEADS}")
    print()

    # 准备数据
    train_loader, val_loader, test_loader, dataset_info = create_dataloaders(DATA_FILE, BATCH_SIZE)
    
    # 初始化GAT模型
    model = FullNetworkGAT(
        num_node_features=dataset_info['num_node_features'],
        num_channels=dataset_info['num_channels'],
        num_heads=NUM_HEADS
    )
    
    print("🧠 GAT模型结构:")
    print(model)
    print(f"\n📊 模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    print()
    
    # 训练模型
    print("🎯 开始训练全网GAT模型...")
    history = train_and_evaluate(model, train_loader, val_loader, EPOCHS, LEARNING_RATE)
    
    # 加载最佳模型并测试
    print("\n🔄 加载最佳模型进行测试...")
    model.load_state_dict(torch.load('best_full_net_gat.pth'))
    test_results = test_model(model, test_loader)
    
    # 绘制结果
    plot_training_curves(history)
    plot_prediction_scatter(test_results)
    
    print("\n✅ 全网GAT模型训练完成！")
    print("=" * 60)
    print("📁 生成的文件:")
    print("   - best_full_net_gat.pth (最佳模型权重)")
    print("   - full_net_gat_training_curves.png (训练曲线)")
    print("   - full_net_gat_prediction_scatter.png (预测散点图)") 