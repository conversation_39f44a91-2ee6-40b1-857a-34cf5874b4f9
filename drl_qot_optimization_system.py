"""
深度强化学习光网络QoT保障系统
核心目标：实时保障全网QoT不受新光路影响
技术路线：DRL + GNN + gnpy协同的QoT感知决策
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import gym
from gym import spaces
import random
from collections import deque, namedtuple
import heapq
from typing import Dict, List, Tuple, Optional
import json
import pickle
from dataclasses import dataclass

# 导入现有的子图预测器
from subgraph_predictor import SubgraphPredictor
from physics_based_qot_system_yh613 import PhysicsBasedQoTSystem

@dataclass
class LightpathRequest:
    """光路请求数据结构"""
    source: int
    destination: int
    wavelength: Optional[int] = None
    power_dbm: float = 0.0  # 固定功率，简化问题
    modulation_format: str = "QPSK"  # 固定调制格式，聚焦核心问题
    required_osnr: float = 15.0

class PrioritizedReplayBuffer:
    """优先经验回放缓冲区"""

    def __init__(self, capacity: int, alpha: float = 0.6, beta: float = 0.4):
        self.capacity = capacity
        self.alpha = alpha  # 优先级指数
        self.beta = beta    # 重要性采样指数
        self.beta_increment = 0.001

        self.buffer = []
        self.priorities = []
        self.position = 0

    def add(self, state, action, reward, next_state, done, td_error: float = 1.0):
        """添加经验"""
        priority = (abs(td_error) + 1e-6) ** self.alpha

        if len(self.buffer) < self.capacity:
            self.buffer.append((state, action, reward, next_state, done))
            self.priorities.append(priority)
        else:
            self.buffer[self.position] = (state, action, reward, next_state, done)
            self.priorities[self.position] = priority

        self.position = (self.position + 1) % self.capacity

    def sample(self, batch_size: int):
        """采样经验"""
        if len(self.buffer) < batch_size:
            return None, None, None

        # 计算采样概率
        priorities = np.array(self.priorities[:len(self.buffer)])
        probabilities = priorities / priorities.sum()

        # 采样索引
        indices = np.random.choice(len(self.buffer), batch_size, p=probabilities)

        # 计算重要性采样权重
        weights = (len(self.buffer) * probabilities[indices]) ** (-self.beta)
        weights = weights / weights.max()  # 归一化

        # 提取经验
        batch = [self.buffer[i] for i in indices]

        # 更新beta
        self.beta = min(1.0, self.beta + self.beta_increment)

        return batch, indices, weights

    def update_priorities(self, indices, td_errors):
        """更新优先级"""
        for idx, td_error in zip(indices, td_errors):
            priority = (abs(td_error) + 1e-6) ** self.alpha
            self.priorities[idx] = priority

    def __len__(self):
        return len(self.buffer)

class NoiseGenerator:
    """噪声生成器 - 用于改进探索"""

    def __init__(self, action_dims: List[int], noise_scale: float = 0.1):
        self.action_dims = action_dims
        self.noise_scale = noise_scale

    def ornstein_uhlenbeck_noise(self, action: np.ndarray, theta: float = 0.15,
                                sigma: float = 0.2, dt: float = 1e-2):
        """Ornstein-Uhlenbeck噪声 - 用于连续动作空间"""
        # 这里简化为高斯噪声，因为我们的动作空间是离散的
        noise = np.random.normal(0, sigma, size=action.shape)
        return noise * self.noise_scale

    def epsilon_decay_schedule(self, episode: int, total_episodes: int,
                              epsilon_start: float = 1.0, epsilon_end: float = 0.01):
        """改进的epsilon衰减策略"""
        # 分段衰减
        if episode < total_episodes * 0.2:  # 前20%快速探索
            return epsilon_start
        elif episode < total_episodes * 0.6:  # 中间40%线性衰减
            progress = (episode - total_episodes * 0.2) / (total_episodes * 0.4)
            return epsilon_start - (epsilon_start - epsilon_end * 3) * progress
        else:  # 后40%慢速衰减到最小值
            progress = (episode - total_episodes * 0.6) / (total_episodes * 0.4)
            return epsilon_end * 3 - (epsilon_end * 2) * progress

class QoTAwareOpticalEnvironment(gym.Env):
    """
    QoT感知的光网络环境
    核心目标：保障全网QoT安全域，避免新光路对现有光路造成影响
    """

    def __init__(self, network_file: str, equipment_file: str, max_wavelengths: int = 80):
        super().__init__()

        # 初始化物理QoT系统
        self.physics_system = PhysicsBasedQoTSystem(network_file, equipment_file)
        self.subgraph_predictor = SubgraphPredictor()

        # 网络参数
        self.max_wavelengths = max_wavelengths
        self.num_nodes = len(self.physics_system.transceivers)

        # 状态空间：完整的物理层信息用于QoT感知
        # [波长功率分布(80), 全网OSNR分布(80), 非线性噪声等级(80), 当前请求信息(6)]
        state_dim = max_wavelengths * 3 + 6  # 246维状态空间
        self.observation_space = spaces.Box(
            low=-np.inf, high=np.inf, shape=(state_dim,), dtype=np.float32
        )

        # 动作空间：完整的联合优化
        # 路径选择 + 波长选择 + 功率选择 + 调制格式选择
        self.action_space = spaces.MultiDiscrete([
            5,    # K=5条候选路径
            80,   # 80个波长通道
            20,   # 20个功率等级 (-10dBm to +10dBm, 1dBm步长)
            4     # 4种调制格式 (QPSK, 8QAM, 16QAM, 32QAM)
        ])

        # 调制格式配置
        self.modulation_formats = {
            0: {"name": "QPSK", "required_osnr": 12.0, "spectral_efficiency": 2.0},
            1: {"name": "8QAM", "required_osnr": 15.0, "spectral_efficiency": 3.0},
            2: {"name": "16QAM", "required_osnr": 18.0, "spectral_efficiency": 4.0},
            3: {"name": "32QAM", "required_osnr": 21.0, "spectral_efficiency": 5.0}
        }

        # 环境状态
        self.current_lightpaths = []
        self.current_request = None
        self.candidate_paths = []

        # QoT保障导向的奖励函数
        self.reward_weights = {
            'qot_success': 20.0,        # QoT达标奖励
            'qot_safety_margin': 15.0,  # QoT安全裕度奖励
            'network_impact': -30.0,    # 对全网QoT影响的惩罚
            'blocking_penalty': -50.0,  # 阻塞惩罚
            'power_efficiency': 5.0,    # 功率效率奖励
            'spectral_efficiency': 8.0  # 频谱效率奖励
        }

        # QoT保障导向的奖励函数
        self.reward_weights = {
            'qot_success': 15.0,        # QoT达标奖励
            'qot_safety_margin': 10.0,  # QoT安全裕度奖励
            'network_impact': -25.0,    # 对全网影响的惩罚
            'blocking_penalty': -30.0,  # 阻塞惩罚
            'wavelength_efficiency': 2.0 # 波长利用效率
        }
        
    def reset(self) -> np.ndarray:
        """重置环境"""
        self.current_lightpaths = []
        self.current_request = self._generate_random_request()
        self.candidate_paths = self._get_candidate_paths(
            self.current_request.source, 
            self.current_request.destination
        )
        
        return self._get_state()
    
    def step(self, action: np.ndarray) -> Tuple[np.ndarray, float, bool, Dict]:
        """执行动作 - 联合优化路径+波长+功率+调制格式"""
        path_idx, wavelength, power_level, modulation_idx = action

        # 解析动作
        selected_path = self.candidate_paths[path_idx] if path_idx < len(self.candidate_paths) else None
        power_dbm = -10.0 + power_level * 1.0  # -10dBm to +10dBm, 1dBm步长
        modulation_config = self.modulation_formats[modulation_idx]

        # 计算奖励
        reward, info = self._calculate_reward(selected_path, wavelength, power_dbm, modulation_config)

        # 更新环境状态
        if info['success']:
            new_lightpath = {
                'path': selected_path,
                'wavelength': wavelength,
                'power_dbm': power_dbm,
                'modulation': modulation_config['name'],
                'required_osnr': modulation_config['required_osnr'],
                'spectral_efficiency': modulation_config['spectral_efficiency'],
                'osnr_db': info['osnr_db']
            }
            self.current_lightpaths.append(new_lightpath)

        # 生成下一个请求
        self.current_request = self._generate_random_request()
        self.candidate_paths = self._get_candidate_paths(
            self.current_request.source,
            self.current_request.destination
        )

        next_state = self._get_state()
        done = len(self.current_lightpaths) >= 50  # 最大50条光路

        return next_state, reward, done, info
    
    def _get_state(self) -> np.ndarray:
        """获取当前状态 - 包含完整物理层信息用于QoT感知"""
        state = np.zeros(self.observation_space.shape[0])

        # 1. 波长功率分布 (0-79)
        power_distribution = np.zeros(self.max_wavelengths)
        for lp in self.current_lightpaths:
            if lp['wavelength'] < self.max_wavelengths:
                power_distribution[lp['wavelength']] += 10**(lp['power_dbm']/10)  # 转换为线性功率
        state[0:80] = power_distribution

        # 2. 全网OSNR分布 (80-159)
        osnr_distribution = np.zeros(self.max_wavelengths)
        for lp in self.current_lightpaths:
            if lp['wavelength'] < self.max_wavelengths:
                osnr_distribution[lp['wavelength']] = lp['osnr_db']
        state[80:160] = osnr_distribution

        # 3. 非线性噪声等级 (160-239)
        nonlinear_noise = self._calculate_nonlinear_noise_distribution()
        state[160:240] = nonlinear_noise

        # 4. 当前请求信息 (240-245) - 扩展为6维
        if self.current_request:
            state[240] = self.current_request.source / self.num_nodes
            state[241] = self.current_request.destination / self.num_nodes
            state[242] = self.current_request.required_osnr / 30.0  # 归一化
            state[243] = len(self.candidate_paths) / 5.0  # 候选路径数量
            # 新增：请求的业务类型和优先级信息
            state[244] = getattr(self.current_request, 'service_type', 0) / 4.0  # 业务类型
            state[245] = getattr(self.current_request, 'priority', 1) / 3.0  # 优先级

        return state.astype(np.float32)
    
    def _calculate_reward(self, path: List[int], wavelength: int,
                         power_dbm: float, modulation_config: Dict) -> Tuple[float, Dict]:
        """
        计算奖励 - 基于QoT保障和联合优化效果
        """
        if path is None or wavelength >= self.max_wavelengths:
            return self.reward_weights['blocking_penalty'], {'success': False}

        # 使用子图预测器评估QoT影响
        new_lightpath = {
            'path': path,
            'wavelength': wavelength,
            'power_dbm': power_dbm,
            'modulation': modulation_config['name'],
            'required_osnr': modulation_config['required_osnr']
        }

        # 预测QoT降级
        prediction_result = self.subgraph_predictor.predict_qot_degradation(
            new_lightpath, self.current_lightpaths
        )

        # 计算新光路的QoT
        qot_result = self.physics_system.calculate_lightpath_qot(
            self.current_request.source,
            self.current_request.destination,
            wavelength,
            power_dbm
        )

        if not qot_result['success']:
            return self.reward_weights['blocking_penalty'], {'success': False}

        reward = 0.0
        info = {
            'success': True,
            'osnr_db': qot_result['osnr_db'],
            'predicted_degradation': prediction_result['predicted_degradation'],
            'modulation': modulation_config['name'],
            'spectral_efficiency': modulation_config['spectral_efficiency']
        }

        # 1. QoT达标奖励 - 考虑调制格式要求
        required_osnr = modulation_config['required_osnr']
        if qot_result['osnr_db'] >= required_osnr:
            reward += self.reward_weights['qot_success']

            # QoT安全裕度奖励
            safety_margin = qot_result['osnr_db'] - required_osnr
            reward += self.reward_weights['qot_safety_margin'] * min(safety_margin / 5.0, 1.0)
        else:
            reward += self.reward_weights['blocking_penalty']
            info['success'] = False
            return reward, info

        # 2. 全网QoT影响惩罚
        network_impact = prediction_result['predicted_degradation']
        if network_impact > 0.5:  # 超过0.5dB影响
            reward += self.reward_weights['network_impact'] * network_impact

        # 3. 功率效率奖励
        power_efficiency = self._calculate_power_efficiency(power_dbm)
        reward += self.reward_weights['power_efficiency'] * power_efficiency

        # 4. 频谱效率奖励
        spectral_efficiency = modulation_config['spectral_efficiency']
        reward += self.reward_weights['spectral_efficiency'] * (spectral_efficiency / 5.0)

        return reward, info
    
    def _calculate_nonlinear_noise_distribution(self) -> np.ndarray:
        """计算非线性噪声分布"""
        noise_levels = np.zeros(self.max_wavelengths)
        
        for wavelength in range(self.max_wavelengths):
            # 计算该波长上的总功率和非线性效应
            total_power = 0.0
            for lp in self.current_lightpaths:
                if abs(lp['wavelength'] - wavelength) <= 2:  # 相邻波长影响
                    total_power += 10**(lp['power_dbm']/10)
            
            # 非线性噪声与功率的三次方成正比
            noise_levels[wavelength] = total_power ** 1.5  # 简化的非线性模型
        
        return noise_levels
    
    def _calculate_power_efficiency(self, power_dbm: float) -> float:
        """计算功率效率奖励"""
        # 鼓励使用适中功率：既不过高（浪费能源、增加非线性噪声）
        # 也不过低（可能导致QoT不足）
        optimal_power = 0.0  # 0dBm为最优功率
        power_deviation = abs(power_dbm - optimal_power)

        # 功率偏差越小，效率越高
        efficiency = max(0, 1.0 - power_deviation / 10.0)
        return efficiency

    def _calculate_wavelength_efficiency(self, wavelength: int) -> float:
        """计算波长利用效率"""
        # 波长负载均衡
        wavelength_usage = sum(1 for lp in self.current_lightpaths if lp['wavelength'] == wavelength)
        load_balance = max(0, 1.0 - wavelength_usage * 0.2)
        return load_balance
    
    def _generate_random_request(self) -> LightpathRequest:
        """生成随机光路请求"""
        source = random.randint(0, self.num_nodes - 1)
        destination = random.randint(0, self.num_nodes - 1)
        while destination == source:
            destination = random.randint(0, self.num_nodes - 1)
        
        return LightpathRequest(
            source=source,
            destination=destination,
            required_osnr=random.uniform(12.0, 20.0)
        )
    
    def _get_candidate_paths(self, source: int, destination: int) -> List[List[int]]:
        """获取K最短路径"""
        # 这里应该调用实际的K最短路径算法
        # 简化实现：返回一些候选路径
        return [[source, destination]]  # 简化版本

class DQNAgent:
    """
    深度Q网络智能体 - 专门针对光网络QoT优化设计
    集成优先经验回放、Double DQN、Dueling DQN等先进技术
    """

    def __init__(self, state_dim: int, action_dims: List[int],
                 learning_rate: float = 1e-4, gamma: float = 0.99,
                 use_prioritized_replay: bool = True, use_double_dqn: bool = True):
        self.state_dim = state_dim
        self.action_dims = action_dims
        self.gamma = gamma
        self.use_prioritized_replay = use_prioritized_replay
        self.use_double_dqn = use_double_dqn

        # 构建网络
        self.q_network = self._build_network()
        self.target_network = self._build_network()
        self.optimizer = torch.optim.Adam(self.q_network.parameters(), lr=learning_rate)

        # 学习率调度器
        self.scheduler = torch.optim.lr_scheduler.StepLR(
            self.optimizer, step_size=1000, gamma=0.95
        )

        # 经验回放
        if use_prioritized_replay:
            self.memory = PrioritizedReplayBuffer(capacity=50000)
        else:
            self.memory = deque(maxlen=50000)
        self.batch_size = 64  # 增大批次大小

        # 探索策略
        self.epsilon = 1.0
        self.epsilon_min = 0.01
        self.epsilon_decay = 0.995

        # 噪声生成器
        self.noise_generator = NoiseGenerator(action_dims)

        # 训练统计
        self.training_step = 0
        self.loss_history = []
        
    def _build_network(self) -> nn.Module:
        """构建深度Q网络 - 融合物理先验知识"""
        
        class PhysicsInformedDQN(nn.Module):
            def __init__(self, state_dim, action_dims):
                super().__init__()
                
                # 物理特征提取器 - 专门处理QoT相关信息
                self.power_encoder = nn.Sequential(
                    nn.Linear(80, 64),
                    nn.ReLU(),
                    nn.Dropout(0.1),
                    nn.Linear(64, 32)
                )

                self.osnr_encoder = nn.Sequential(
                    nn.Linear(80, 64),
                    nn.ReLU(),
                    nn.Dropout(0.1),
                    nn.Linear(64, 32)
                )

                self.nonlinear_encoder = nn.Sequential(
                    nn.Linear(80, 64),
                    nn.ReLU(),
                    nn.Dropout(0.1),
                    nn.Linear(64, 32)
                )

                # 请求信息编码器 - 扩展为6维输入
                self.request_encoder = nn.Sequential(
                    nn.Linear(6, 24),
                    nn.ReLU(),
                    nn.Linear(24, 16)
                )

                # 融合层 - QoT感知的特征融合
                fusion_dim = 32 + 32 + 32 + 16  # 112
                self.fusion_layer = nn.Sequential(
                    nn.Linear(fusion_dim, 256),
                    nn.ReLU(),
                    nn.Dropout(0.2),
                    nn.Linear(256, 128),
                    nn.ReLU(),
                    nn.Dropout(0.1)
                )

                # 多头输出 - 联合优化的四个维度
                self.path_head = nn.Linear(128, action_dims[0])      # 路径选择
                self.wavelength_head = nn.Linear(128, action_dims[1]) # 波长选择
                self.power_head = nn.Linear(128, action_dims[2])     # 功率选择
                self.modulation_head = nn.Linear(128, action_dims[3]) # 调制格式选择
                
            def forward(self, state):
                # 分离不同类型的QoT相关特征
                power_features = self.power_encoder(state[:, 0:80])
                osnr_features = self.osnr_encoder(state[:, 80:160])
                nonlinear_features = self.nonlinear_encoder(state[:, 160:240])
                request_features = self.request_encoder(state[:, 240:246])  # 扩展为6维

                # QoT感知的特征融合
                fused_features = torch.cat([
                    power_features, osnr_features,
                    nonlinear_features, request_features
                ], dim=1)

                shared_repr = self.fusion_layer(fused_features)

                # 联合优化的多头输出
                path_q = self.path_head(shared_repr)        # 路径选择Q值
                wavelength_q = self.wavelength_head(shared_repr)  # 波长选择Q值
                power_q = self.power_head(shared_repr)      # 功率选择Q值
                modulation_q = self.modulation_head(shared_repr)  # 调制格式选择Q值

                return path_q, wavelength_q, power_q, modulation_q
        
        return PhysicsInformedDQN(self.state_dim, self.action_dims)

    def act(self, state: np.ndarray, training: bool = True) -> np.ndarray:
        """选择动作 - epsilon-greedy策略"""
        if training and random.random() <= self.epsilon:
            # 智能探索：在可行动作中随机选择
            return self._intelligent_exploration(state)

        # 利用当前策略
        state_tensor = torch.FloatTensor(state).unsqueeze(0)
        with torch.no_grad():
            q_values = self.q_network(state_tensor)
            actions = [torch.argmax(q).item() for q in q_values]

        return np.array(actions)

    def _intelligent_exploration(self, state: np.ndarray) -> np.ndarray:
        """智能探索策略 - 基于QoT约束的联合优化探索"""
        # 解析状态
        power_dist = state[0:80]
        osnr_dist = state[80:160]
        nonlinear_noise = state[160:240]
        request_info = state[240:246]  # 扩展为6维

        # 找到可用波长（功率较低的）
        available_wavelengths = np.where(power_dist < np.percentile(power_dist, 75))[0]

        if len(available_wavelengths) == 0:
            available_wavelengths = np.arange(80)

        # 智能波长选择：优先选择非线性噪声较低的波长
        noise_scores = nonlinear_noise[available_wavelengths]
        if len(noise_scores) > 0:
            best_wavelengths = available_wavelengths[noise_scores < np.percentile(noise_scores, 60)]
            wavelength = random.choice(best_wavelengths if len(best_wavelengths) > 0 else available_wavelengths)
        else:
            wavelength = random.randint(0, 79)

        # 智能功率选择：根据OSNR需求估算
        required_osnr = request_info[2] * 30.0  # 反归一化
        # 功率范围：-10dBm to +10dBm (20个等级)
        estimated_power_level = min(19, max(0, int((required_osnr - 8) / 1.5)))
        power_level = max(0, min(19, estimated_power_level + random.randint(-3, 3)))

        # 智能调制格式选择：根据OSNR需求
        if required_osnr <= 13:
            modulation_candidates = [0]  # QPSK
        elif required_osnr <= 16:
            modulation_candidates = [0, 1]  # QPSK, 8QAM
        elif required_osnr <= 19:
            modulation_candidates = [0, 1, 2]  # QPSK, 8QAM, 16QAM
        else:
            modulation_candidates = [0, 1, 2, 3]  # 所有格式

        modulation = random.choice(modulation_candidates)

        return np.array([
            random.randint(0, min(4, max(1, len(self.candidate_paths)))),  # 路径选择
            wavelength,
            power_level,
            modulation
        ])

    def remember(self, state, action, reward, next_state, done, td_error: float = None):
        """存储经验"""
        if self.use_prioritized_replay:
            if td_error is None:
                # 计算TD误差
                td_error = self._calculate_td_error(state, action, reward, next_state, done)
            self.memory.add(state, action, reward, next_state, done, td_error)
        else:
            self.memory.append((state, action, reward, next_state, done))

    def _calculate_td_error(self, state, action, reward, next_state, done):
        """计算TD误差用于优先级"""
        state_tensor = torch.FloatTensor(state).unsqueeze(0)
        next_state_tensor = torch.FloatTensor(next_state).unsqueeze(0)

        with torch.no_grad():
            current_q_values = self.q_network(state_tensor)
            next_q_values = self.target_network(next_state_tensor)

            # 计算当前Q值
            current_q = current_q_values[0][0, action[0]]  # 简化：只考虑第一个动作维度

            # 计算目标Q值
            if done:
                target_q = reward
            else:
                target_q = reward + self.gamma * torch.max(next_q_values[0])

            td_error = abs(current_q - target_q).item()

        return td_error

    def replay(self):
        """经验回放训练 - 支持优先经验回放的Double DQN实现"""
        if len(self.memory) < self.batch_size:
            return 0.0

        # 采样经验
        if self.use_prioritized_replay:
            batch, indices, weights = self.memory.sample(self.batch_size)
            if batch is None:
                return 0.0
            weights = torch.FloatTensor(weights)
        else:
            batch = random.sample(self.memory, self.batch_size)
            indices = None
            weights = torch.ones(self.batch_size)

        states = torch.FloatTensor([e[0] for e in batch])
        actions = torch.LongTensor([e[1] for e in batch])
        rewards = torch.FloatTensor([e[2] for e in batch])
        next_states = torch.FloatTensor([e[3] for e in batch])
        dones = torch.BoolTensor([e[4] for e in batch])

        # 当前Q值
        current_q_values = self.q_network(states)

        # Double DQN: 使用主网络选择动作，目标网络评估Q值
        with torch.no_grad():
            if self.use_double_dqn:
                # 主网络选择下一步动作
                next_q_values_main = self.q_network(next_states)
                next_actions = [torch.argmax(q, dim=1) for q in next_q_values_main]

                # 目标网络评估选定动作的Q值
                next_q_values_target = self.target_network(next_states)
            else:
                # 标准DQN：直接使用目标网络
                next_q_values_target = self.target_network(next_states)
                next_actions = [torch.argmax(q, dim=1) for q in next_q_values_target]

            # 计算目标Q值
            target_q_values = []
            td_errors = []

            for i in range(len(current_q_values)):
                batch_targets = []
                batch_td_errors = []

                for j in range(self.batch_size):
                    if dones[j]:
                        target = rewards[j]
                    else:
                        if self.use_double_dqn:
                            next_q = next_q_values_target[i][j, next_actions[i][j]]
                        else:
                            next_q = torch.max(next_q_values_target[i][j])
                        target = rewards[j] + self.gamma * next_q

                    batch_targets.append(target)

                    # 计算TD误差用于更新优先级
                    current_q = current_q_values[i][j, actions[j, i]]
                    td_error = abs(current_q - target).item()
                    batch_td_errors.append(td_error)

                target_q_values.append(torch.FloatTensor(batch_targets))
                td_errors.append(batch_td_errors)

        # 计算加权损失 - 每个动作头分别计算
        total_loss = 0
        losses = []

        for i, (current_q, target_q) in enumerate(zip(current_q_values, target_q_values)):
            action_idx = actions[:, i]
            current_q_selected = current_q.gather(1, action_idx.unsqueeze(1)).squeeze()

            # 计算元素级损失
            element_loss = F.smooth_l1_loss(current_q_selected, target_q, reduction='none')

            # 应用重要性采样权重
            weighted_loss = (element_loss * weights).mean()

            total_loss += weighted_loss
            losses.append(weighted_loss.item())

        # 反向传播
        self.optimizer.zero_grad()
        total_loss.backward()

        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(self.q_network.parameters(), max_norm=1.0)

        self.optimizer.step()
        self.scheduler.step()

        # 更新优先级
        if self.use_prioritized_replay and indices is not None:
            # 使用第一个动作头的TD误差作为优先级
            self.memory.update_priorities(indices, td_errors[0])

        # 更新epsilon
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay

        # 记录训练统计
        self.training_step += 1
        self.loss_history.append(total_loss.item())

        return total_loss.item()

    def update_target_network(self, soft_update: bool = False, tau: float = 0.005):
        """更新目标网络"""
        if soft_update:
            # 软更新：目标网络参数缓慢向主网络靠近
            for target_param, main_param in zip(self.target_network.parameters(),
                                               self.q_network.parameters()):
                target_param.data.copy_(tau * main_param.data + (1.0 - tau) * target_param.data)
        else:
            # 硬更新：直接复制参数
            self.target_network.load_state_dict(self.q_network.state_dict())

    def save_checkpoint(self, filepath: str):
        """保存检查点"""
        checkpoint = {
            'q_network_state_dict': self.q_network.state_dict(),
            'target_network_state_dict': self.target_network.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'epsilon': self.epsilon,
            'training_step': self.training_step,
            'loss_history': self.loss_history,
            'config': {
                'state_dim': self.state_dim,
                'action_dims': self.action_dims,
                'gamma': self.gamma,
                'use_prioritized_replay': self.use_prioritized_replay,
                'use_double_dqn': self.use_double_dqn
            }
        }
        torch.save(checkpoint, filepath)

    def load_checkpoint(self, filepath: str):
        """加载检查点"""
        checkpoint = torch.load(filepath)
        self.q_network.load_state_dict(checkpoint['q_network_state_dict'])
        self.target_network.load_state_dict(checkpoint['target_network_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        self.epsilon = checkpoint['epsilon']
        self.training_step = checkpoint['training_step']
        self.loss_history = checkpoint['loss_history']

    def get_training_stats(self) -> Dict:
        """获取训练统计信息"""
        return {
            'training_step': self.training_step,
            'epsilon': self.epsilon,
            'memory_size': len(self.memory),
            'avg_loss_last_100': np.mean(self.loss_history[-100:]) if len(self.loss_history) >= 100 else 0,
            'current_lr': self.scheduler.get_last_lr()[0] if hasattr(self.scheduler, 'get_last_lr') else 0
        }


class DRLQoTTrainer:
    """
    DRL QoT优化训练器 - 增强版
    支持多种训练策略和高级功能
    """

    def __init__(self, network_file: str, equipment_file: str,
                 use_curriculum_learning: bool = True,
                 use_early_stopping: bool = True):
        self.env = QoTAwareOpticalEnvironment(network_file, equipment_file)
        self.agent = DQNAgent(
            state_dim=self.env.observation_space.shape[0],
            action_dims=self.env.action_space.nvec.tolist(),
            use_prioritized_replay=True,
            use_double_dqn=True
        )

        # 训练配置
        self.use_curriculum_learning = use_curriculum_learning
        self.use_early_stopping = use_early_stopping

        # 课程学习参数
        self.curriculum_stage = 0
        self.curriculum_thresholds = [0.3, 0.6, 0.8]  # 成功率阈值

        # 早停参数
        self.best_performance = -np.inf
        self.patience = 100
        self.patience_counter = 0

        self.training_history = {
            'episode_rewards': [],
            'episode_lengths': [],
            'success_rates': [],
            'average_osnr': [],
            'qot_violations': [],
            'loss_values': [],
            'epsilon_values': [],
            'curriculum_stages': [],
            'learning_rates': []
        }

    def train(self, episodes: int = 1000, save_interval: int = 100,
              target_update_freq: int = 10, validation_freq: int = 50):
        """训练DRL智能体 - 增强版训练循环"""
        print("🚀 开始DRL QoT优化训练...")
        print(f"📊 训练配置: {episodes}轮, 优先回放={self.agent.use_prioritized_replay}, "
              f"Double DQN={self.agent.use_double_dqn}")

        for episode in range(episodes):
            # 课程学习：调整环境难度
            if self.use_curriculum_learning:
                self._update_curriculum(episode)

            state = self.env.reset()
            total_reward = 0
            steps = 0
            successes = 0
            osnr_values = []
            qot_violations = 0
            episode_loss = 0
            replay_count = 0

            while True:
                # 选择动作
                action = self.agent.act(state, training=True)

                # 执行动作
                next_state, reward, done, info = self.env.step(action)

                # 存储经验
                self.agent.remember(state, action, reward, next_state, done)

                # 经验回放 (每步都进行)
                if len(self.agent.memory) >= self.agent.batch_size:
                    loss = self.agent.replay()
                    episode_loss += loss
                    replay_count += 1

                # 更新统计
                total_reward += reward
                steps += 1

                if info.get('success', False):
                    successes += 1
                    osnr_values.append(info['osnr_db'])

                if info.get('predicted_degradation', 0) > 2.0:
                    qot_violations += 1

                state = next_state

                if done:
                    break

            # 软更新目标网络
            if episode % target_update_freq == 0:
                self.agent.update_target_network(soft_update=True, tau=0.005)

            # 记录训练历史
            success_rate = successes / steps if steps > 0 else 0
            avg_loss = episode_loss / replay_count if replay_count > 0 else 0

            self.training_history['episode_rewards'].append(total_reward)
            self.training_history['episode_lengths'].append(steps)
            self.training_history['success_rates'].append(success_rate)
            self.training_history['average_osnr'].append(
                np.mean(osnr_values) if osnr_values else 0
            )
            self.training_history['qot_violations'].append(qot_violations)
            self.training_history['loss_values'].append(avg_loss)
            self.training_history['epsilon_values'].append(self.agent.epsilon)
            self.training_history['curriculum_stages'].append(self.curriculum_stage)

            # 获取当前学习率
            current_lr = self.agent.scheduler.get_last_lr()[0] if hasattr(self.agent.scheduler, 'get_last_lr') else 0
            self.training_history['learning_rates'].append(current_lr)

            # 验证和早停
            if episode % validation_freq == 0 and episode > 0:
                validation_score = self._validate_agent()
                if self.use_early_stopping:
                    if self._check_early_stopping(validation_score):
                        print(f"🛑 早停触发在第 {episode} 轮")
                        break

            # 打印进度
            if episode % 50 == 0:
                self._print_training_progress(episode)

            # 保存模型
            if episode % save_interval == 0 and episode > 0:
                self.save_model(f"drl_qot_model_episode_{episode}.pth")

        print("✅ 训练完成!")
        return self.training_history

    def _update_curriculum(self, episode: int):
        """更新课程学习阶段"""
        if len(self.training_history['success_rates']) < 50:
            return

        recent_success_rate = np.mean(self.training_history['success_rates'][-50:])

        # 检查是否可以进入下一阶段
        if (self.curriculum_stage < len(self.curriculum_thresholds) and
            recent_success_rate > self.curriculum_thresholds[self.curriculum_stage]):
            self.curriculum_stage += 1
            print(f"📈 课程学习进入阶段 {self.curriculum_stage}")

            # 调整环境难度
            if self.curriculum_stage == 1:
                self.env.reward_weights['qot_safety'] *= 1.5
            elif self.curriculum_stage == 2:
                self.env.reward_weights['efficiency'] *= 2.0
            elif self.curriculum_stage == 3:
                self.env.reward_weights['blocking_penalty'] *= 1.5

    def _validate_agent(self) -> float:
        """验证智能体性能"""
        validation_episodes = 10
        total_rewards = []
        success_rates = []

        # 临时关闭探索
        original_epsilon = self.agent.epsilon
        self.agent.epsilon = 0.0

        for _ in range(validation_episodes):
            state = self.env.reset()
            total_reward = 0
            successes = 0
            steps = 0

            while True:
                action = self.agent.act(state, training=False)
                next_state, reward, done, info = self.env.step(action)

                total_reward += reward
                steps += 1
                if info.get('success', False):
                    successes += 1

                state = next_state
                if done:
                    break

            total_rewards.append(total_reward)
            success_rates.append(successes / steps if steps > 0 else 0)

        # 恢复探索
        self.agent.epsilon = original_epsilon

        # 返回综合评分
        avg_reward = np.mean(total_rewards)
        avg_success = np.mean(success_rates)
        validation_score = avg_reward + avg_success * 10  # 加权评分

        return validation_score

    def _check_early_stopping(self, validation_score: float) -> bool:
        """检查早停条件"""
        if validation_score > self.best_performance:
            self.best_performance = validation_score
            self.patience_counter = 0
            return False
        else:
            self.patience_counter += 1
            return self.patience_counter >= self.patience

    def _print_training_progress(self, episode: int):
        """打印训练进度"""
        window = min(50, len(self.training_history['episode_rewards']))

        avg_reward = np.mean(self.training_history['episode_rewards'][-window:])
        avg_success = np.mean(self.training_history['success_rates'][-window:])
        avg_loss = np.mean(self.training_history['loss_values'][-window:])
        avg_violations = np.mean(self.training_history['qot_violations'][-window:])

        stats = self.agent.get_training_stats()

        print(f"Episode {episode:4d} | "
              f"Reward: {avg_reward:6.2f} | "
              f"Success: {avg_success:5.1%} | "
              f"Loss: {avg_loss:6.4f} | "
              f"Violations: {avg_violations:4.1f} | "
              f"ε: {stats['epsilon']:.3f} | "
              f"LR: {stats['current_lr']:.2e} | "
              f"Stage: {self.curriculum_stage}")

    def evaluate(self, episodes: int = 100) -> Dict:
        """评估训练好的智能体"""
        print(f"🧪 评估智能体性能 ({episodes} 轮)...")

        # 关闭探索
        original_epsilon = self.agent.epsilon
        self.agent.epsilon = 0.0

        results = {
            'episode_rewards': [],
            'success_rates': [],
            'average_osnr': [],
            'qot_violations': [],
            'blocking_rates': []
        }

        for episode in range(episodes):
            state = self.env.reset()
            total_reward = 0
            successes = 0
            blocks = 0
            osnr_values = []
            qot_violations = 0
            steps = 0

            while True:
                action = self.agent.act(state, training=False)
                next_state, reward, done, info = self.env.step(action)

                total_reward += reward
                steps += 1

                if info.get('success', False):
                    successes += 1
                    osnr_values.append(info['osnr_db'])
                else:
                    blocks += 1

                if info.get('predicted_degradation', 0) > 2.0:
                    qot_violations += 1

                state = next_state
                if done:
                    break

            results['episode_rewards'].append(total_reward)
            results['success_rates'].append(successes / steps if steps > 0 else 0)
            results['average_osnr'].append(np.mean(osnr_values) if osnr_values else 0)
            results['qot_violations'].append(qot_violations)
            results['blocking_rates'].append(blocks / steps if steps > 0 else 0)

        # 恢复探索
        self.agent.epsilon = original_epsilon

        # 计算统计指标
        evaluation_stats = {
            'avg_reward': np.mean(results['episode_rewards']),
            'std_reward': np.std(results['episode_rewards']),
            'avg_success_rate': np.mean(results['success_rates']),
            'avg_osnr': np.mean(results['average_osnr']),
            'avg_violations': np.mean(results['qot_violations']),
            'avg_blocking_rate': np.mean(results['blocking_rates'])
        }

        print("📊 评估结果:")
        print(f"   平均奖励: {evaluation_stats['avg_reward']:.2f} ± {evaluation_stats['std_reward']:.2f}")
        print(f"   平均成功率: {evaluation_stats['avg_success_rate']:.2%}")
        print(f"   平均OSNR: {evaluation_stats['avg_osnr']:.2f} dB")
        print(f"   平均违规: {evaluation_stats['avg_violations']:.1f} 次/轮")
        print(f"   平均阻塞率: {evaluation_stats['avg_blocking_rate']:.2%}")

        return evaluation_stats

    def save_model(self, filename: str, include_training_state: bool = True):
        """保存模型和训练状态"""
        save_dict = {
            'q_network_state_dict': self.agent.q_network.state_dict(),
            'target_network_state_dict': self.agent.target_network.state_dict(),
            'optimizer_state_dict': self.agent.optimizer.state_dict(),
            'scheduler_state_dict': self.agent.scheduler.state_dict(),
            'training_history': self.training_history,
            'agent_config': {
                'epsilon': self.agent.epsilon,
                'training_step': self.agent.training_step,
                'loss_history': self.agent.loss_history,
                'state_dim': self.agent.state_dim,
                'action_dims': self.agent.action_dims,
                'gamma': self.agent.gamma,
                'use_prioritized_replay': self.agent.use_prioritized_replay,
                'use_double_dqn': self.agent.use_double_dqn
            },
            'trainer_config': {
                'curriculum_stage': self.curriculum_stage,
                'best_performance': self.best_performance,
                'patience_counter': self.patience_counter,
                'use_curriculum_learning': self.use_curriculum_learning,
                'use_early_stopping': self.use_early_stopping
            }
        }

        if include_training_state and hasattr(self.agent, 'memory'):
            # 保存经验回放缓冲区（可选）
            if hasattr(self.agent.memory, 'buffer'):
                save_dict['memory_buffer'] = {
                    'buffer': self.agent.memory.buffer[-1000:],  # 只保存最近1000个经验
                    'priorities': self.agent.memory.priorities[-1000:] if hasattr(self.agent.memory, 'priorities') else None
                }

        torch.save(save_dict, filename)
        print(f"💾 模型已保存: {filename}")

    def load_model(self, filename: str, load_training_state: bool = True):
        """加载模型和训练状态"""
        checkpoint = torch.load(filename, map_location='cpu')

        # 加载网络参数
        self.agent.q_network.load_state_dict(checkpoint['q_network_state_dict'])
        self.agent.target_network.load_state_dict(checkpoint['target_network_state_dict'])
        self.agent.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])

        if 'scheduler_state_dict' in checkpoint:
            self.agent.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])

        # 加载训练历史
        self.training_history = checkpoint.get('training_history', {})

        if load_training_state and 'agent_config' in checkpoint:
            # 加载智能体状态
            agent_config = checkpoint['agent_config']
            self.agent.epsilon = agent_config.get('epsilon', 0.01)
            self.agent.training_step = agent_config.get('training_step', 0)
            self.agent.loss_history = agent_config.get('loss_history', [])

            # 加载训练器状态
            if 'trainer_config' in checkpoint:
                trainer_config = checkpoint['trainer_config']
                self.curriculum_stage = trainer_config.get('curriculum_stage', 0)
                self.best_performance = trainer_config.get('best_performance', -np.inf)
                self.patience_counter = trainer_config.get('patience_counter', 0)

        print(f"📂 模型已加载: {filename}")

        # 返回配置信息
        return checkpoint.get('agent_config', {})


def main():
    """主函数 - 演示DRL QoT优化系统"""

    # 配置文件路径（需要根据实际情况修改）
    network_file = "Data/CORONET_Global_Topology_expected.json"
    equipment_file = "Data/default_edfa_config.json"

    try:
        # 创建训练器
        trainer = DRLQoTTrainer(network_file, equipment_file)

        # 开始训练
        history = trainer.train(episodes=500, save_interval=100)

        # 保存最终模型
        trainer.save_model("drl_qot_final_model.pth")

        # 保存训练历史
        with open("drl_training_history.json", "w") as f:
            json.dump(history, f, indent=2)

        print("🎉 DRL QoT优化系统训练完成!")
        print(f"📊 最终成功率: {np.mean(history['success_rates'][-50:]):.2%}")
        print(f"📈 平均奖励: {np.mean(history['episode_rewards'][-50:]):.2f}")

    except Exception as e:
        print(f"❌ 训练过程中出现错误: {e}")
        print("请检查网络配置文件路径和依赖项是否正确安装")


if __name__ == "__main__":
    main()
