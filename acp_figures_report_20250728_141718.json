{"conference": "ACP (Academic Conference Publication)", "generation_time": "2025-07-28T14:17:24.561965", "total_figures": 8, "figures": {"dynamic_scoring_comparison": "acp_dynamic_scoring_comparison_20250728_141718.png", "subgraph_effect_comparison": "acp_subgraph_effect_comparison_20250728_141718.png", "four_method_comparison": "acp_four_method_comparison_20250728_141718.png", "detailed_ablation_study": "acp_detailed_ablation_study_20250728_141718.png", "performance_radar_chart": "acp_performance_radar_chart_20250728_141718.png", "scalability_comparison": "acp_scalability_comparison_20250728_141718.png", "training_efficiency": "acp_training_efficiency_20250728_141718.png", "realtime_benchmark": "acp_realtime_benchmark_20250728_141718.png"}, "figure_descriptions": {"dynamic_scoring_comparison": "Comparison of methods with and without dynamic scoring mechanism", "subgraph_effect_comparison": "Effect of subgraph strategy on accuracy and computational efficiency", "four_method_comparison": "Comprehensive comparison of all four method combinations", "detailed_ablation_study": "Detailed ablation study showing component contributions", "performance_radar_chart": "Multi-dimensional performance comparison using radar charts", "scalability_comparison": "Scalability analysis across different network sizes", "training_efficiency": "Training convergence and stability comparison", "realtime_benchmark": "Real-time performance benchmark under concurrent loads"}, "key_findings": {"dynamic_scoring_improvement": "2.5-3.2% accuracy improvement with dynamic scoring", "subgraph_speedup": "3-6x speedup with subgraph strategy", "memory_reduction": "40-60% memory usage reduction", "scalability_advantage": "Better scalability with increasing network size", "training_efficiency": "20-30% faster convergence", "realtime_performance": "2-3x higher throughput under concurrent load"}}