#!/usr/bin/env python3
"""
完整的子图QoT估计系统
整合所有组件，提供完整的端到端解决方案

系统架构：
1. 智能子图GAT模型 - 核心预测引擎
2. 动态光路影响检测器 - 智能识别受影响光路
3. 优化QoT更新器 - 高效批量更新QoT值
4. 全面对比测试系统 - 与基线方法对比验证

使用场景：
- 光网络QoT实时预测
- 动态光路建立/拆除的影响分析
- 全网QoT状态维护和更新
- 网络规划和优化决策支持
"""

import torch
import numpy as np
import time
import json
from typing import Dict, List, Tuple, Optional
from datetime import datetime
import dgl
import warnings
warnings.filterwarnings('ignore')

# 导入我们实现的所有组件
from intelligent_subgraph_qot_system import (
    IntelligentSubgraphGAT, 
    IntelligentQoTDataGenerator,
    train_intelligent_subgraph_system
)
from dynamic_lightpath_impact_detector import (
    DynamicImpactDetector, 
    LightpathInfo, 
    ImpactAnalysisResult
)
from optimized_qot_updater import (
    OptimizedQoTUpdater,
    QoTUpdateTask
)
from comprehensive_comparison_system import (
    ComprehensiveComparison,
    run_comprehensive_comparison_test
)

class CompleteSubgraphQoTSystem:
    """完整的子图QoT估计系统"""
    
    def __init__(self, network_topology_file=None, pretrained_model_path=None, 
                 config_file=None):
        """
        初始化完整系统
        
        Args:
            network_topology_file: 网络拓扑文件
            pretrained_model_path: 预训练模型路径
            config_file: 配置文件路径
        """
        print("🚀 初始化完整子图QoT估计系统")
        print("=" * 60)
        
        # 加载配置
        self.config = self._load_configuration(config_file)
        
        # 创建网络拓扑
        self.network_graph = self._create_network_topology(network_topology_file)
        
        # 初始化核心组件
        self._initialize_components(pretrained_model_path)
        
        # 系统状态
        self.system_stats = {
            'total_predictions': 0,
            'successful_updates': 0,
            'cache_hits': 0,
            'avg_response_time': 0.0,
            'system_start_time': time.time()
        }
        
        print("✅ 系统初始化完成")
        self._print_system_info()
    
    def _load_configuration(self, config_file) -> Dict:
        """加载系统配置"""
        default_config = {
            'network': {
                'num_nodes': 14,
                'node_feature_dim': 10,
                'max_wavelengths': 80
            },
            'model': {
                'hidden_dim': 128,
                'num_layers': 3,
                'num_heads': 8,
                'dropout': 0.15,
                'subgraph_size_range': [6, 15]
            },
            'system': {
                'batch_update_size': 16,
                'cache_ttl': 300,
                'max_workers': 4,
                'update_interval': 0.1
            },
            'thresholds': {
                'high_impact': 0.7,
                'medium_impact': 0.4,
                'low_impact': 0.1,
                'confidence_min': 0.6
            }
        }
        
        if config_file:
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                # 合并配置
                default_config.update(user_config)
            except Exception as e:
                print(f"⚠️ 无法加载配置文件 {config_file}: {e}")
                print("使用默认配置")
        
        return default_config
    
    def _create_network_topology(self, topology_file) -> dgl.DGLGraph:
        """创建网络拓扑"""
        if topology_file:
            try:
                # 尝试从文件加载拓扑
                with open(topology_file, 'r', encoding='utf-8') as f:
                    topo_data = json.load(f)
                
                u = torch.tensor(topo_data['edges']['src'])
                v = torch.tensor(topo_data['edges']['dst'])
                
                graph = dgl.graph((u, v))
                graph = dgl.to_bidirected(graph)
                
                print(f"📂 从文件加载网络拓扑: {topology_file}")
                
            except Exception as e:
                print(f"⚠️ 无法加载拓扑文件: {e}")
                print("使用默认日本网络拓扑")
                graph = self._create_default_topology()
        else:
            graph = self._create_default_topology()
        
        return graph
    
    def _create_default_topology(self) -> dgl.DGLGraph:
        """创建默认的日本网络拓扑"""
        u = torch.tensor([0,0,1,2,3,3,4,4,4,5,6,6,7,8,11,9,8,13,11,11,13,10])
        v = torch.tensor([1,2,3,4,4,5,5,8,6,7,7,11,9,11,9,10,13,11,10,12,12,12])
        
        graph = dgl.graph((u, v))
        graph = dgl.to_bidirected(graph)
        
        print("🌐 使用默认日本网络拓扑")
        
        return graph
    
    def _initialize_components(self, pretrained_model_path):
        """初始化系统组件"""
        print("🔧 初始化系统组件...")
        
        # 1. 动态影响检测器
        self.impact_detector = DynamicImpactDetector(
            self.network_graph, 
            intelligent_model_path=pretrained_model_path
        )
        
        # 2. QoT更新器
        self.qot_updater = OptimizedQoTUpdater(
            self.network_graph,
            self.impact_detector,
            intelligent_model_path=pretrained_model_path,
            max_workers=self.config['system']['max_workers']
        )
        
        # 3. 智能模型（如果需要单独使用）
        if pretrained_model_path:
            try:
                self.intelligent_model = IntelligentSubgraphGAT(
                    node_feature_dim=self.config['network']['node_feature_dim'],
                    hidden_dim=self.config['model']['hidden_dim'],
                    num_layers=self.config['model']['num_layers'],
                    num_heads=self.config['model']['num_heads'],
                    dropout=self.config['model']['dropout']
                )
                
                device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
                self.intelligent_model.load_state_dict(torch.load(pretrained_model_path, map_location=device))
                self.intelligent_model.to(device)
                self.intelligent_model.eval()
                
                print(f"✅ 加载预训练智能模型: {pretrained_model_path}")
                
            except Exception as e:
                print(f"⚠️ 无法加载预训练模型: {e}")
                self.intelligent_model = None
        else:
            self.intelligent_model = None
        
        print("✅ 组件初始化完成")
    
    def _print_system_info(self):
        """打印系统信息"""
        print(f"\n📊 系统信息:")
        print(f"   网络节点数: {self.network_graph.num_nodes()}")
        print(f"   网络边数: {self.network_graph.num_edges()}")
        print(f"   模型隐藏维度: {self.config['model']['hidden_dim']}")
        print(f"   最大工作线程: {self.config['system']['max_workers']}")
        print(f"   缓存TTL: {self.config['system']['cache_ttl']}秒")
        print(f"   设备: {'CUDA' if torch.cuda.is_available() else 'CPU'}")
    
    def add_lightpath(self, lightpath_request: Dict) -> Dict:
        """
        添加新光路并进行全面分析
        
        Args:
            lightpath_request: 光路请求
                {
                    'id': int,
                    'source': int,
                    'destination': int,
                    'wavelength': int,
                    'power_dbm': float,
                    'modulation': str,
                    'bitrate_gbps': float,
                    'priority': int
                }
        
        Returns:
            完整的分析结果
        """
        start_time = time.time()
        
        print(f"🔍 处理光路添加请求: LP-{lightpath_request['id']}")
        print(f"   路径: {lightpath_request['source']} → {lightpath_request['destination']}")
        
        try:
            # 1. 创建光路信息
            lightpath = LightpathInfo(
                id=lightpath_request['id'],
                source=lightpath_request['source'],
                destination=lightpath_request['destination'],
                path=self._calculate_shortest_path(
                    lightpath_request['source'], 
                    lightpath_request['destination']
                ),
                wavelength=lightpath_request['wavelength'],
                power_dbm=lightpath_request['power_dbm'],
                modulation=lightpath_request.get('modulation', '16QAM'),
                bitrate_gbps=lightpath_request.get('bitrate_gbps', 100),
                current_qot=0.0,  # 将被更新
                establishment_time=time.time(),
                priority=lightpath_request.get('priority', 1)
            )
            
            # 2. 影响分析
            impact_analysis = self.impact_detector.add_lightpath(lightpath)
            
            # 3. 调度QoT更新
            affected_updates = []
            for affected_result in impact_analysis['affected_lightpaths']:
                # 调度更新
                self.qot_updater.schedule_update(
                    affected_result.lightpath_id,
                    affected_result,
                    update_type='incremental'
                )
                affected_updates.append({
                    'lightpath_id': affected_result.lightpath_id,
                    'impact_probability': affected_result.impact_probability,
                    'estimated_qot_change': affected_result.estimated_qot_change,
                    'confidence': affected_result.confidence_score
                })
            
            # 4. 立即更新新光路的QoT
            new_qot_result = self.qot_updater.execute_immediate_update(lightpath.id)
            
            # 5. 更新系统统计
            response_time = time.time() - start_time
            self._update_system_stats(response_time, len(affected_updates))
            
            result = {
                'success': True,
                'lightpath_id': lightpath.id,
                'lightpath_path': lightpath.path,
                'new_lightpath_qot': new_qot_result,
                'impact_analysis': {
                    'total_affected': impact_analysis['total_affected'],
                    'affected_lightpaths': affected_updates
                },
                'response_time': response_time,
                'timestamp': datetime.now().isoformat()
            }
            
            print(f"✅ 光路添加完成: {impact_analysis['total_affected']} 条光路受影响")
            print(f"   响应时间: {response_time:.3f}s")
            
            return result
            
        except Exception as e:
            error_result = {
                'success': False,
                'error': str(e),
                'lightpath_id': lightpath_request['id'],
                'response_time': time.time() - start_time,
                'timestamp': datetime.now().isoformat()
            }
            
            print(f"❌ 光路添加失败: {e}")
            return error_result
    
    def remove_lightpath(self, lightpath_id: int) -> Dict:
        """
        移除光路并分析影响
        
        Args:
            lightpath_id: 光路ID
            
        Returns:
            移除分析结果
        """
        start_time = time.time()
        
        print(f"🗑️ 处理光路移除请求: LP-{lightpath_id}")
        
        try:
            # 1. 影响分析
            removal_analysis = self.impact_detector.remove_lightpath(lightpath_id)
            
            if 'error' in removal_analysis:
                return {
                    'success': False,
                    'error': removal_analysis['error'],
                    'response_time': time.time() - start_time
                }
            
            # 2. 使受影响光路的缓存失效
            improved_lightpath_ids = [
                result['lightpath_id'] 
                for result in removal_analysis['improved_lightpaths']
            ]
            
            self.qot_updater.cache.invalidate(improved_lightpath_ids)
            
            # 3. 批量更新改善的光路
            if improved_lightpath_ids:
                batch_results = self.qot_updater.execute_batch_update(improved_lightpath_ids)
            else:
                batch_results = []
            
            # 4. 更新系统统计
            response_time = time.time() - start_time
            self._update_system_stats(response_time, len(improved_lightpath_ids))
            
            result = {
                'success': True,
                'removed_lightpath_id': lightpath_id,
                'improvement_analysis': {
                    'total_improved': removal_analysis['total_improved'],
                    'improved_lightpaths': removal_analysis['improved_lightpaths']
                },
                'batch_update_results': batch_results,
                'response_time': response_time,
                'timestamp': datetime.now().isoformat()
            }
            
            print(f"✅ 光路移除完成: {removal_analysis['total_improved']} 条光路QoT改善")
            print(f"   响应时间: {response_time:.3f}s")
            
            return result
            
        except Exception as e:
            error_result = {
                'success': False,
                'error': str(e),
                'lightpath_id': lightpath_id,
                'response_time': time.time() - start_time,
                'timestamp': datetime.now().isoformat()
            }
            
            print(f"❌ 光路移除失败: {e}")
            return error_result
    
    def predict_lightpath_impact(self, new_lightpath_request: Dict, 
                                target_lightpath_id: int) -> Dict:
        """
        预测新光路对指定光路的影响（不实际添加光路）
        
        Args:
            new_lightpath_request: 新光路请求
            target_lightpath_id: 目标光路ID
            
        Returns:
            影响预测结果
        """
        start_time = time.time()
        
        print(f"🔮 预测光路影响: 新光路 → LP-{target_lightpath_id}")
        
        try:
            if target_lightpath_id not in self.impact_detector.active_lightpaths:
                return {
                    'success': False,
                    'error': f'目标光路 LP-{target_lightpath_id} 不存在'
                }
            
            target_lightpath = self.impact_detector.active_lightpaths[target_lightpath_id]
            
            # 使用智能模型进行预测
            if self.intelligent_model:
                device = next(self.intelligent_model.parameters()).device
                
                # 构建节点特征
                node_features = self._build_prediction_features()
                node_features_tensor = torch.tensor(node_features, dtype=torch.float32).to(device)
                
                # 新光路和目标光路节点
                new_lightpath_nodes = [new_lightpath_request['source'], new_lightpath_request['destination']]
                target_lightpath_nodes = [target_lightpath.source, target_lightpath.destination]
                
                with torch.no_grad():
                    impact_logits, qot_prediction, subgraph_info = self.intelligent_model(
                        self.network_graph.to(device), node_features_tensor,
                        new_lightpath_nodes, target_lightpath_nodes,
                        return_subgraph_info=True
                    )
                    
                    impact_prob = torch.softmax(impact_logits, dim=1)[0, 1].item()
                    qot_change = qot_prediction.item()
                
                result = {
                    'success': True,
                    'new_lightpath': new_lightpath_request,
                    'target_lightpath_id': target_lightpath_id,
                    'prediction': {
                        'impact_probability': impact_prob,
                        'estimated_qot_change': qot_change,
                        'impact_classification': 'High' if impact_prob > 0.7 else 'Medium' if impact_prob > 0.4 else 'Low',
                        'subgraph_size': subgraph_info['subgraph_size'],
                        'selected_nodes': subgraph_info['selected_nodes']
                    },
                    'response_time': time.time() - start_time,
                    'timestamp': datetime.now().isoformat()
                }
                
                print(f"✅ 影响预测完成: 影响概率={impact_prob:.3f}, QoT变化={qot_change:.3f}dB")
                
            else:
                # 使用简化的预测方法
                path_overlap = self._calculate_path_overlap(
                    self._calculate_shortest_path(
                        new_lightpath_request['source'], 
                        new_lightpath_request['destination']
                    ),
                    target_lightpath.path
                )
                
                wavelength_proximity = 1.0 / (1.0 + abs(
                    new_lightpath_request['wavelength'] - target_lightpath.wavelength
                ) * 0.1)
                
                estimated_impact = path_overlap * wavelength_proximity
                
                result = {
                    'success': True,
                    'new_lightpath': new_lightpath_request,
                    'target_lightpath_id': target_lightpath_id,
                    'prediction': {
                        'impact_probability': estimated_impact,
                        'estimated_qot_change': estimated_impact * 2.0,  # 简化估计
                        'impact_classification': 'High' if estimated_impact > 0.7 else 'Medium' if estimated_impact > 0.4 else 'Low',
                        'method': 'simplified'
                    },
                    'response_time': time.time() - start_time,
                    'timestamp': datetime.now().isoformat()
                }
                
                print(f"✅ 简化影响预测完成: 影响概率={estimated_impact:.3f}")
            
            return result
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'response_time': time.time() - start_time,
                'timestamp': datetime.now().isoformat()
            }
    
    def get_system_status(self) -> Dict:
        """获取系统状态"""
        
        # 获取各组件状态
        network_stats = self.impact_detector.get_network_statistics()
        updater_stats = self.qot_updater.get_update_statistics()
        
        # 计算系统运行时间
        uptime = time.time() - self.system_stats['system_start_time']
        
        status = {
            'system_info': {
                'uptime_seconds': uptime,
                'uptime_formatted': self._format_duration(uptime),
                'network_nodes': self.network_graph.num_nodes(),
                'network_edges': self.network_graph.num_edges(),
                'intelligent_model_loaded': self.intelligent_model is not None
            },
            'performance_stats': self.system_stats.copy(),
            'network_status': network_stats,
            'updater_status': updater_stats,
            'active_lightpaths': list(self.impact_detector.active_lightpaths.keys()),
            'timestamp': datetime.now().isoformat()
        }
        
        return status
    
    def run_system_benchmark(self, num_operations=100) -> Dict:
        """运行系统性能基准测试"""
        print(f"⚡ 运行系统性能基准测试 ({num_operations} 次操作)")
        
        benchmark_start = time.time()
        
        # 生成测试光路请求
        test_requests = []
        for i in range(num_operations):
            nodes = list(range(self.network_graph.num_nodes()))
            src = np.random.choice(nodes)
            dst = np.random.choice([n for n in nodes if n != src])
            
            request = {
                'id': 1000 + i,
                'source': src,
                'destination': dst,
                'wavelength': np.random.randint(1, 81),
                'power_dbm': np.random.uniform(-2, 2),
                'modulation': '16QAM',
                'bitrate_gbps': 100,
                'priority': 1
            }
            test_requests.append(request)
        
        # 执行基准测试
        add_times = []
        predict_times = []
        remove_times = []
        
        added_lightpaths = []
        
        # 添加光路测试
        for i, request in enumerate(test_requests[:num_operations//2]):
            start_time = time.time()
            result = self.add_lightpath(request)
            add_time = time.time() - start_time
            
            add_times.append(add_time)
            if result['success']:
                added_lightpaths.append(request['id'])
        
        # 预测测试
        for i in range(min(20, len(added_lightpaths))):
            if i < len(test_requests) - 1 and added_lightpaths:
                start_time = time.time()
                self.predict_lightpath_impact(
                    test_requests[num_operations//2 + i], 
                    added_lightpaths[i % len(added_lightpaths)]
                )
                predict_time = time.time() - start_time
                predict_times.append(predict_time)
        
        # 移除光路测试
        for lp_id in added_lightpaths[:10]:
            start_time = time.time()
            self.remove_lightpath(lp_id)
            remove_time = time.time() - start_time
            remove_times.append(remove_time)
        
        total_benchmark_time = time.time() - benchmark_start
        
        # 计算统计信息
        benchmark_results = {
            'total_operations': num_operations,
            'total_time': total_benchmark_time,
            'operations_per_second': num_operations / total_benchmark_time,
            'add_lightpath': {
                'count': len(add_times),
                'avg_time': np.mean(add_times) if add_times else 0,
                'min_time': np.min(add_times) if add_times else 0,
                'max_time': np.max(add_times) if add_times else 0,
                'std_time': np.std(add_times) if add_times else 0
            },
            'predict_impact': {
                'count': len(predict_times),
                'avg_time': np.mean(predict_times) if predict_times else 0,
                'min_time': np.min(predict_times) if predict_times else 0,
                'max_time': np.max(predict_times) if predict_times else 0,
                'std_time': np.std(predict_times) if predict_times else 0
            },
            'remove_lightpath': {
                'count': len(remove_times),
                'avg_time': np.mean(remove_times) if remove_times else 0,
                'min_time': np.min(remove_times) if remove_times else 0,
                'max_time': np.max(remove_times) if remove_times else 0,
                'std_time': np.std(remove_times) if remove_times else 0
            },
            'timestamp': datetime.now().isoformat()
        }
        
        print(f"✅ 基准测试完成:")
        print(f"   总操作数: {num_operations}")
        print(f"   总耗时: {total_benchmark_time:.2f}s")
        print(f"   吞吐量: {benchmark_results['operations_per_second']:.2f} ops/s")
        print(f"   平均添加时间: {benchmark_results['add_lightpath']['avg_time']*1000:.2f}ms")
        print(f"   平均预测时间: {benchmark_results['predict_impact']['avg_time']*1000:.2f}ms")
        
        return benchmark_results
    
    def _calculate_shortest_path(self, source: int, destination: int) -> List[int]:
        """计算最短路径"""
        try:
            import networkx as nx
            nx_graph = dgl.to_networkx(self.network_graph).to_undirected()
            path = nx.shortest_path(nx_graph, source, destination)
            return path
        except:
            # 如果无法计算，返回直连路径
            return [source, destination]
    
    def _calculate_path_overlap(self, path1: List[int], path2: List[int]) -> float:
        """计算路径重叠比例"""
        if not path1 or not path2:
            return 0.0
        
        edges1 = set()
        edges2 = set()
        
        for i in range(len(path1) - 1):
            edges1.add(tuple(sorted([path1[i], path1[i+1]])))
        
        for i in range(len(path2) - 1):
            edges2.add(tuple(sorted([path2[i], path2[i+1]])))
        
        if not edges2:
            return 0.0
        
        overlap = len(edges1.intersection(edges2))
        return overlap / len(edges2.union(edges1))
    
    def _build_prediction_features(self) -> List[List[float]]:
        """构建预测用的节点特征"""
        num_nodes = self.network_graph.num_nodes()
        features = []
        
        for node_id in range(num_nodes):
            node_features = [
                float(self.network_graph.in_degrees()[node_id].item()),  # 度数
                0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0  # 其他特征（简化）
            ]
            features.append(node_features)
        
        return features
    
    def _update_system_stats(self, response_time: float, affected_count: int):
        """更新系统统计"""
        self.system_stats['total_predictions'] += 1
        self.system_stats['successful_updates'] += affected_count
        
        # 更新平均响应时间
        total_ops = self.system_stats['total_predictions']
        current_avg = self.system_stats['avg_response_time']
        self.system_stats['avg_response_time'] = (
            (current_avg * (total_ops - 1) + response_time) / total_ops
        )
    
    def _format_duration(self, seconds: float) -> str:
        """格式化持续时间"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        
        return f"{hours:02d}:{minutes:02d}:{secs:02d}"
    
    def shutdown(self):
        """优雅关闭系统"""
        print("🛑 关闭完整子图QoT系统...")
        
        try:
            # 关闭QoT更新器
            self.qot_updater.shutdown()
            
            # 保存系统状态
            final_status = self.get_system_status()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            with open(f'system_final_status_{timestamp}.json', 'w', encoding='utf-8') as f:
                json.dump(final_status, f, indent=2, ensure_ascii=False, default=str)
            
            print(f"💾 系统状态已保存: system_final_status_{timestamp}.json")
            
        except Exception as e:
            print(f"⚠️ 关闭过程中出现警告: {e}")
        
        print("✅ 系统已安全关闭")

def demo_complete_system():
    """演示完整系统使用"""
    print("🎭 完整子图QoT系统演示")
    print("=" * 80)
    
    # 1. 初始化系统
    system = CompleteSubgraphQoTSystem()
    
    try:
        # 2. 添加几条测试光路
        print(f"\n📡 添加测试光路...")
        
        test_lightpaths = [
            {
                'id': 1001,
                'source': 0,
                'destination': 5,
                'wavelength': 40,
                'power_dbm': 0.0,
                'modulation': '16QAM',
                'bitrate_gbps': 100,
                'priority': 1
            },
            {
                'id': 1002,
                'source': 2,
                'destination': 8,
                'wavelength': 42,
                'power_dbm': 1.0,
                'modulation': '16QAM',
                'bitrate_gbps': 150,
                'priority': 2
            },
            {
                'id': 1003,
                'source': 6,
                'destination': 11,
                'wavelength': 38,
                'power_dbm': -0.5,
                'modulation': 'QPSK',
                'bitrate_gbps': 50,
                'priority': 1
            }
        ]
        
        add_results = []
        for lp_request in test_lightpaths:
            result = system.add_lightpath(lp_request)
            add_results.append(result)
            time.sleep(0.5)  # 短暂延迟观察系统行为
        
        # 3. 测试影响预测
        print(f"\n🔮 测试影响预测...")
        
        prediction_request = {
            'id': 1004,
            'source': 1,
            'destination': 7,
            'wavelength': 41,
            'power_dbm': 0.5,
            'modulation': '16QAM',
            'bitrate_gbps': 100
        }
        
        prediction_result = system.predict_lightpath_impact(prediction_request, 1001)
        print(f"预测结果: {prediction_result['prediction']}")
        
        # 4. 获取系统状态
        print(f"\n📊 系统状态...")
        status = system.get_system_status()
        print(f"活跃光路: {len(status['active_lightpaths'])}")
        print(f"总预测次数: {status['performance_stats']['total_predictions']}")
        print(f"平均响应时间: {status['performance_stats']['avg_response_time']*1000:.2f}ms")
        
        # 5. 运行性能基准测试
        print(f"\n⚡ 性能基准测试...")
        benchmark_results = system.run_system_benchmark(num_operations=20)
        
        # 6. 移除一条光路
        print(f"\n🗑️ 移除光路测试...")
        remove_result = system.remove_lightpath(1002)
        print(f"移除结果: 成功={remove_result['success']}")
        
        # 7. 最终状态
        final_status = system.get_system_status()
        print(f"\n📈 最终统计:")
        print(f"   运行时间: {final_status['system_info']['uptime_formatted']}")
        print(f"   总操作数: {final_status['performance_stats']['total_predictions']}")
        print(f"   缓存命中率: {final_status['updater_status']['cache_stats']['hit_rate']:.3f}")
        print(f"   活跃光路数: {len(final_status['active_lightpaths'])}")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 8. 安全关闭系统
        system.shutdown()
    
    print(f"\n🎉 系统演示完成!")

if __name__ == "__main__":
    demo_complete_system()